#!/usr/bin/env python3
"""
Monitor the automated video processing progress
"""

import time
import json
from pathlib import Path

def monitor_processing():
    """Monitor processing progress"""
    
    target_dir = Path("/Users/<USER>/Desktop/processed videos for training")
    
    print("📊 Monitoring Video Processing Progress")
    print("=" * 45)
    
    start_time = time.time()
    last_count = 0
    
    while True:
        try:
            # Count processed videos
            if target_dir.exists():
                processed_videos = list(target_dir.glob("*_mouth_cropped.webm"))
                current_count = len(processed_videos)
                
                # Calculate progress
                total_expected = 2080  # From the filtering results
                progress_percent = (current_count / total_expected) * 100
                
                # Calculate processing rate
                elapsed_time = time.time() - start_time
                if elapsed_time > 0:
                    videos_per_minute = (current_count / elapsed_time) * 60
                    
                    # Estimate remaining time
                    remaining_videos = total_expected - current_count
                    if videos_per_minute > 0:
                        remaining_minutes = remaining_videos / videos_per_minute
                        remaining_hours = remaining_minutes / 60
                    else:
                        remaining_minutes = 0
                        remaining_hours = 0
                else:
                    videos_per_minute = 0
                    remaining_minutes = 0
                    remaining_hours = 0
                
                # Show progress
                print(f"\r📊 Progress: {current_count:,}/{total_expected:,} videos ({progress_percent:.1f}%) | "
                      f"Rate: {videos_per_minute:.1f} videos/min | "
                      f"ETA: {remaining_hours:.1f}h {remaining_minutes%60:.0f}m", end="", flush=True)
                
                # Show detailed update every 100 videos
                if current_count > 0 and current_count % 100 == 0 and current_count != last_count:
                    print(f"\n✅ Milestone: {current_count:,} videos processed")
                    
                    # Check for processing report
                    report_path = target_dir / "processing_report.json"
                    if report_path.exists():
                        try:
                            with open(report_path, 'r') as f:
                                report = json.load(f)
                            
                            success_rate = report.get('processing_summary', {}).get('success_rate', 0)
                            print(f"📈 Current success rate: {success_rate:.1f}%")
                        except:
                            pass
                
                last_count = current_count
                
                # Check if processing is complete
                if current_count >= total_expected:
                    print(f"\n🎉 Processing Complete!")
                    print(f"✅ {current_count:,} videos processed")
                    print(f"⏱️  Total time: {elapsed_time/3600:.1f} hours")
                    break
            
            else:
                print(f"\r⏳ Waiting for processing to start...", end="", flush=True)
            
            time.sleep(30)  # Check every 30 seconds
            
        except KeyboardInterrupt:
            print(f"\n⏹️  Monitoring stopped by user")
            break
        except Exception as e:
            print(f"\n❌ Monitoring error: {e}")
            time.sleep(60)  # Wait longer on error

def show_final_summary():
    """Show final processing summary"""
    
    target_dir = Path("/Users/<USER>/Desktop/processed videos for training")
    
    print(f"\n📋 Final Processing Summary")
    print("=" * 30)
    
    if target_dir.exists():
        # Count processed videos
        processed_videos = list(target_dir.glob("*_mouth_cropped.webm"))
        print(f"📊 Total processed videos: {len(processed_videos):,}")
        
        # Check for manifest
        manifest_path = target_dir / "processed_training_manifest.csv"
        if manifest_path.exists():
            print(f"✅ Training manifest created: {manifest_path}")
        
        # Check for report
        report_path = target_dir / "processing_report.json"
        if report_path.exists():
            print(f"✅ Processing report created: {report_path}")
            
            try:
                with open(report_path, 'r') as f:
                    report = json.load(f)
                
                summary = report.get('processing_summary', {})
                file_metrics = report.get('file_size_metrics', {})
                
                print(f"\n📈 Key Metrics:")
                print(f"   Success rate: {summary.get('success_rate', 0):.1f}%")
                print(f"   Average size reduction: {file_metrics.get('average_size_reduction_factor', 0):.1f}x")
                print(f"   Space saved: {file_metrics.get('total_space_saved_mb', 0):.1f} MB")
                
            except Exception as e:
                print(f"⚠️  Could not read report details: {e}")
    
    else:
        print(f"❌ Target directory not found: {target_dir}")

if __name__ == '__main__':
    try:
        monitor_processing()
        show_final_summary()
    except KeyboardInterrupt:
        print(f"\n👋 Monitoring stopped")
        show_final_summary()
