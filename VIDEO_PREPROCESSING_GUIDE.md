# Video Pre-processing Pipeline for ICU Lipreading

## Overview

This document describes the enhanced video pre-processing pipeline for the ICU lipreading project. The pipeline has been completely redesigned to handle WebM format videos, provide robust error handling, and ensure compatibility with LipNet specifications.

## Key Features

### ✅ Completed Enhancements

1. **WebM Format Support**
   - Full WebM video format support via `imageio` and `imageio-ffmpeg`
   - Automatic fallback from OpenCV to imageio for unsupported formats
   - Robust video reading with multiple codec support

2. **Quality Validation**
   - Frame count validation
   - Resolution validation
   - Brightness and contrast analysis
   - Blank frame detection
   - Comprehensive quality metrics

3. **Data Augmentation**
   - Brightness adjustment
   - Contrast enhancement
   - Noise addition
   - Configurable augmentation types

4. **Duplicate Management**
   - Automatic duplicate detection and cleanup
   - Hash-based file comparison
   - Smart duplicate removal (keeps newest)

5. **Progress Tracking**
   - Comprehensive manifest logging
   - Processing statistics
   - Error tracking and reporting

6. **LipNet Compatibility**
   - Exact frame count normalization (75 frames)
   - FPS standardization (25 FPS)
   - Resolution normalization (140x46 pixels)
   - Grayscale conversion

## File Structure

```
enhanced_video_preprocessor.py  # Main enhanced preprocessor
auto_train_26_phrases.py       # Original 26-phrase automation script
preprocess_videos.py           # Original 5-phrase preprocessor
VIDEO_PREPROCESSING_GUIDE.md   # This documentation
requirements.txt               # Updated with WebM dependencies
```

## Dependencies

### New Dependencies Added
- `imageio>=2.37.0` - For WebM and advanced video format support
- `imageio-ffmpeg>=0.6.0` - FFmpeg backend for imageio

### Installation
```bash
source venv/bin/activate
pip install imageio imageio-ffmpeg
```

## Usage

### Enhanced Video Preprocessor

The new `enhanced_video_preprocessor.py` provides multiple operation modes:

#### 1. Process Videos
```bash
python enhanced_video_preprocessor.py --input_dir /path/to/videos --mode process
```

Options:
- `--phrase_set {5,26}` - Choose phrase set (default: 26)
- `--demographic TAG` - Add demographic tag
- `--augment` - Apply data augmentation
- `--verbose` - Enable detailed logging

#### 2. Cleanup Duplicates
```bash
python enhanced_video_preprocessor.py --input_dir data --mode cleanup
```

#### 3. Validate Processed Videos
```bash
python enhanced_video_preprocessor.py --input_dir data --mode validate
```

#### 4. Generate Report
```bash
python enhanced_video_preprocessor.py --input_dir data --mode report
```

### Original Scripts (Legacy)

#### Auto Train 26 Phrases
```bash
python auto_train_26_phrases.py --mode preprocess --input "~/Desktop/icu-26-phrases"
```

#### Simple Preprocessor (5 phrases)
```bash
python preprocess_videos.py --input_dir /path/to/videos --process_type crop
```

## Video Format Support

### Supported Input Formats
- **MP4** (.mp4, .MP4)
- **MOV** (.mov, .MOV) 
- **AVI** (.avi, .AVI)
- **MKV** (.mkv, .MKV)
- **WebM** (.webm, .WEBM) ✅ **NEW**
- **M4V** (.m4v, .M4V)

### Output Format
- **MP4** with H.264 codec
- **25 FPS** (LipNet standard)
- **75 frames** per video (LipNet standard)
- **140x46 resolution** (LipNet standard)
- **Grayscale** format

## Processing Pipeline

### 1. Video Discovery
- Recursive search for supported video files
- Automatic phrase matching via filename keywords
- Duplicate detection and filtering

### 2. Video Reading
- **Primary**: OpenCV VideoCapture
- **Fallback**: imageio with FFmpeg backend (for WebM)
- Robust error handling for corrupted files

### 3. Quality Validation
- Minimum frame count check (≥10 frames)
- Minimum resolution check (≥20x20 pixels)
- Blank frame ratio analysis (<50% blank frames)
- Brightness and contrast metrics

### 4. Face and Mouth Detection
- OpenCV Haar cascade face detection
- Mouth region extraction (lower 43% of face)
- Fallback to center-lower region if no face detected

### 5. Frame Processing
- FPS normalization to 25 FPS
- Frame resampling using linear interpolation
- Mouth region cropping
- Resize to 140x46 pixels
- Frame count normalization to 75 frames

### 6. Data Augmentation (Optional)
- Random brightness adjustment (±20%)
- Random contrast adjustment (±20%)
- Gaussian noise addition (σ=5)

### 7. Output Generation
- MP4 video creation with H.264 codec
- Manifest entry logging
- Quality metrics recording

## Phrase Sets

### 5-Phrase Set (Legacy)
1. "Call the nurse"
2. "Help me"
3. "I cant breathe"
4. "I feel sick"
5. "I feel tired"

### 26-Phrase Set (Comprehensive)
1. "Where am I?"
2. "Who is with me today?"
3. "What happened to me?"
4. "Am I getting better?"
5. "Please explain again."
6. "Where is my wife?"
7. "Where is my husband?"
8. "I want to phone my family."
9. "I want to see my wife."
10. "I want to see my husband."
11. "What time is my wife coming?"
12. "What time is my husband coming?"
13. "I feel anxious."
14. "Stay with me, please."
15. "My chest hurts."
16. "My back hurts."
17. "I'm confused."
18. "I'm in pain."
19. "I have a headache."
20. "I'm uncomfortable."
21. "I need a medication."
22. "I need to lie down."
23. "I need to use the toilet."
24. "I need to sit up."
25. "I need help."
26. "I need to move."

## Quality Metrics

The enhanced preprocessor tracks comprehensive quality metrics:

```json
{
  "valid": true,
  "frame_count": 152,
  "avg_brightness": 116.93,
  "avg_contrast": 46.04,
  "blank_ratio": 0.0,
  "resolution": [654, 356]
}
```

## Manifest Logging

All processing is logged to `data/processing_manifest.csv` with fields:
- `input_path` - Original video file path
- `output_path` - Processed video file path
- `phrase` - Matched phrase text
- `phrase_idx` - Phrase index
- `fps` - Output FPS (25)
- `n_frames` - Output frame count (75)
- `width` - Output width (140)
- `height` - Output height (46)
- `demographic` - Demographic tag
- `quality_metrics` - JSON quality data
- `processed_at` - Processing timestamp
- `file_hash` - MD5 hash for duplicate detection

## Error Handling

### Robust Error Recovery
- Multiple video reading methods
- Graceful fallback for unsupported codecs
- Comprehensive logging of failures
- Continuation on individual file failures

### Common Issues and Solutions

1. **WebM files not reading**
   - Solution: Install `imageio-ffmpeg`
   - Automatic fallback to imageio

2. **Face detection failures**
   - Solution: Automatic fallback to center-lower region
   - Configurable detection parameters

3. **Duplicate processed files**
   - Solution: Use cleanup mode
   - Automatic duplicate detection

4. **Quality validation failures**
   - Solution: Check source video quality
   - Review quality metrics in manifest

## Performance Optimization

- Efficient frame resampling using numpy
- Minimal memory footprint
- Progress tracking with tqdm
- Parallel processing ready (future enhancement)

## Testing and Validation

### Validation Results
- ✅ WebM format support confirmed
- ✅ LipNet specification compliance verified
- ✅ Duplicate cleanup functionality tested
- ✅ Quality validation working correctly
- ✅ 37 duplicate files successfully removed

### Test Commands
```bash
# Test processing
python enhanced_video_preprocessor.py --input_dir data/i_cant_breathe --mode process --verbose

# Test validation
python enhanced_video_preprocessor.py --input_dir data --mode validate

# Test cleanup
python enhanced_video_preprocessor.py --input_dir data --mode cleanup
```

## Migration from Legacy Scripts

### From preprocess_videos.py
1. Use `--phrase_set 5` for 5-phrase compatibility
2. Enhanced face detection and quality validation
3. Automatic WebM support

### From auto_train_26_phrases.py
1. Use `--phrase_set 26` for full phrase set
2. Improved error handling and logging
3. Better duplicate management

## Future Enhancements

### Planned Features
- [ ] Parallel video processing
- [ ] Advanced data augmentation techniques
- [ ] Real-time processing monitoring
- [ ] Integration with training pipeline
- [ ] Automatic quality-based filtering
- [ ] Video compression optimization

### Performance Improvements
- [ ] GPU-accelerated processing
- [ ] Batch processing optimization
- [ ] Memory usage optimization
- [ ] Streaming video processing

## Troubleshooting

### Common Commands
```bash
# Check video properties
ffprobe input_video.webm

# Test imageio installation
python -c "import imageio; print(imageio.__version__)"

# View processing log
tail -f video_preprocessing.log

# Check manifest
head -5 data/processing_manifest.csv
```

### Support
For issues or questions, check the processing log at `video_preprocessing.log` and the manifest at `data/processing_manifest.csv`.
