#!/usr/bin/env python3
"""
Batch Video Analyzer for Perfect 10 ICU Lipreading Classifier
Analyzes multiple test videos and provides comparison results
"""

import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import sys
import json
from typing import Dict, List, Tuple
import pandas as pd

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class BatchPerfect10Analyzer:
    """Batch analyzer for testing multiple videos with Perfect 10 model"""
    
    def __init__(self, model_path: str):
        """Initialize the batch video analyzer"""
        
        self.model_path = Path(model_path)
        self.device = torch.device('cpu')  # Use CPU for consistency
        
        # Perfect 10 phrases (in order)
        self.perfect_phrases = [
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today"
        ]
        
        # Create phrase mappings
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        
        # Initialize components
        self.model = None
        self.video_processor = None
        
        print(f"🎯 Batch Perfect 10 Video Analyzer Initialized")
        print(f"   Model path: {self.model_path}")
        print(f"   Device: {self.device}")
        print(f"   Perfect phrases: {len(self.perfect_phrases)}")
    
    def load_model(self) -> bool:
        """Load the trained Perfect 10 model"""
        
        print(f"\n🤖 Loading Perfect 10 Model")
        print("=" * 30)
        
        if not self.model_path.exists():
            print(f"❌ Model checkpoint not found: {self.model_path}")
            return False
        
        try:
            # Load checkpoint
            checkpoint = torch.load(self.model_path, map_location=self.device)
            
            # Create model
            self.model = Perfect10Mobile3DTiny(num_classes=10)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            # Get training info
            best_val_accuracy = checkpoint.get('best_val_accuracy', 0.0)
            epoch = checkpoint.get('epoch', 0)
            
            print(f"✅ Perfect 10 model loaded successfully")
            print(f"   Parameters: {self.model.get_num_parameters():,}")
            print(f"   Training epoch: {epoch}")
            print(f"   Best validation accuracy: {best_val_accuracy:.1%}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            return False
    
    def initialize_video_processor(self):
        """Initialize video processor with same settings as training"""
        
        self.video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True
        )
        
        print(f"✅ Video processor initialized")
        print(f"   Target frames: 32")
        print(f"   Target size: 96×96")
        print(f"   Grayscale: True")
    
    def analyze_single_video(self, video_path: str) -> Dict:
        """Analyze a single video and return predictions"""
        
        video_name = Path(video_path).name
        
        if not Path(video_path).exists():
            return {
                'success': False,
                'video_name': video_name,
                'error': f'Video file not found: {video_path}'
            }
        
        try:
            # Process video
            video_tensor = self.video_processor.process_video(video_path)
            
            # Add batch dimension
            video_batch = video_tensor.unsqueeze(0).to(self.device)
            
            # Get prediction
            with torch.no_grad():
                outputs = self.model(video_batch)
                probabilities = F.softmax(outputs, dim=1)
                
                # Get top-3 predictions
                top3_probs, top3_indices = torch.topk(probabilities, 3, dim=1)
                
                top3_probs = top3_probs[0].cpu().numpy()
                top3_indices = top3_indices[0].cpu().numpy()
                
                # Convert to phrases
                top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
                
                # Determine confidence level
                top_confidence = float(top3_probs[0])
                if top_confidence >= 0.7:
                    confidence_level = "Very High"
                elif top_confidence >= 0.5:
                    confidence_level = "Moderate"
                elif top_confidence >= 0.3:
                    confidence_level = "Low"
                else:
                    confidence_level = "Very Low"
                
                return {
                    'success': True,
                    'video_name': video_name,
                    'video_path': video_path,
                    'video_shape': video_tensor.shape,
                    'top_prediction': top3_phrases[0],
                    'top_confidence': top_confidence,
                    'confidence_level': confidence_level,
                    'top3_phrases': top3_phrases,
                    'top3_probabilities': top3_probs.tolist(),
                    'all_probabilities': probabilities[0].cpu().numpy().tolist()
                }
                
        except Exception as e:
            return {
                'success': False,
                'video_name': video_name,
                'error': str(e)
            }
    
    def analyze_batch(self, video_paths: List[str]) -> List[Dict]:
        """Analyze multiple videos in batch"""
        
        print(f"\n🎬 Batch Analysis of {len(video_paths)} Videos")
        print("=" * 45)
        
        results = []
        
        for i, video_path in enumerate(video_paths, 1):
            video_name = Path(video_path).name
            print(f"\n📹 Analyzing {i}/{len(video_paths)}: {video_name}")
            
            result = self.analyze_single_video(video_path)
            results.append(result)
            
            if result['success']:
                print(f"   ✅ Success: {result['top_prediction'].title()} ({result['top_confidence']:.1%})")
            else:
                print(f"   ❌ Failed: {result['error']}")
        
        return results
    
    def create_summary_table(self, results: List[Dict]) -> pd.DataFrame:
        """Create a summary table of all results"""
        
        table_data = []
        
        for result in results:
            if result['success']:
                table_data.append({
                    'Video': result['video_name'],
                    'Top Prediction': result['top_prediction'].title(),
                    'Confidence': f"{result['top_confidence']:.1%}",
                    'Confidence Level': result['confidence_level'],
                    '2nd Choice': result['top3_phrases'][1].title(),
                    '2nd Confidence': f"{result['top3_probabilities'][1]:.1%}",
                    '3rd Choice': result['top3_phrases'][2].title(),
                    '3rd Confidence': f"{result['top3_probabilities'][2]:.1%}"
                })
            else:
                table_data.append({
                    'Video': result['video_name'],
                    'Top Prediction': 'FAILED',
                    'Confidence': 'N/A',
                    'Confidence Level': 'N/A',
                    '2nd Choice': 'N/A',
                    '2nd Confidence': 'N/A',
                    '3rd Choice': 'N/A',
                    '3rd Confidence': 'N/A'
                })
        
        return pd.DataFrame(table_data)
    
    def display_results(self, results: List[Dict]):
        """Display comprehensive results for all videos"""
        
        print(f"\n🎯 Perfect 10 Batch Analysis Results")
        print("=" * 40)
        
        # Create and display summary table
        summary_df = self.create_summary_table(results)
        print(f"\n📊 **SUMMARY TABLE**")
        print(summary_df.to_string(index=False))
        
        # Detailed results for each video
        print(f"\n📋 **DETAILED RESULTS**")
        
        for result in results:
            if result['success']:
                print(f"\n🎬 **{result['video_name'].upper()}**")
                print(f"   🏆 Top: {result['top_prediction'].title()} ({result['top_confidence']:.1%}) - {result['confidence_level']}")
                print(f"   🥈 2nd: {result['top3_phrases'][1].title()} ({result['top3_probabilities'][1]:.1%})")
                print(f"   🥉 3rd: {result['top3_phrases'][2].title()} ({result['top3_probabilities'][2]:.1%})")
            else:
                print(f"\n🎬 **{result['video_name'].upper()}**")
                print(f"   ❌ Analysis failed: {result['error']}")
        
        # Statistics
        successful_analyses = [r for r in results if r['success']]
        if successful_analyses:
            print(f"\n📈 **ANALYSIS STATISTICS**")
            print(f"   Successful analyses: {len(successful_analyses)}/{len(results)}")
            
            # Confidence distribution
            confidences = [r['top_confidence'] for r in successful_analyses]
            avg_confidence = np.mean(confidences)
            max_confidence = np.max(confidences)
            min_confidence = np.min(confidences)
            
            print(f"   Average confidence: {avg_confidence:.1%}")
            print(f"   Highest confidence: {max_confidence:.1%}")
            print(f"   Lowest confidence: {min_confidence:.1%}")
            
            # Confidence levels
            confidence_levels = [r['confidence_level'] for r in successful_analyses]
            level_counts = {level: confidence_levels.count(level) for level in set(confidence_levels)}
            print(f"   Confidence levels: {level_counts}")
    
    def save_batch_results(self, results: List[Dict], output_path: str):
        """Save batch analysis results to file"""
        
        batch_results = {
            'analysis_info': {
                'model_type': 'Perfect10Mobile3DTiny',
                'num_classes': 10,
                'perfect_phrases': self.perfect_phrases,
                'total_videos': len(results),
                'successful_analyses': len([r for r in results if r['success']])
            },
            'individual_results': results,
            'summary_table': self.create_summary_table(results).to_dict('records')
        }
        
        # Save to JSON
        with open(output_path, 'w') as f:
            json.dump(batch_results, f, indent=2)
        
        print(f"💾 Batch results saved: {output_path}")

def main():
    """Main batch analysis function"""
    
    print("🎬 Perfect 10 ICU Lipreading Batch Video Analyzer")
    print("=" * 55)
    
    # Configuration
    model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
    
    # Test video paths
    test_videos = [
        "/Users/<USER>/Desktop/test 2.webm",
        "/Users/<USER>/Desktop/test 3.webm", 
        "/Users/<USER>/Desktop/test 4.webm",
        "/Users/<USER>/Desktop/test 5.webm"
    ]
    
    # Initialize analyzer
    analyzer = BatchPerfect10Analyzer(model_path)
    
    # Load model
    if not analyzer.load_model():
        print("❌ Failed to load Perfect 10 model. Make sure training has completed.")
        return
    
    # Initialize video processor
    analyzer.initialize_video_processor()
    
    # Check which videos exist
    existing_videos = []
    missing_videos = []
    
    for video_path in test_videos:
        if Path(video_path).exists():
            existing_videos.append(video_path)
        else:
            missing_videos.append(Path(video_path).name)
    
    if missing_videos:
        print(f"\n⚠️  Missing videos: {', '.join(missing_videos)}")
    
    if not existing_videos:
        print(f"❌ No test videos found on desktop")
        return
    
    print(f"\n🎯 Found {len(existing_videos)} test videos to analyze")
    
    # Analyze all videos
    results = analyzer.analyze_batch(existing_videos)
    
    # Display results
    analyzer.display_results(results)
    
    # Save results
    output_path = "batch_video_analysis_results.json"
    analyzer.save_batch_results(results, output_path)
    
    print(f"\n🎉 Batch Analysis Complete!")
    print("=" * 30)
    print(f"✅ {len(existing_videos)} videos analyzed")
    print(f"✅ Results displayed above")
    print(f"✅ Detailed results saved to: {output_path}")

if __name__ == '__main__':
    main()
