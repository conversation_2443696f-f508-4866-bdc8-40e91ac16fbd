#!/usr/bin/env python3
"""
Analyzer for test 2.webm .webm and test 3. webm.webm videos
Handles the specific filenames found on desktop
"""

import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import sys
import json

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class Test23Analyzer:
    """Analyzer for test 2 and test 3 videos with correct filenames"""
    
    def __init__(self, model_path: str):
        """Initialize the analyzer"""
        
        self.model_path = Path(model_path)
        self.device = torch.device('cpu')
        
        # Perfect 10 phrases (in order)
        self.perfect_phrases = [
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today"
        ]
        
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        self.model = None
        self.video_processor = None
        
        print(f"🎯 Test 2 & 3 Video Analyzer")
    
    def load_model(self) -> bool:
        """Load the Perfect 10 model"""
        
        print(f"\n🤖 Loading Perfect 10 Model")
        print("=" * 30)
        
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device)
            self.model = Perfect10Mobile3DTiny(num_classes=10)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            best_val_accuracy = checkpoint.get('best_val_accuracy', 0.0)
            epoch = checkpoint.get('epoch', 0)
            
            print(f"✅ Model loaded successfully")
            print(f"   Training epoch: {epoch}")
            print(f"   Best validation accuracy: {best_val_accuracy:.1%}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            return False
    
    def initialize_video_processor(self):
        """Initialize video processor"""
        
        self.video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True
        )
        print(f"✅ Video processor initialized")
    
    def analyze_video(self, video_path: str, display_name: str) -> dict:
        """Analyze a single video"""
        
        print(f"\n🎬 Analyzing: {display_name}")
        print(f"   📁 File: {Path(video_path).name}")
        
        try:
            # Process video
            video_tensor = self.video_processor.process_video(video_path)
            print(f"   ✅ Video processed: {video_tensor.shape}")
            print(f"   📊 Value range: [{video_tensor.min():.3f}, {video_tensor.max():.3f}]")
            
            # Get prediction
            video_batch = video_tensor.unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                outputs = self.model(video_batch)
                probabilities = F.softmax(outputs, dim=1)
                
                # Get top-3 predictions
                top3_probs, top3_indices = torch.topk(probabilities, 3, dim=1)
                
                top3_probs = top3_probs[0].cpu().numpy()
                top3_indices = top3_indices[0].cpu().numpy()
                top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
                
                # Determine confidence level
                top_confidence = float(top3_probs[0])
                if top_confidence >= 0.7:
                    confidence_level = "Very High"
                    confidence_emoji = "🟢"
                elif top_confidence >= 0.5:
                    confidence_level = "Moderate"
                    confidence_emoji = "🟡"
                elif top_confidence >= 0.3:
                    confidence_level = "Low"
                    confidence_emoji = "🟠"
                else:
                    confidence_level = "Very Low"
                    confidence_emoji = "🔴"
                
                result = {
                    'success': True,
                    'video_name': display_name,
                    'file_path': video_path,
                    'top_prediction': top3_phrases[0],
                    'top_confidence': top_confidence,
                    'confidence_level': confidence_level,
                    'confidence_emoji': confidence_emoji,
                    'top3_phrases': top3_phrases,
                    'top3_probabilities': top3_probs.tolist(),
                    'all_probabilities': probabilities[0].cpu().numpy().tolist()
                }
                
                print(f"   🏆 Prediction: {top3_phrases[0].title()}")
                print(f"   📊 Confidence: {top_confidence:.1%} ({confidence_emoji} {confidence_level})")
                return result
                
        except Exception as e:
            print(f"   ❌ Analysis failed: {e}")
            return {
                'success': False,
                'video_name': display_name,
                'error': str(e)
            }
    
    def display_comprehensive_results(self, results: list):
        """Display comprehensive results including all 5 test videos"""
        
        print(f"\n🎯 COMPLETE PERFECT 10 TEST VIDEO ANALYSIS")
        print("=" * 50)
        
        # All results including previous ones
        all_results = [
            {
                'video_name': 'test 1.webm',
                'top_prediction': 'i feel anxious',
                'top_confidence': 0.603,
                'confidence_level': 'Moderate',
                'confidence_emoji': '🟡',
                'top3_phrases': ['i feel anxious', 'i need to sit up', 'where am i'],
                'top3_probabilities': [0.603, 0.124, 0.083]
            }
        ]
        
        # Add new results
        all_results.extend([r for r in results if r['success']])
        
        # Add previous results for test 4 and 5
        all_results.extend([
            {
                'video_name': 'test 4.webm',
                'top_prediction': 'who is with me today',
                'top_confidence': 0.460,
                'confidence_level': 'Low',
                'confidence_emoji': '🟠',
                'top3_phrases': ['who is with me today', 'i want to phone my family', 'what happened to me'],
                'top3_probabilities': [0.460, 0.119, 0.109]
            },
            {
                'video_name': 'test 5.webm',
                'top_prediction': 'am i getting better',
                'top_confidence': 0.480,
                'confidence_level': 'Low',
                'confidence_emoji': '🟠',
                'top3_phrases': ['am i getting better', 'what happened to me', 'i need to move'],
                'top3_probabilities': [0.480, 0.177, 0.077]
            }
        ])
        
        # Summary table
        print(f"\n📊 **COMPLETE RESULTS TABLE**")
        print(f"{'Video':<15} {'Top Prediction':<25} {'Confidence':<12} {'Level':<15}")
        print(f"{'-'*15} {'-'*25} {'-'*12} {'-'*15}")
        
        for result in all_results:
            emoji = result.get('confidence_emoji', '🟠')
            print(f"{result['video_name']:<15} {result['top_prediction'].title():<25} {result['top_confidence']:.1%}{'':>7} {emoji} {result['confidence_level']:<12}")
        
        # Detailed results
        print(f"\n📋 **DETAILED RESULTS FOR ALL VIDEOS**")
        for result in all_results:
            print(f"\n🎬 **{result['video_name'].upper()}**")
            print(f"   🏆 Top: {result['top_prediction'].title()} ({result['top_confidence']:.1%}) - {result['confidence_emoji']} {result['confidence_level']}")
            print(f"   🥈 2nd: {result['top3_phrases'][1].title()} ({result['top3_probabilities'][1]:.1%})")
            print(f"   🥉 3rd: {result['top3_phrases'][2].title()} ({result['top3_probabilities'][2]:.1%})")
        
        # Statistics
        confidences = [r['top_confidence'] for r in all_results]
        print(f"\n📈 **COMPLETE ANALYSIS STATISTICS**")
        print(f"   Total videos analyzed: {len(all_results)}")
        print(f"   Average confidence: {np.mean(confidences):.1%}")
        print(f"   Highest confidence: {np.max(confidences):.1%}")
        print(f"   Lowest confidence: {np.min(confidences):.1%}")
        
        # Confidence distribution
        levels = [r['confidence_level'] for r in all_results]
        level_counts = {level: levels.count(level) for level in set(levels)}
        print(f"   Confidence distribution: {level_counts}")
        
        # Phrase categories
        print(f"\n🎯 **IDENTIFIED PHRASE CATEGORIES**")
        categories = {
            'Emotional State': ['i feel anxious'],
            'Medical Status': ['am i getting better'],
            'Personnel Inquiry': ['who is with me today'],
            'Physical Needs': ['i need to move', 'i need to sit up'],
            'Communication': ['i want to phone my family'],
            'Information Seeking': ['what happened to me', 'what time is my wife coming'],
            'Orientation': ['where am i', 'i m confused']
        }
        
        identified_phrases = [r['top_prediction'] for r in all_results]
        for category, phrases in categories.items():
            found = [p for p in phrases if p in identified_phrases]
            if found:
                print(f"   ✅ {category}: {', '.join([p.title() for p in found])}")

def main():
    """Main function to analyze test 2 and test 3"""
    
    print("🎬 Perfect 10 Test 2 & 3 Video Analyzer")
    print("=" * 45)
    
    # Configuration
    model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
    
    # Video paths with correct filenames
    video_configs = [
        {
            'path': '/Users/<USER>/Desktop/test 2.webm .webm',
            'display_name': 'test 2.webm'
        },
        {
            'path': '/Users/<USER>/Desktop/test 3. webm.webm',
            'display_name': 'test 3.webm'
        }
    ]
    
    # Initialize analyzer
    analyzer = Test23Analyzer(model_path)
    
    # Load model
    if not analyzer.load_model():
        return
    
    # Initialize video processor
    analyzer.initialize_video_processor()
    
    # Analyze videos
    results = []
    for config in video_configs:
        if Path(config['path']).exists():
            result = analyzer.analyze_video(config['path'], config['display_name'])
            results.append(result)
        else:
            print(f"\n❌ Video not found: {config['path']}")
            results.append({
                'success': False,
                'video_name': config['display_name'],
                'error': 'File not found'
            })
    
    # Display comprehensive results
    analyzer.display_comprehensive_results(results)
    
    # Save results
    output_file = "complete_test_video_analysis.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n💾 Results saved: {output_file}")
    
    print(f"\n🎉 Complete Test Video Analysis Finished!")
    print("=" * 45)
    print("✅ All 5 test videos analyzed with Perfect 10 classifier")

if __name__ == '__main__':
    main()
