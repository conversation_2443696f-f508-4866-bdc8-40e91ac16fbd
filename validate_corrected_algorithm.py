#!/usr/bin/env python3
"""
Validate the corrected mouth detection algorithm on multiple sample videos.
"""

import cv2
import numpy as np
import sys
from pathlib import Path
import glob

# Add the current directory to path
sys.path.append('/Users/<USER>/Desktop/app dev 23.5.25')

try:
    from enhanced_video_preprocessor import EnhancedVideoPreprocessor
except ImportError as e:
    print(f"❌ Error importing enhanced_video_preprocessor: {e}")
    sys.exit(1)

def test_sample_videos():
    """Test corrected algorithm on multiple sample videos"""
    
    # Find sample videos
    video_dir = Path("/Users/<USER>/Desktop/icu-videos-today")
    sample_videos = list(video_dir.glob("*.webm"))[:5]  # Test first 5 videos
    
    if not sample_videos:
        print(f"❌ No sample videos found in {video_dir}")
        return False
    
    print(f"🧪 TESTING CORRECTED ALGORITHM ON {len(sample_videos)} SAMPLE VIDEOS")
    print("=" * 70)
    
    preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
    
    all_tests_passed = True
    
    for i, video_path in enumerate(sample_videos):
        print(f"\n📹 Testing video {i+1}: {video_path.name}")
        
        # Extract a frame for analysis
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            print(f"❌ Cannot open video: {video_path}")
            continue
        
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        cap.set(cv2.CAP_PROP_POS_FRAMES, 25)  # Middle frame
        ret, frame = cap.read()
        cap.release()
        
        if not ret:
            print(f"❌ Cannot read frame from video")
            continue
        
        print(f"   Video size: {width}x{height}")
        
        # Test mouth detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        x1, y1, x2, y2 = preprocessor.detect_mouth_region(gray)
        
        print(f"   Mouth region: ({x1}, {y1}) to ({x2}, {y2})")
        print(f"   Region size: {x2-x1}x{y2-y1}")
        print(f"   Y percentage: {y1/height*100:.1f}% to {y2/height*100:.1f}%")
        
        # Validate coordinates
        test_passed = True
        
        # Check if targeting upper portion (should be < 50% of height)
        if y2 > height * 0.5:
            print(f"   ❌ FAIL: Targeting lower portion (Y2={y2} > {height*0.5})")
            test_passed = False
        else:
            print(f"   ✅ PASS: Targeting upper portion")
        
        # Check if region is reasonable size
        region_width = x2 - x1
        region_height = y2 - y1
        if region_width < 50 or region_height < 30:
            print(f"   ❌ FAIL: Region too small ({region_width}x{region_height})")
            test_passed = False
        elif region_width > 200 or region_height > 100:
            print(f"   ❌ FAIL: Region too large ({region_width}x{region_height})")
            test_passed = False
        else:
            print(f"   ✅ PASS: Region size appropriate")
        
        # For 400x200 videos, check if targeting middle column
        if width == 400 and height == 200:
            col_width = width // 3  # 133
            if x1 >= col_width and x2 <= 2 * col_width:
                print(f"   ✅ PASS: Targeting middle column ({col_width}-{2*col_width})")
            else:
                print(f"   ❌ FAIL: Not targeting middle column (X: {x1}-{x2}, should be {col_width}-{2*col_width})")
                test_passed = False
        
        if not test_passed:
            all_tests_passed = False
        
        # Create visual validation
        validation_frame = frame.copy()
        cv2.rectangle(validation_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        cv2.putText(validation_frame, f"Video {i+1}: {video_path.stem[:20]}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(validation_frame, f"Region: ({x1},{y1}) to ({x2},{y2})", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        cv2.imwrite(f"validation_video_{i+1}.jpg", validation_frame)
        print(f"   💾 Saved validation_video_{i+1}.jpg")
    
    print(f"\n📊 VALIDATION SUMMARY:")
    if all_tests_passed:
        print(f"✅ ALL TESTS PASSED - Algorithm is working correctly!")
        return True
    else:
        print(f"❌ SOME TESTS FAILED - Algorithm needs further adjustment")
        return False

def test_processing_pipeline():
    """Test the complete processing pipeline on a sample video"""
    print(f"\n🔧 TESTING COMPLETE PROCESSING PIPELINE")
    
    video_path = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    
    if not Path(video_path).exists():
        print(f"❌ Test video not found: {video_path}")
        return False
    
    # Remove existing processed video
    processed_path = Path("data/where_am_i/where_am_i__useruser01__18to39__male__not_specified__20250809T053449_processed.mp4")
    if processed_path.exists():
        processed_path.unlink()
    
    # Process the video
    preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
    
    # Find phrase index
    phrase_idx = 0  # "Where am I?" is first phrase
    
    try:
        result = preprocessor.process_single_video(Path(video_path), phrase_idx)
        
        if result and processed_path.exists():
            # Validate processed video
            cap = cv2.VideoCapture(str(processed_path))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frames / fps
            
            print(f"✅ Processing successful!")
            print(f"   Output specs: {width}x{height}, {fps} FPS, {frames} frames, {duration:.1f}s")
            
            # Check LipNet compliance
            compliance_passed = True
            if width != 140 or height != 46:
                print(f"❌ Resolution incorrect: {width}x{height} (should be 140x46)")
                compliance_passed = False
            if abs(fps - 25.0) > 0.1:
                print(f"❌ FPS incorrect: {fps} (should be 25.0)")
                compliance_passed = False
            if frames != 75:
                print(f"❌ Frame count incorrect: {frames} (should be 75)")
                compliance_passed = False
            if abs(duration - 3.0) > 0.1:
                print(f"❌ Duration incorrect: {duration:.1f}s (should be 3.0s)")
                compliance_passed = False
            
            if compliance_passed:
                print(f"✅ LipNet compliance: PASSED")
            else:
                print(f"❌ LipNet compliance: FAILED")
            
            # Extract sample frames for visual inspection
            sample_frames = []
            for frame_idx in [0, 25, 50, 74]:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if ret:
                    sample_frames.append((frame_idx, frame))
            
            cap.release()
            
            # Create validation grid
            if len(sample_frames) >= 4:
                # Scale up frames for visibility
                scaled_frames = []
                for frame_idx, frame in sample_frames:
                    scaled = cv2.resize(frame, (280, 92), interpolation=cv2.INTER_NEAREST)
                    cv2.putText(scaled, f"Frame {frame_idx}", (10, 20), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
                    scaled_frames.append(scaled)
                
                # Create 2x2 grid
                top_row = np.hstack([scaled_frames[0], scaled_frames[1]])
                bottom_row = np.hstack([scaled_frames[2], scaled_frames[3]])
                grid = np.vstack([top_row, bottom_row])
                
                # Add title
                title_height = 40
                title_img = np.zeros((title_height, grid.shape[1], 3), dtype=np.uint8)
                cv2.putText(title_img, "CORRECTED ALGORITHM - PROCESSED OUTPUT VALIDATION", 
                           (20, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                
                final_grid = np.vstack([title_img, grid])
                cv2.imwrite("processed_output_validation.jpg", final_grid)
                print(f"💾 Saved processed_output_validation.jpg")
            
            return compliance_passed
        else:
            print(f"❌ Processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Processing error: {e}")
        return False

def main():
    print("🔍 CORRECTED ALGORITHM VALIDATION")
    print("=" * 50)
    
    # Test on multiple sample videos
    sample_test_passed = test_sample_videos()
    
    # Test complete processing pipeline
    pipeline_test_passed = test_processing_pipeline()
    
    print(f"\n📋 FINAL VALIDATION RESULTS:")
    print(f"Sample videos test: {'✅ PASSED' if sample_test_passed else '❌ FAILED'}")
    print(f"Processing pipeline test: {'✅ PASSED' if pipeline_test_passed else '❌ FAILED'}")
    
    if sample_test_passed and pipeline_test_passed:
        print(f"\n🎉 VALIDATION SUCCESSFUL!")
        print(f"✅ Corrected algorithm is working properly")
        print(f"✅ Ready to reprocess full dataset")
        return True
    else:
        print(f"\n❌ VALIDATION FAILED!")
        print(f"❌ Algorithm needs further adjustment before full reprocessing")
        return False

if __name__ == "__main__":
    main()
