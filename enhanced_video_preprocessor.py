#!/usr/bin/env python3
"""
Enhanced Video Preprocessor for ICU Lipreading
Comprehensive video preprocessing pipeline with WebM support, data augmentation, and quality validation.

Features:
- WebM format support via imageio
- Robust error handling and validation
- Data augmentation capabilities
- Progress tracking and resumption
- Duplicate detection and cleanup
- Quality validation checks
- Comprehensive logging

Usage:
    python enhanced_video_preprocessor.py --input_dir /path/to/videos --mode process
    python enhanced_video_preprocessor.py --input_dir /path/to/videos --mode cleanup
    python enhanced_video_preprocessor.py --input_dir /path/to/videos --mode validate
"""

import os
import sys
import csv
import json
import pickle
import argparse
import hashlib
import logging
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any
from datetime import datetime

import cv2
import numpy as np
from tqdm import tqdm

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('video_preprocessing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants for LipNet compatibility
TARGET_FPS = 25
TARGET_W, TARGET_H = 140, 46  # LipNet frame size (W,H)
TARGET_T = 75  # frames per clip

# ICU phrases (26 comprehensive set)
ICU_26_PHRASES = [
    "Where am I?", "Who is with me today?", "What happened to me?", "Am I getting better?",
    "Please explain again.", "Where is my wife?", "Where is my husband?",
    "I want to phone my family.", "I want to see my wife.", "I want to see my husband.",
    "What time is my wife coming?", "What time is my husband coming?", "I feel anxious.",
    "Stay with me, please.", "My chest hurts.", "My back hurts.", "I'm confused.",
    "I'm in pain.", "I have a headache.", "I'm uncomfortable.", "I need a medication.",
    "I need to lie down.", "I need to use the toilet.", "I need to sit up.",
    "I need help.", "I need to move."
]

# Simplified 5-phrase set for compatibility
ICU_5_PHRASES = [
    "Call the nurse",
    "Help me", 
    "I cant breathe",
    "I feel sick",
    "I feel tired"
]

# Keywords for phrase matching
PHRASE_KEYWORDS_26 = [
    ["where", "am", "i"], ["who", "is", "with", "me"], ["what", "happened"],
    ["am", "i", "getting", "better"], ["please", "explain"], ["where", "is", "my", "wife"],
    ["where", "is", "my", "husband"], ["phone", "my", "family"], ["see", "my", "wife"],
    ["see", "my", "husband"], ["time", "wife", "coming"], ["time", "husband", "coming"],
    ["feel", "anxious"], ["stay", "with", "me"], ["chest", "hurts"], ["back", "hurts"],
    ["confused"], ["in", "pain"], ["headache"], ["uncomfortable"], ["need", "medication"],
    ["lie", "down"], ["use", "toilet"], ["sit", "up"], ["need", "help"], ["need", "move"]
]

PHRASE_KEYWORDS_5 = [
    ["call", "nurse"], ["help", "me"], ["cant", "breathe"], ["feel", "sick"], ["feel", "tired"]
]

# Special mappings for videos that don't match standard keywords
SPECIAL_FILENAME_MAPPINGS_5 = {
    "henry video": 0,  # "Call the nurse"
    "img 3969": 0,     # "Call the nurse"
    "test i need help": 1,  # "I need help" -> maps to "Help me" (index 1)
    "test i cant breathe": 2,  # "I cant breathe" (index 2)
    "bright test i need help": 1,  # Bright version of "I need help"
}

def slug_phrase(phrase: str) -> str:
    """Convert phrase to filesystem-safe slug"""
    return "".join(ch.lower() if ch.isalnum() else "_" for ch in phrase).strip("_")

def calculate_file_hash(file_path: Path) -> str:
    """Calculate MD5 hash of file for duplicate detection"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating hash for {file_path}: {e}")
        return ""

class EnhancedVideoPreprocessor:
    def __init__(self, input_dir: str, data_dir: str = "data", phrase_set: str = "26", 
                 demographic: str = "mixed"):
        self.input_dir = Path(input_dir).expanduser()
        self.data_dir = Path(data_dir)
        self.phrase_set = phrase_set
        self.demographic = demographic
        
        # Select phrase set
        if phrase_set == "5":
            self.phrases = ICU_5_PHRASES
            self.keywords = PHRASE_KEYWORDS_5
        else:
            self.phrases = ICU_26_PHRASES
            self.keywords = PHRASE_KEYWORDS_26
            
        # Create directories
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.manifest_path = self.data_dir / "processing_manifest.csv"
        self.duplicates_path = self.data_dir / "duplicates.json"
        
        # Initialize face detector
        self.face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + "haarcascade_frontalface_default.xml"
        )
        
        logger.info(f"Initialized preprocessor for {len(self.phrases)} phrases")
        logger.info(f"Input directory: {self.input_dir}")
        logger.info(f"Output directory: {self.data_dir}")

    def find_video_files(self) -> List[Path]:
        """Find all supported video files recursively"""
        extensions = [
            '*.mp4', '*.MP4', '*.mov', '*.MOV', '*.avi', '*.AVI', 
            '*.mkv', '*.MKV', '*.webm', '*.WEBM', '*.m4v', '*.M4V'
        ]
        
        video_files = []
        for ext in extensions:
            video_files.extend(self.input_dir.rglob(ext))
        
        # Filter out already processed files
        video_files = [vf for vf in video_files if not vf.name.endswith('_processed.mp4')]
        
        logger.info(f"Found {len(video_files)} video files")
        return sorted(video_files)

    def read_video_frames_robust(self, video_path: Path) -> Tuple[List[np.ndarray], float]:
        """
        Robust video reading with multiple fallback methods
        Returns: (frames_list, fps_src)
        """
        frames = []
        fps_src = TARGET_FPS
        
        # Method 1: Try OpenCV first
        try:
            cap = cv2.VideoCapture(str(video_path))
            if cap.isOpened():
                fps_src = cap.get(cv2.CAP_PROP_FPS) or TARGET_FPS
                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break
                    if frame is not None:
                        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                        frames.append(gray)
                cap.release()
                
                if frames:
                    logger.debug(f"OpenCV: Read {len(frames)} frames from {video_path.name}")
                    return frames, fps_src
        except Exception as e:
            logger.warning(f"OpenCV failed for {video_path.name}: {e}")
        
        # Method 2: Fallback to imageio for WebM and other formats
        try:
            import imageio
            reader = imageio.get_reader(str(video_path))
            
            # Get metadata
            try:
                meta = reader.get_meta_data()
                fps_src = meta.get('fps', TARGET_FPS)
            except Exception:
                fps_src = TARGET_FPS
            
            # Read frames
            for frame in reader:
                if frame is not None:
                    # Convert RGB to grayscale
                    if len(frame.shape) == 3:
                        gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
                    else:
                        gray = frame
                    frames.append(gray)
            
            reader.close()
            logger.debug(f"imageio: Read {len(frames)} frames from {video_path.name}")
            return frames, fps_src
            
        except ImportError:
            logger.error("imageio not available for WebM support")
        except Exception as e:
            logger.error(f"imageio failed for {video_path.name}: {e}")
        
        return frames, fps_src

    def detect_mouth_region(self, gray_frame: np.ndarray) -> Tuple[int, int, int, int]:
        """
        Detect mouth region using face detection
        CORRECTED: Based on precise lip locator analysis of ICU dataset videos
        Returns: (x1, y1, x2, y2) coordinates
        """
        h, w = gray_frame.shape

        # Detect if this is an original ICU video (400x200) vs processed video (140x46)
        is_original_icu_video = (w == 400 and h == 200)
        is_processed_video = (w == 140 and h == 46)

        # Detect faces
        faces = self.face_cascade.detectMultiScale(
            gray_frame,
            scaleFactor=1.1,
            minNeighbors=5,
            flags=cv2.CASCADE_SCALE_IMAGE,
            minSize=(30, 30) if is_processed_video else (60, 60)
        )

        if len(faces) == 0:
            # Fallback: Use precise coordinates based on video type
            if is_original_icu_video:
                # For 400x200 ICU videos: target top-middle section (3x2 grid layout)
                # Grid: 3 columns (133px each), 2 rows (100px each)
                # Lips are in top-middle section: (133, 0) to (266, 100)
                # ENHANCED: Expanded upward for better upper lip visibility
                x1 = 146  # Top-middle section with margin
                y1 = 4    # Enhanced upper lip capture (was 8, originally 20)
                x2 = 253  # Top-middle section with margin
                y2 = 70   # Slightly expanded for proportional framing (was 68, originally 80)
                logger.debug(f"ICU video fallback: enhanced lip region at ({x1}, {y1}) to ({x2}, {y2})")
            elif is_processed_video:
                # For 140x46 processed videos: proportionally scale the lip region
                x1 = 14   # 10% margin from left
                y1 = 10   # Upper portion for lips
                x2 = 126  # 10% margin from right
                y2 = 25   # Keep lip region height proportional
                logger.debug(f"Processed video fallback: lip region at ({x1}, {y1}) to ({x2}, {y2})")
            else:
                # For other video sizes: use proportional scaling
                bw = int(0.8 * w)
                bh = int(0.16 * h)  # 16% of height for lip region
                x1 = (w - bw) // 2
                y1 = int(h * 0.30)  # 30% from top
                x2 = x1 + bw
                y2 = min(h - 1, y1 + bh)
                logger.debug(f"Generic fallback: lip region at ({x1}, {y1}) to ({x2}, {y2})")

            return x1, y1, x2, y2

        # Use largest face
        x, y, fw, fh = max(faces, key=lambda f: f[2] * f[3])
        logger.debug(f"Detected face: {fw}x{fh} at ({x}, {y}), frame: {w}x{h}")

        # Extract mouth region - CORRECTED based on analysis
        mx1 = x + int(0.15 * fw)
        mx2 = x + int(0.85 * fw)

        if is_original_icu_video:
            # For 400x200 ICU videos: target lip area within detected face (top-middle section)
            # Constrain to top-middle column and upper portion
            # ENHANCED: Expanded for better upper lip visibility
            mx1 = max(mx1, 146)  # Don't go left of top-middle section
            mx2 = min(mx2, 253)  # Don't go right of top-middle section
            my1 = y + int(0.02 * fh)  # Start at 2% of face height (enhanced upper lip capture)
            my2 = y + int(0.37 * fh)  # End at 37% of face height (slightly expanded)
            logger.debug(f"ICU video face detection: enhanced lip region in top-middle section")
        elif is_processed_video:
            # For 140x46 processed videos: lips should be in upper portion
            my1 = y + int(0.10 * fh)  # Start at 10% of face height
            my2 = y + int(0.40 * fh)  # End at 40% of face height
            logger.debug(f"Processed video face detection: 10%-40% of face height")
        else:
            # For other video sizes: use conservative lip region
            my1 = y + int(0.55 * fh)  # Original logic for full-face videos
            my2 = y + int(0.98 * fh)
            logger.debug(f"Generic face detection: 55%-98% of face height")

        # Ensure bounds
        mx1 = max(0, mx1)
        my1 = max(0, my1)
        mx2 = min(w - 1, mx2)
        my2 = min(h - 1, my2)

        if mx2 <= mx1 or my2 <= my1:
            # Fallback if detection failed - use the same logic as no face detected
            if is_original_icu_video:
                return 146, 4, 253, 70
            elif is_processed_video:
                return 14, 10, 126, 25
            else:
                return 0, int(h * 0.30), w - 1, min(h - 1, int(h * 0.46))

        logger.debug(f"Final mouth region: ({mx1}, {my1}) to ({mx2}, {my2})")
        return mx1, my1, mx2, my2

    def match_filename_to_phrase(self, filename: str) -> Optional[int]:
        """Match filename to phrase index using keywords"""
        fn = filename.lower().replace("-", " ").replace("_", " ")

        # Check special filename mappings first (for 5-phrase set)
        if self.phrase_set == "5":
            for pattern, phrase_idx in SPECIAL_FILENAME_MAPPINGS_5.items():
                if pattern in fn:
                    return phrase_idx

        # Standard keyword matching
        for idx, words in enumerate(self.keywords):
            if all(word in fn for word in words):
                return idx

        return None

    def validate_video_quality(self, frames: List[np.ndarray]) -> Dict[str, Any]:
        """Validate video quality and return metrics"""
        if not frames:
            return {"valid": False, "reason": "No frames"}

        # Check frame count
        if len(frames) < 10:
            return {"valid": False, "reason": "Too few frames"}

        # Check frame dimensions
        h, w = frames[0].shape
        if h < 20 or w < 20:
            return {"valid": False, "reason": "Frame too small"}

        # Check for blank frames
        blank_frames = sum(1 for frame in frames if np.mean(frame) < 10)
        blank_ratio = blank_frames / len(frames)

        if blank_ratio > 0.5:
            return {"valid": False, "reason": "Too many blank frames"}

        # Calculate quality metrics
        avg_brightness = np.mean([np.mean(frame) for frame in frames])
        avg_contrast = np.mean([np.std(frame) for frame in frames])

        return {
            "valid": True,
            "frame_count": len(frames),
            "avg_brightness": avg_brightness,
            "avg_contrast": avg_contrast,
            "blank_ratio": blank_ratio,
            "resolution": (w, h)
        }

    def apply_data_augmentation(self, frames: List[np.ndarray],
                               augment_type: str = "none") -> List[np.ndarray]:
        """Apply data augmentation to frames"""
        if augment_type == "none":
            return frames

        augmented = []
        for frame in frames:
            if augment_type == "brightness":
                # Random brightness adjustment
                factor = np.random.uniform(0.8, 1.2)
                frame = np.clip(frame * factor, 0, 255).astype(np.uint8)
            elif augment_type == "contrast":
                # Random contrast adjustment
                factor = np.random.uniform(0.8, 1.2)
                frame = np.clip((frame - 128) * factor + 128, 0, 255).astype(np.uint8)
            elif augment_type == "noise":
                # Add slight noise
                noise = np.random.normal(0, 5, frame.shape)
                frame = np.clip(frame + noise, 0, 255).astype(np.uint8)

            augmented.append(frame)

        return augmented

    def process_single_video(self, video_path: Path, phrase_idx: int,
                           augment: bool = False) -> bool:
        """Process a single video file"""
        try:
            phrase = self.phrases[phrase_idx]
            phrase_dir = slug_phrase(phrase)
            out_dir = self.data_dir / phrase_dir
            out_dir.mkdir(parents=True, exist_ok=True)

            # Generate output filename
            base_name = video_path.stem
            out_mp4 = out_dir / f"{base_name}_processed.mp4"

            # Skip if already processed
            if out_mp4.exists():
                logger.info(f"Skip (exists): {out_mp4.name}")
                return True

            # Read frames
            raw_frames, fps_src = self.read_video_frames_robust(video_path)
            if not raw_frames:
                logger.error(f"No frames extracted from: {video_path}")
                return False

            # Validate quality
            quality_metrics = self.validate_video_quality(raw_frames)
            if not quality_metrics["valid"]:
                logger.warning(f"Quality check failed for {video_path.name}: {quality_metrics['reason']}")
                return False

            # Resample to target FPS
            duration_s = len(raw_frames) / max(fps_src, 1e-6)
            target_frame_count = max(1, int(round(duration_s * TARGET_FPS)))

            if target_frame_count != len(raw_frames):
                indices = np.linspace(0, len(raw_frames) - 1, num=target_frame_count, dtype=int)
                resampled_frames = [raw_frames[i] for i in indices]
            else:
                resampled_frames = raw_frames

            # Detect mouth region on middle frame
            mid_frame = resampled_frames[len(resampled_frames) // 2]
            x1, y1, x2, y2 = self.detect_mouth_region(mid_frame)

            # Crop and resize frames
            processed_frames = []
            for frame in resampled_frames:
                # Crop to mouth region
                cropped = frame[y1:y2, x1:x2]

                # Resize to target dimensions
                if cropped.size > 0:
                    resized = cv2.resize(cropped, (TARGET_W, TARGET_H),
                                       interpolation=cv2.INTER_AREA)
                    processed_frames.append(resized)

            if not processed_frames:
                logger.error(f"No valid frames after processing: {video_path}")
                return False

            # Apply data augmentation if requested
            if augment:
                processed_frames = self.apply_data_augmentation(processed_frames, "brightness")

            # Normalize to target frame count
            if len(processed_frames) > TARGET_T:
                processed_frames = processed_frames[:TARGET_T]
            while len(processed_frames) < TARGET_T:
                processed_frames.append(processed_frames[-1])

            # Write output video
            fourcc = cv2.VideoWriter_fourcc(*"mp4v")
            writer = cv2.VideoWriter(str(out_mp4), fourcc, TARGET_FPS,
                                   (TARGET_W, TARGET_H), False)

            for frame in processed_frames:
                writer.write(frame)
            writer.release()

            # Record in manifest
            self.write_manifest_entry({
                "input_path": str(video_path),
                "output_path": str(out_mp4),
                "phrase": phrase,
                "phrase_idx": phrase_idx,
                "fps": TARGET_FPS,
                "n_frames": TARGET_T,
                "width": TARGET_W,
                "height": TARGET_H,
                "demographic": self.demographic,
                "quality_metrics": quality_metrics,
                "processed_at": datetime.now().isoformat(),
                "file_hash": calculate_file_hash(video_path)
            })

            logger.info(f"✅ Processed: {video_path.name} -> {phrase_dir}")
            return True

        except Exception as e:
            logger.error(f"❌ Error processing {video_path}: {e}")
            return False

    def write_manifest_entry(self, entry: Dict[str, Any]):
        """Write entry to processing manifest"""
        fieldnames = [
            "input_path", "output_path", "phrase", "phrase_idx", "fps",
            "n_frames", "width", "height", "demographic", "quality_metrics",
            "processed_at", "file_hash"
        ]

        file_exists = self.manifest_path.exists()
        with open(self.manifest_path, "a", newline="") as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            if not file_exists:
                writer.writeheader()

            # Convert complex fields to JSON strings
            entry_copy = entry.copy()
            entry_copy["quality_metrics"] = json.dumps(entry["quality_metrics"])
            writer.writerow(entry_copy)

    def process_all_videos(self, augment: bool = False) -> Dict[str, int]:
        """Process all videos in input directory"""
        video_files = self.find_video_files()
        if not video_files:
            logger.warning("No video files found to process")
            return {"processed": 0, "failed": 0, "skipped": 0}

        stats = {"processed": 0, "failed": 0, "skipped": 0}

        logger.info(f"Processing {len(video_files)} video files...")

        for video_file in tqdm(video_files, desc="Processing videos"):
            # Try to match phrase
            phrase_idx = self.match_filename_to_phrase(video_file.name)

            if phrase_idx is None:
                logger.warning(f"Could not match phrase for: {video_file.name}")
                stats["skipped"] += 1
                continue

            success = self.process_single_video(video_file, phrase_idx, augment)
            if success:
                stats["processed"] += 1
            else:
                stats["failed"] += 1

        logger.info(f"Processing complete: {stats}")
        return stats

    def cleanup_duplicates(self) -> Dict[str, int]:
        """Clean up duplicate processed files"""
        logger.info("Scanning for duplicate processed files...")

        duplicates_found = 0
        duplicates_removed = 0

        for phrase_dir in self.data_dir.iterdir():
            if not phrase_dir.is_dir():
                continue

            # Group files by base name
            file_groups = {}
            for file_path in phrase_dir.glob("*_processed*.mp4"):
                # Extract base name (remove _processed suffixes)
                base_name = file_path.stem
                while base_name.endswith("_processed"):
                    base_name = base_name[:-10]  # Remove "_processed"

                if base_name not in file_groups:
                    file_groups[base_name] = []
                file_groups[base_name].append(file_path)

            # Remove duplicates (keep the first one)
            for base_name, files in file_groups.items():
                if len(files) > 1:
                    duplicates_found += len(files) - 1
                    # Sort by modification time, keep the newest
                    files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

                    for duplicate_file in files[1:]:
                        try:
                            duplicate_file.unlink()
                            duplicates_removed += 1
                            logger.info(f"Removed duplicate: {duplicate_file}")
                        except Exception as e:
                            logger.error(f"Failed to remove {duplicate_file}: {e}")

        stats = {"found": duplicates_found, "removed": duplicates_removed}
        logger.info(f"Cleanup complete: {stats}")
        return stats

    def validate_processed_videos(self) -> Dict[str, Any]:
        """Validate all processed videos"""
        logger.info("Validating processed videos...")

        validation_results = {
            "total_videos": 0,
            "valid_videos": 0,
            "invalid_videos": 0,
            "issues": []
        }

        for phrase_dir in self.data_dir.iterdir():
            if not phrase_dir.is_dir():
                continue

            for video_file in phrase_dir.glob("*_processed.mp4"):
                validation_results["total_videos"] += 1

                try:
                    # Check if file can be opened
                    cap = cv2.VideoCapture(str(video_file))
                    if not cap.isOpened():
                        validation_results["invalid_videos"] += 1
                        validation_results["issues"].append({
                            "file": str(video_file),
                            "issue": "Cannot open video file"
                        })
                        continue

                    # Check video properties
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

                    cap.release()

                    # Validate against expected values
                    issues = []
                    if abs(fps - TARGET_FPS) > 1:
                        issues.append(f"FPS mismatch: {fps} vs {TARGET_FPS}")
                    if frame_count != TARGET_T:
                        issues.append(f"Frame count mismatch: {frame_count} vs {TARGET_T}")
                    if width != TARGET_W or height != TARGET_H:
                        issues.append(f"Resolution mismatch: {width}x{height} vs {TARGET_W}x{TARGET_H}")

                    if issues:
                        validation_results["invalid_videos"] += 1
                        validation_results["issues"].append({
                            "file": str(video_file),
                            "issues": issues
                        })
                    else:
                        validation_results["valid_videos"] += 1

                except Exception as e:
                    validation_results["invalid_videos"] += 1
                    validation_results["issues"].append({
                        "file": str(video_file),
                        "issue": f"Validation error: {e}"
                    })

        logger.info(f"Validation complete: {validation_results['valid_videos']}/{validation_results['total_videos']} videos valid")
        return validation_results

    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive processing report"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "phrase_set": self.phrase_set,
            "total_phrases": len(self.phrases),
            "phrase_stats": {},
            "overall_stats": {
                "total_processed_videos": 0,
                "total_original_videos": 0
            }
        }

        # Count videos per phrase
        for i, phrase in enumerate(self.phrases):
            phrase_dir = self.data_dir / slug_phrase(phrase)
            if phrase_dir.exists():
                processed_videos = list(phrase_dir.glob("*_processed.mp4"))
                original_videos = list(phrase_dir.glob("*.mov")) + list(phrase_dir.glob("*.mp4"))
                original_videos = [v for v in original_videos if not v.name.endswith("_processed.mp4")]

                report["phrase_stats"][phrase] = {
                    "processed_count": len(processed_videos),
                    "original_count": len(original_videos),
                    "phrase_index": i
                }

                report["overall_stats"]["total_processed_videos"] += len(processed_videos)
                report["overall_stats"]["total_original_videos"] += len(original_videos)

        return report


def main():
    parser = argparse.ArgumentParser(description="Enhanced Video Preprocessor for ICU Lipreading")
    parser.add_argument("--input_dir", required=True, help="Input directory containing videos")
    parser.add_argument("--data_dir", default="data", help="Output data directory")
    parser.add_argument("--mode", choices=["process", "cleanup", "validate", "report"],
                       default="process", help="Processing mode")
    parser.add_argument("--phrase_set", choices=["5", "26"], default="26",
                       help="Phrase set to use (5 or 26 phrases)")
    parser.add_argument("--demographic", default="mixed", help="Demographic tag for processed videos")
    parser.add_argument("--augment", action="store_true", help="Apply data augmentation")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize preprocessor
    preprocessor = EnhancedVideoPreprocessor(
        input_dir=args.input_dir,
        data_dir=args.data_dir,
        phrase_set=args.phrase_set,
        demographic=args.demographic
    )

    try:
        if args.mode == "process":
            stats = preprocessor.process_all_videos(augment=args.augment)
            print(f"\n✅ Processing completed: {stats}")

        elif args.mode == "cleanup":
            stats = preprocessor.cleanup_duplicates()
            print(f"\n✅ Cleanup completed: {stats}")

        elif args.mode == "validate":
            results = preprocessor.validate_processed_videos()
            print(f"\n✅ Validation completed: {results['valid_videos']}/{results['total_videos']} videos valid")
            if results["issues"]:
                print("\nIssues found:")
                for issue in results["issues"][:10]:  # Show first 10 issues
                    print(f"  - {issue}")

        elif args.mode == "report":
            report = preprocessor.generate_report()
            print(f"\n📊 Processing Report:")
            print(f"Phrase set: {report['phrase_set']} ({report['total_phrases']} phrases)")
            print(f"Total processed videos: {report['overall_stats']['total_processed_videos']}")
            print(f"Total original videos: {report['overall_stats']['total_original_videos']}")
            print("\nPer-phrase breakdown:")
            for phrase, stats in report["phrase_stats"].items():
                print(f"  {phrase}: {stats['processed_count']} processed, {stats['original_count']} original")

    except Exception as e:
        logger.error(f"❌ Processing failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
