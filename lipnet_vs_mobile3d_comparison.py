#!/usr/bin/env python3
"""
Comprehensive performance comparison: LipNet vs Mobile3DTiny on Perfect 10 phrases
Analyzes accuracy improvements, computational requirements, and deployment considerations
"""

import torch
import torch.nn.functional as F
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import json
import time
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report

# Add current directory to path
sys.path.append('.')

from lipnet_perfect_10 import LipNetPerfect10, LipNetPerfect10Manager
from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class ModelPerformanceComparator:
    """Comprehensive comparison between LipNet and Mobile3DTiny on Perfect 10 phrases"""
    
    def __init__(self):
        """Initialize the performance comparator"""
        
        self.device = torch.device('cpu')  # Use CPU for fair comparison
        
        # Perfect 10 phrases
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        
        # Models
        self.lipnet_model = None
        self.mobile3d_model = None
        
        # Video processor
        self.video_processor = VideoProcessor(
            target_frames=64,
            target_size=(112, 112),
            grayscale=True,
            fps=25.0,
            use_dataset_normalization=True
        )
        
        print(f"🔍 Model Performance Comparator Initialized")
        print(f"   Perfect phrases: {len(self.perfect_phrases)}")
        print(f"   Device: {self.device}")
        print(f"   Enhanced preprocessing: 64 frames, 112×112, z-score")
    
    def load_models(self, lipnet_path: str, mobile3d_path: str) -> Tuple[bool, bool]:
        """Load both LipNet and Mobile3DTiny models"""
        
        print(f"\n🤖 Loading Models for Comparison")
        print("=" * 35)
        
        lipnet_loaded = False
        mobile3d_loaded = False
        
        # Load LipNet model
        if Path(lipnet_path).exists():
            try:
                checkpoint = torch.load(lipnet_path, map_location=self.device)
                
                manager = LipNetPerfect10Manager()
                self.lipnet_model = manager.create_model(
                    hidden_dim=256,
                    num_rnn_layers=2,
                    rnn_type='LSTM',
                    dropout=0.3
                )
                
                self.lipnet_model.load_state_dict(checkpoint['model_state_dict'])
                self.lipnet_model.to(self.device)
                self.lipnet_model.eval()
                
                lipnet_accuracy = checkpoint.get('best_val_accuracy', 0.0)
                print(f"✅ LipNet loaded: {self.lipnet_model.get_num_parameters():,} params, {lipnet_accuracy:.1%} accuracy")
                lipnet_loaded = True
                
            except Exception as e:
                print(f"❌ Failed to load LipNet: {e}")
        else:
            print(f"⚠️  LipNet model not found: {lipnet_path}")
        
        # Load Mobile3DTiny model
        if Path(mobile3d_path).exists():
            try:
                checkpoint = torch.load(mobile3d_path, map_location=self.device)
                
                self.mobile3d_model = Perfect10Mobile3DTiny(num_classes=10)
                self.mobile3d_model.load_state_dict(checkpoint['model_state_dict'])
                self.mobile3d_model.to(self.device)
                self.mobile3d_model.eval()
                
                mobile3d_accuracy = checkpoint.get('best_val_accuracy', 0.0)
                print(f"✅ Mobile3DTiny loaded: {self.mobile3d_model.get_num_parameters():,} params, {mobile3d_accuracy:.1%} accuracy")
                mobile3d_loaded = True
                
            except Exception as e:
                print(f"❌ Failed to load Mobile3DTiny: {e}")
        else:
            print(f"⚠️  Mobile3DTiny model not found: {mobile3d_path}")
        
        return lipnet_loaded, mobile3d_loaded
    
    def benchmark_inference_speed(self, num_iterations: int = 50) -> Dict:
        """Benchmark inference speed for both models"""
        
        print(f"\n⏱️  Benchmarking Inference Speed")
        print("=" * 30)
        
        # Create dummy input (64 frames, 112×112)
        dummy_input = torch.randn(1, 1, 64, 112, 112).to(self.device)
        
        results = {}
        
        # Benchmark LipNet
        if self.lipnet_model:
            print(f"🧠 Benchmarking LipNet...")
            
            # Warmup
            for _ in range(5):
                with torch.no_grad():
                    _ = self.lipnet_model(dummy_input)
            
            # Actual benchmark
            start_time = time.time()
            for _ in range(num_iterations):
                with torch.no_grad():
                    _ = self.lipnet_model(dummy_input)
            end_time = time.time()
            
            lipnet_time = (end_time - start_time) / num_iterations
            results['lipnet'] = {
                'avg_inference_time': lipnet_time,
                'fps': 1.0 / lipnet_time,
                'parameters': self.lipnet_model.get_num_parameters()
            }
            
            print(f"   Avg inference time: {lipnet_time*1000:.2f}ms")
            print(f"   FPS: {1.0/lipnet_time:.1f}")
        
        # Benchmark Mobile3DTiny
        if self.mobile3d_model:
            print(f"📱 Benchmarking Mobile3DTiny...")
            
            # Warmup
            for _ in range(5):
                with torch.no_grad():
                    _ = self.mobile3d_model(dummy_input)
            
            # Actual benchmark
            start_time = time.time()
            for _ in range(num_iterations):
                with torch.no_grad():
                    _ = self.mobile3d_model(dummy_input)
            end_time = time.time()
            
            mobile3d_time = (end_time - start_time) / num_iterations
            results['mobile3d'] = {
                'avg_inference_time': mobile3d_time,
                'fps': 1.0 / mobile3d_time,
                'parameters': self.mobile3d_model.get_num_parameters()
            }
            
            print(f"   Avg inference time: {mobile3d_time*1000:.2f}ms")
            print(f"   FPS: {1.0/mobile3d_time:.1f}")
        
        return results
    
    def evaluate_on_dataset(self, manifest_path: str) -> Dict:
        """Evaluate both models on the Perfect 10 dataset"""
        
        print(f"\n📊 Evaluating Models on Perfect 10 Dataset")
        print("=" * 45)
        
        if not Path(manifest_path).exists():
            print(f"❌ Manifest not found: {manifest_path}")
            return {}
        
        # Load manifest
        manifest_df = pd.read_csv(manifest_path)
        print(f"📋 Loaded {len(manifest_df)} videos for evaluation")
        
        results = {}
        
        # Evaluate LipNet
        if self.lipnet_model:
            print(f"\n🧠 Evaluating LipNet...")
            lipnet_results = self._evaluate_model(self.lipnet_model, manifest_df, "LipNet")
            results['lipnet'] = lipnet_results
        
        # Evaluate Mobile3DTiny
        if self.mobile3d_model:
            print(f"\n📱 Evaluating Mobile3DTiny...")
            mobile3d_results = self._evaluate_model(self.mobile3d_model, manifest_df, "Mobile3DTiny")
            results['mobile3d'] = mobile3d_results
        
        return results
    
    def _evaluate_model(self, model, manifest_df: pd.DataFrame, model_name: str) -> Dict:
        """Evaluate a single model on the dataset"""
        
        model.eval()
        
        all_predictions = []
        all_labels = []
        all_confidences = []
        failed_videos = []
        
        for idx, row in manifest_df.iterrows():
            video_path = row['video_path']
            phrase = row['phrase']
            true_label = self.phrase_to_idx[phrase]
            
            try:
                # Process video
                video_tensor = self.video_processor.process_video(video_path)
                video_batch = video_tensor.unsqueeze(0).to(self.device)
                
                # Get prediction
                with torch.no_grad():
                    outputs = model(video_batch)
                    probabilities = F.softmax(outputs, dim=1)
                    
                    predicted_class = torch.argmax(probabilities, dim=1).item()
                    confidence = torch.max(probabilities, dim=1)[0].item()
                
                all_predictions.append(predicted_class)
                all_labels.append(true_label)
                all_confidences.append(confidence)
                
            except Exception as e:
                print(f"   ⚠️  Failed to process {Path(video_path).name}: {e}")
                failed_videos.append(video_path)
        
        # Calculate metrics
        accuracy = accuracy_score(all_labels, all_predictions)
        f1 = f1_score(all_labels, all_predictions, average='weighted')
        
        # Per-class accuracy
        cm = confusion_matrix(all_labels, all_predictions)
        per_class_accuracy = cm.diagonal() / cm.sum(axis=1)
        
        # Confidence statistics
        avg_confidence = np.mean(all_confidences)
        confidence_std = np.std(all_confidences)
        
        print(f"   Overall accuracy: {accuracy:.1%}")
        print(f"   F1 score: {f1:.3f}")
        print(f"   Average confidence: {avg_confidence:.1%}")
        print(f"   Failed videos: {len(failed_videos)}")
        
        return {
            'model_name': model_name,
            'accuracy': accuracy,
            'f1_score': f1,
            'per_class_accuracy': per_class_accuracy.tolist(),
            'confusion_matrix': cm.tolist(),
            'avg_confidence': avg_confidence,
            'confidence_std': confidence_std,
            'predictions': all_predictions,
            'labels': all_labels,
            'confidences': all_confidences,
            'failed_videos': failed_videos,
            'total_videos': len(manifest_df),
            'successful_videos': len(all_predictions)
        }
    
    def generate_comparison_report(self, evaluation_results: Dict, speed_results: Dict) -> Dict:
        """Generate comprehensive comparison report"""
        
        print(f"\n📋 Generating Comprehensive Comparison Report")
        print("=" * 50)
        
        report = {
            'comparison_summary': {},
            'detailed_metrics': {},
            'recommendations': {}
        }
        
        # Summary comparison
        if 'lipnet' in evaluation_results and 'mobile3d' in evaluation_results:
            lipnet_acc = evaluation_results['lipnet']['accuracy']
            mobile3d_acc = evaluation_results['mobile3d']['accuracy']
            
            accuracy_improvement = lipnet_acc - mobile3d_acc
            
            report['comparison_summary'] = {
                'lipnet_accuracy': lipnet_acc,
                'mobile3d_accuracy': mobile3d_acc,
                'accuracy_improvement': accuracy_improvement,
                'lipnet_confidence': evaluation_results['lipnet']['avg_confidence'],
                'mobile3d_confidence': evaluation_results['mobile3d']['avg_confidence'],
                'winner': 'LipNet' if lipnet_acc > mobile3d_acc else 'Mobile3DTiny'
            }
            
            print(f"🏆 ACCURACY COMPARISON:")
            print(f"   LipNet:       {lipnet_acc:.1%}")
            print(f"   Mobile3DTiny: {mobile3d_acc:.1%}")
            print(f"   Improvement:  {accuracy_improvement:+.1%}")
        
        # Speed comparison
        if 'lipnet' in speed_results and 'mobile3d' in speed_results:
            lipnet_time = speed_results['lipnet']['avg_inference_time']
            mobile3d_time = speed_results['mobile3d']['avg_inference_time']
            
            speed_ratio = lipnet_time / mobile3d_time
            
            print(f"\n⏱️  SPEED COMPARISON:")
            print(f"   LipNet:       {lipnet_time*1000:.2f}ms")
            print(f"   Mobile3DTiny: {mobile3d_time*1000:.2f}ms")
            print(f"   Speed ratio:  {speed_ratio:.1f}x slower")
        
        # Model size comparison
        if 'lipnet' in speed_results and 'mobile3d' in speed_results:
            lipnet_params = speed_results['lipnet']['parameters']
            mobile3d_params = speed_results['mobile3d']['parameters']
            
            param_ratio = lipnet_params / mobile3d_params
            
            print(f"\n📊 MODEL SIZE COMPARISON:")
            print(f"   LipNet:       {lipnet_params:,} parameters")
            print(f"   Mobile3DTiny: {mobile3d_params:,} parameters")
            print(f"   Size ratio:   {param_ratio:.1f}x larger")
        
        # Recommendations
        recommendations = []
        
        if 'lipnet' in evaluation_results and 'mobile3d' in evaluation_results:
            if evaluation_results['lipnet']['accuracy'] > 0.95:
                recommendations.append("✅ LipNet achieves >95% target accuracy")
            
            if evaluation_results['lipnet']['accuracy'] > evaluation_results['mobile3d']['accuracy']:
                recommendations.append("🏆 LipNet provides superior accuracy for Perfect 10 phrases")
            
            if accuracy_improvement > 0.05:
                recommendations.append("📈 Significant accuracy improvement justifies LipNet adoption")
        
        if 'mobile3d' in speed_results and 'lipnet' in speed_results:
            if speed_results['mobile3d']['avg_inference_time'] < speed_results['lipnet']['avg_inference_time']:
                recommendations.append("⚡ Mobile3DTiny offers faster inference for real-time applications")
        
        report['recommendations'] = recommendations
        
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in recommendations:
            print(f"   {rec}")
        
        return report
    
    def save_comparison_results(self, report: Dict, output_path: str):
        """Save comparison results to file"""
        
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n💾 Comparison results saved: {output_path}")

def main():
    """Main comparison function"""
    
    print("🔍 LipNet vs Mobile3DTiny Performance Comparison")
    print("=" * 55)
    print("Comprehensive analysis of accuracy, speed, and deployment considerations")
    
    # Configuration
    lipnet_model_path = "checkpoints/lipnet_perfect_10/best_lipnet_perfect_10_model.pth"
    mobile3d_model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
    manifest_path = "perfect_10_phrases_manifest.csv"
    
    # Create comparator
    comparator = ModelPerformanceComparator()
    
    # Load models
    lipnet_loaded, mobile3d_loaded = comparator.load_models(lipnet_model_path, mobile3d_model_path)
    
    if not (lipnet_loaded or mobile3d_loaded):
        print("❌ No models loaded for comparison")
        return
    
    # Benchmark inference speed
    speed_results = comparator.benchmark_inference_speed()
    
    # Evaluate on dataset
    evaluation_results = comparator.evaluate_on_dataset(manifest_path)
    
    # Generate comprehensive report
    report = comparator.generate_comparison_report(evaluation_results, speed_results)
    
    # Save results
    output_path = "lipnet_vs_mobile3d_comparison_report.json"
    comparator.save_comparison_results(report, output_path)
    
    print(f"\n🎉 Performance Comparison Complete!")
    print("=" * 40)
    print(f"✅ Models benchmarked and evaluated")
    print(f"✅ Comprehensive report generated")
    print(f"✅ Results saved for deployment decisions")

if __name__ == '__main__':
    main()
