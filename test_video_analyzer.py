#!/usr/bin/env python3
"""
Test Video Analyzer for Perfect 10 ICU Lipreading Classifier
Analyzes test videos using the trained Perfect 10 model
"""

import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import sys
import json
from typing import Dict, List, Tuple

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class Perfect10VideoAnalyzer:
    """Analyzer for testing videos with Perfect 10 model"""
    
    def __init__(self, model_path: str):
        """Initialize the video analyzer"""
        
        self.model_path = Path(model_path)
        self.device = torch.device('cpu')  # Use CPU for consistency
        
        # Perfect 10 phrases (in order)
        self.perfect_phrases = [
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today"
        ]
        
        # Create phrase mappings
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        
        # Initialize components
        self.model = None
        self.video_processor = None
        
        print(f"🎯 Perfect 10 Video Analyzer Initialized")
        print(f"   Model path: {self.model_path}")
        print(f"   Device: {self.device}")
        print(f"   Perfect phrases: {len(self.perfect_phrases)}")
    
    def load_model(self) -> bool:
        """Load the trained Perfect 10 model"""
        
        print(f"\n🤖 Loading Perfect 10 Model")
        print("=" * 30)
        
        if not self.model_path.exists():
            print(f"❌ Model checkpoint not found: {self.model_path}")
            return False
        
        try:
            # Load checkpoint
            checkpoint = torch.load(self.model_path, map_location=self.device)
            
            # Create model
            self.model = Perfect10Mobile3DTiny(num_classes=10)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            # Get training info
            best_val_accuracy = checkpoint.get('best_val_accuracy', 0.0)
            epoch = checkpoint.get('epoch', 0)
            
            print(f"✅ Perfect 10 model loaded successfully")
            print(f"   Parameters: {self.model.get_num_parameters():,}")
            print(f"   Training epoch: {epoch}")
            print(f"   Best validation accuracy: {best_val_accuracy:.1%}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def initialize_video_processor(self):
        """Initialize video processor with same settings as training"""
        
        self.video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True
        )
        
        print(f"✅ Video processor initialized")
        print(f"   Target frames: 32")
        print(f"   Target size: 96×96")
        print(f"   Grayscale: True")
    
    def analyze_video(self, video_path: str) -> Dict:
        """Analyze a test video and return predictions"""
        
        print(f"\n🎬 Analyzing Test Video")
        print("=" * 25)
        print(f"📹 Video: {Path(video_path).name}")
        
        if not Path(video_path).exists():
            return {
                'success': False,
                'error': f'Video file not found: {video_path}'
            }
        
        try:
            # Process video
            print(f"🔄 Processing video through pipeline...")
            video_tensor = self.video_processor.process_video(video_path)
            
            print(f"✅ Video processed successfully")
            print(f"   Input shape: {video_tensor.shape}")
            print(f"   Value range: [{video_tensor.min():.3f}, {video_tensor.max():.3f}]")
            
            # Add batch dimension
            video_batch = video_tensor.unsqueeze(0).to(self.device)
            
            # Get prediction
            print(f"\n🔮 Generating predictions...")
            with torch.no_grad():
                outputs = self.model(video_batch)
                probabilities = F.softmax(outputs, dim=1)
                
                # Get top-3 predictions
                top3_probs, top3_indices = torch.topk(probabilities, 3, dim=1)
                
                top3_probs = top3_probs[0].cpu().numpy()
                top3_indices = top3_indices[0].cpu().numpy()
                
                # Convert to phrases
                top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
                
                # Get all predictions for analysis
                all_probs = probabilities[0].cpu().numpy()
                
                return {
                    'success': True,
                    'video_path': video_path,
                    'video_shape': video_tensor.shape,
                    'top_prediction': top3_phrases[0],
                    'top_confidence': float(top3_probs[0]),
                    'top3_phrases': top3_phrases,
                    'top3_probabilities': top3_probs.tolist(),
                    'all_probabilities': all_probs.tolist(),
                    'raw_outputs': outputs[0].cpu().numpy().tolist()
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'traceback': str(e)
            }
    
    def display_results(self, results: Dict):
        """Display analysis results in a formatted way"""
        
        if not results['success']:
            print(f"❌ Analysis failed: {results['error']}")
            return
        
        print(f"\n🎯 Perfect 10 Prediction Results")
        print("=" * 35)
        
        # Top prediction
        top_phrase = results['top_prediction']
        top_confidence = results['top_confidence']
        
        print(f"🏆 **TOP PREDICTION**")
        print(f"   Phrase: \"{top_phrase.title()}\"")
        print(f"   Confidence: {top_confidence:.1%}")
        
        # Confidence level assessment
        if top_confidence >= 0.7:
            confidence_level = "🟢 Very High"
        elif top_confidence >= 0.5:
            confidence_level = "🟡 Moderate"
        elif top_confidence >= 0.3:
            confidence_level = "🟠 Low"
        else:
            confidence_level = "🔴 Very Low"
        
        print(f"   Confidence Level: {confidence_level}")
        
        # Top-3 predictions
        print(f"\n📊 **TOP-3 PREDICTIONS**")
        for i, (phrase, prob) in enumerate(zip(results['top3_phrases'], results['top3_probabilities'])):
            rank_emoji = ["🥇", "🥈", "🥉"][i]
            print(f"   {rank_emoji} {phrase.title()}: {prob:.1%}")
        
        # All predictions summary
        print(f"\n📋 **ALL PREDICTIONS SUMMARY**")
        all_probs = results['all_probabilities']
        for i, (phrase, prob) in enumerate(zip(self.perfect_phrases, all_probs)):
            if prob >= 0.05:  # Only show predictions above 5%
                print(f"   {phrase.title()}: {prob:.1%}")
        
        # Technical details
        print(f"\n🔧 **TECHNICAL DETAILS**")
        print(f"   Video shape: {results['video_shape']}")
        print(f"   Model: Perfect10Mobile3DTiny")
        print(f"   Classes: 10 Perfect ICU phrases")
        print(f"   Processing: Mouth-cropped → 96×96 → 32 frames → Grayscale")
    
    def save_results(self, results: Dict, output_path: str):
        """Save analysis results to file"""
        
        if results['success']:
            # Add metadata
            results['analyzer_info'] = {
                'model_type': 'Perfect10Mobile3DTiny',
                'num_classes': 10,
                'perfect_phrases': self.perfect_phrases,
                'preprocessing': {
                    'target_frames': 32,
                    'target_size': [96, 96],
                    'grayscale': True,
                    'normalization': '[-1, 1]'
                }
            }
            
            # Save to JSON
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"💾 Results saved: {output_path}")

def main():
    """Main analysis function"""
    
    print("🎬 Perfect 10 ICU Lipreading Video Analyzer")
    print("=" * 50)
    
    # Configuration
    model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
    test_video_path = "/Users/<USER>/Desktop/test 1.webm"
    
    # Initialize analyzer
    analyzer = Perfect10VideoAnalyzer(model_path)
    
    # Load model
    if not analyzer.load_model():
        print("❌ Failed to load Perfect 10 model. Make sure training has completed.")
        return
    
    # Initialize video processor
    analyzer.initialize_video_processor()
    
    # Check if test video exists
    if not Path(test_video_path).exists():
        print(f"❌ Test video not found: {test_video_path}")
        print(f"📁 Please ensure 'test 1.webm' is on the desktop")
        return
    
    # Analyze test video
    print(f"\n🎯 Analyzing Test Video: {Path(test_video_path).name}")
    results = analyzer.analyze_video(test_video_path)
    
    # Display results
    analyzer.display_results(results)
    
    # Save results
    if results['success']:
        output_path = "test_video_analysis_results.json"
        analyzer.save_results(results, output_path)
        
        print(f"\n🎉 Analysis Complete!")
        print("=" * 20)
        print(f"✅ Test video analyzed successfully")
        print(f"✅ Results displayed above")
        print(f"✅ Detailed results saved to: {output_path}")
    else:
        print(f"\n❌ Analysis failed")

if __name__ == '__main__':
    main()
