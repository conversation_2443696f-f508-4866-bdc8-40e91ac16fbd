#!/usr/bin/env python3
"""
LipNet Perfect 10 Rescue Training Analysis and Model Testing
Comprehensive analysis of the trained LipNet model and comparison with previous implementations
"""

import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import sys
import json
import time
from typing import Dict, List

# Add current directory to path
sys.path.append('.')

from lipnet_perfect_10 import LipNetPerfect10, LipNetPerfect10Manager
from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class LipNetRescueAnalyzer:
    """Comprehensive analyzer for LipNet Perfect 10 Rescue model"""
    
    def __init__(self):
        """Initialize the LipNet Rescue analyzer"""
        
        self.device = torch.device('cpu')
        
        # Perfect 10 phrases
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        
        # Model paths
        self.lipnet_rescue_path = "checkpoints/lipnet_perfect_10_rescue/best_lipnet_perfect_10_rescue_model.pth"
        self.mobile3d_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
        
        # Models
        self.lipnet_model = None
        self.mobile3d_model = None
        
        print(f"🎯 LipNet Perfect 10 Rescue Analyzer Initialized")
        print(f"   Perfect phrases: {len(self.perfect_phrases)}")
        print(f"   Device: {self.device}")
    
    def analyze_training_results(self) -> Dict:
        """Analyze LipNet Rescue training results"""
        
        print(f"\n📊 LipNet Perfect 10 Rescue Training Analysis")
        print("=" * 50)
        
        if not Path(self.lipnet_rescue_path).exists():
            return {
                'success': False,
                'error': f'LipNet Rescue model not found: {self.lipnet_rescue_path}'
            }
        
        # Load checkpoint
        checkpoint = torch.load(self.lipnet_rescue_path, map_location=self.device)
        
        analysis = {
            'success': True,
            'model_info': checkpoint.get('model_info', {}),
            'best_val_accuracy': checkpoint.get('best_val_accuracy', 0.0),
            'epoch': checkpoint.get('epoch', 0),
            'training_history': checkpoint.get('training_history', {}),
            'architecture': 'LipNet Perfect 10 Rescue',
            'parameters': checkpoint['model_info'].get('parameters', 0),
            'dataset': checkpoint['model_info'].get('dataset', 'Unknown'),
            'target_accuracy': checkpoint['model_info'].get('target_accuracy', '>95%')
        }
        
        print(f"✅ LipNet Rescue Model Analysis:")
        print(f"   Architecture: {analysis['architecture']}")
        print(f"   Parameters: {analysis['parameters']:,}")
        print(f"   Dataset: {analysis['dataset']}")
        print(f"   Best validation accuracy: {analysis['best_val_accuracy']:.1%}")
        print(f"   Training epoch: {analysis['epoch']}")
        print(f"   Target accuracy: {analysis['target_accuracy']}")
        
        # Analyze training progress
        history = analysis['training_history']
        if history:
            train_accs = history.get('train_acc', [])
            val_accs = history.get('val_acc', [])
            
            if train_accs and val_accs:
                print(f"\n📈 Training Progress:")
                print(f"   Initial train accuracy: {train_accs[0]:.1%}")
                print(f"   Final train accuracy: {train_accs[-1]:.1%}")
                print(f"   Initial val accuracy: {val_accs[0]:.1%}")
                print(f"   Final val accuracy: {val_accs[-1]:.1%}")
                print(f"   Training epochs completed: {len(train_accs)}")
        
        return analysis
    
    def load_models(self) -> Dict:
        """Load both LipNet Rescue and Mobile3DTiny models for comparison"""
        
        print(f"\n🤖 Loading Models for Comparison")
        print("=" * 35)
        
        results = {
            'lipnet_loaded': False,
            'mobile3d_loaded': False,
            'lipnet_accuracy': 0.0,
            'mobile3d_accuracy': 0.0
        }
        
        # Load LipNet Rescue model
        if Path(self.lipnet_rescue_path).exists():
            try:
                checkpoint = torch.load(self.lipnet_rescue_path, map_location=self.device)
                
                manager = LipNetPerfect10Manager()
                self.lipnet_model = manager.create_model(
                    hidden_dim=256,
                    num_rnn_layers=2,
                    rnn_type='LSTM',
                    dropout=0.3
                )
                
                self.lipnet_model.load_state_dict(checkpoint['model_state_dict'])
                self.lipnet_model.to(self.device)
                self.lipnet_model.eval()
                
                results['lipnet_loaded'] = True
                results['lipnet_accuracy'] = checkpoint.get('best_val_accuracy', 0.0)
                
                print(f"✅ LipNet Rescue loaded: {self.lipnet_model.get_num_parameters():,} params, {results['lipnet_accuracy']:.1%} accuracy")
                
            except Exception as e:
                print(f"❌ LipNet Rescue loading failed: {e}")
        else:
            print(f"❌ LipNet Rescue model not found")
        
        # Load Mobile3DTiny model for comparison
        if Path(self.mobile3d_path).exists():
            try:
                checkpoint = torch.load(self.mobile3d_path, map_location=self.device)
                
                self.mobile3d_model = Perfect10Mobile3DTiny(num_classes=10)
                self.mobile3d_model.load_state_dict(checkpoint['model_state_dict'])
                self.mobile3d_model.to(self.device)
                self.mobile3d_model.eval()
                
                results['mobile3d_loaded'] = True
                results['mobile3d_accuracy'] = checkpoint.get('best_val_accuracy', 0.0)
                
                print(f"✅ Mobile3DTiny loaded: {self.mobile3d_model.get_num_parameters():,} params, {results['mobile3d_accuracy']:.1%} accuracy")
                
            except Exception as e:
                print(f"❌ Mobile3DTiny loading failed: {e}")
        else:
            print(f"❌ Mobile3DTiny model not found")
        
        return results
    
    def test_model_on_video(self, video_path: str, model, model_name: str) -> Dict:
        """Test a model on a specific video"""
        
        if not Path(video_path).exists():
            return {
                'success': False,
                'error': f'Video not found: {video_path}',
                'model_name': model_name
            }
        
        try:
            # Enhanced video processor
            video_processor = VideoProcessor(
                target_frames=64,
                target_size=(112, 112),
                grayscale=True,
                fps=25.0,
                mouth_crop=None,  # Adaptive based on video
                use_dataset_normalization=True
            )
            
            # Detect if pre-cropped
            is_pre_cropped = "_mouth_cropped" in Path(video_path).name or "processed_" in Path(video_path).name
            if not is_pre_cropped:
                video_processor.mouth_crop = (133, 0, 133, 100)
            
            # Process video
            start_time = time.time()
            video_tensor = video_processor.process_video(video_path)
            processing_time = (time.time() - start_time) * 1000
            
            # Ensure correct shape
            if video_tensor.shape != (1, 64, 112, 112):
                return {
                    'success': False,
                    'error': f'Shape mismatch: {video_tensor.shape}',
                    'model_name': model_name
                }
            
            # Model inference
            video_batch = video_tensor.unsqueeze(0).to(self.device)
            with torch.no_grad():
                logits = model(video_batch)
                probabilities = F.softmax(logits[0], dim=0)
            
            inference_time = (time.time() - start_time) * 1000
            
            # Get predictions
            top3_probs, top3_indices = torch.topk(probabilities, 3)
            top3_probs = top3_probs.cpu().numpy()
            top3_indices = top3_indices.cpu().numpy()
            top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
            
            return {
                'success': True,
                'model_name': model_name,
                'video_path': video_path,
                'video_name': Path(video_path).name,
                'processing_time_ms': processing_time,
                'inference_time_ms': inference_time,
                'tensor_shape': list(video_tensor.shape),
                'value_range': [float(video_tensor.min()), float(video_tensor.max())],
                'top_prediction': top3_phrases[0],
                'top_confidence': float(top3_probs[0]),
                'top3_phrases': top3_phrases,
                'top3_probabilities': top3_probs.tolist(),
                'all_probabilities': probabilities.cpu().numpy().tolist()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'model_name': model_name,
                'video_path': video_path
            }
    
    def compare_models_on_test_video(self, video_path: str) -> Dict:
        """Compare both models on the same test video"""
        
        print(f"\n🎬 Model Comparison on Test Video")
        print("=" * 35)
        print(f"Video: {Path(video_path).name}")
        
        results = {
            'video_path': video_path,
            'video_name': Path(video_path).name,
            'lipnet_result': None,
            'mobile3d_result': None,
            'comparison': {}
        }
        
        # Test LipNet Rescue
        if self.lipnet_model:
            print(f"\n🧠 Testing LipNet Rescue...")
            results['lipnet_result'] = self.test_model_on_video(video_path, self.lipnet_model, "LipNet Rescue")
            
            if results['lipnet_result']['success']:
                lipnet_res = results['lipnet_result']
                print(f"   ✅ Prediction: \"{lipnet_res['top_prediction'].title()}\" ({lipnet_res['top_confidence']:.1%})")
                print(f"   ⏱️  Inference: {lipnet_res['inference_time_ms']:.0f}ms")
            else:
                print(f"   ❌ Failed: {results['lipnet_result']['error']}")
        
        # Test Mobile3DTiny
        if self.mobile3d_model:
            print(f"\n📱 Testing Mobile3DTiny...")
            results['mobile3d_result'] = self.test_model_on_video(video_path, self.mobile3d_model, "Mobile3DTiny")
            
            if results['mobile3d_result']['success']:
                mobile_res = results['mobile3d_result']
                print(f"   ✅ Prediction: \"{mobile_res['top_prediction'].title()}\" ({mobile_res['top_confidence']:.1%})")
                print(f"   ⏱️  Inference: {mobile_res['inference_time_ms']:.0f}ms")
            else:
                print(f"   ❌ Failed: {results['mobile3d_result']['error']}")
        
        # Generate comparison
        if (results['lipnet_result'] and results['lipnet_result']['success'] and 
            results['mobile3d_result'] and results['mobile3d_result']['success']):
            
            lipnet_res = results['lipnet_result']
            mobile_res = results['mobile3d_result']
            
            results['comparison'] = {
                'same_prediction': lipnet_res['top_prediction'] == mobile_res['top_prediction'],
                'confidence_difference': lipnet_res['top_confidence'] - mobile_res['top_confidence'],
                'inference_time_difference': lipnet_res['inference_time_ms'] - mobile_res['inference_time_ms'],
                'lipnet_better_confidence': lipnet_res['top_confidence'] > mobile_res['top_confidence']
            }
            
            print(f"\n📊 Model Comparison:")
            print(f"   Same prediction: {'✅ Yes' if results['comparison']['same_prediction'] else '❌ No'}")
            print(f"   Confidence difference: {results['comparison']['confidence_difference']:+.1%}")
            print(f"   Inference time difference: {results['comparison']['inference_time_difference']:+.0f}ms")
            print(f"   LipNet better confidence: {'✅ Yes' if results['comparison']['lipnet_better_confidence'] else '❌ No'}")
        
        return results
    
    def generate_comprehensive_report(self, training_analysis: Dict, model_comparison: Dict) -> Dict:
        """Generate comprehensive analysis report"""
        
        print(f"\n📊 COMPREHENSIVE LIPNET RESCUE ANALYSIS REPORT")
        print("=" * 55)
        
        report = {
            'training_analysis': training_analysis,
            'model_comparison': model_comparison,
            'summary': {},
            'recommendations': []
        }
        
        # Training summary
        if training_analysis['success']:
            achieved_accuracy = training_analysis['best_val_accuracy']
            target_accuracy = 0.95  # >95% target
            
            report['summary']['training_completed'] = True
            report['summary']['achieved_accuracy'] = achieved_accuracy
            report['summary']['target_achieved'] = achieved_accuracy >= target_accuracy
            report['summary']['accuracy_gap'] = target_accuracy - achieved_accuracy
            
            print(f"🎯 Training Summary:")
            print(f"   ✅ LipNet Rescue training completed")
            print(f"   📊 Achieved accuracy: {achieved_accuracy:.1%}")
            print(f"   🎯 Target accuracy: {target_accuracy:.1%}")
            print(f"   {'✅ Target achieved' if report['summary']['target_achieved'] else '❌ Target not achieved'}")
            if not report['summary']['target_achieved']:
                print(f"   📉 Accuracy gap: {report['summary']['accuracy_gap']:.1%}")
        
        # Model comparison summary
        if model_comparison and 'comparison' in model_comparison:
            comp = model_comparison['comparison']
            report['summary']['models_compared'] = True
            report['summary']['same_prediction'] = comp['same_prediction']
            report['summary']['lipnet_better_confidence'] = comp['lipnet_better_confidence']
            
            print(f"\n🤖 Model Comparison Summary:")
            print(f"   📊 Both models tested: ✅ Yes")
            print(f"   🎯 Same prediction: {'✅ Yes' if comp['same_prediction'] else '❌ No'}")
            print(f"   💪 LipNet better confidence: {'✅ Yes' if comp['lipnet_better_confidence'] else '❌ No'}")
        
        # Generate recommendations
        recommendations = []
        
        if not report['summary'].get('target_achieved', False):
            recommendations.append("Continue training LipNet Rescue model to achieve >95% accuracy target")
            recommendations.append("Investigate and fix video shape mismatch issues in dataset")
            recommendations.append("Implement more robust data preprocessing and augmentation")
            recommendations.append("Consider increasing training epochs and fine-tuning hyperparameters")
        
        if training_analysis.get('best_val_accuracy', 0) > 0:
            recommendations.append("LipNet architecture shows promise with 30% accuracy in early training")
            recommendations.append("Address dataset quality issues for improved performance")
        
        recommendations.extend([
            "Fix video preprocessing pipeline to handle dimension inconsistencies",
            "Implement more comprehensive data validation and cleaning",
            "Consider ensemble methods combining LipNet and Mobile3DTiny",
            "Expand dataset with more diverse speakers and recording conditions"
        ])
        
        report['recommendations'] = recommendations
        
        print(f"\n💡 Recommendations:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
        
        return report

def main():
    """Main analysis function"""
    
    print("🎯 LipNet Perfect 10 Rescue Comprehensive Analysis")
    print("=" * 55)
    
    # Initialize analyzer
    analyzer = LipNetRescueAnalyzer()
    
    # Analyze training results
    training_analysis = analyzer.analyze_training_results()
    
    # Load models for comparison
    model_results = analyzer.load_models()
    
    # Test models on sample video
    test_video = "/Users/<USER>/Desktop/630pm.webm"
    model_comparison = analyzer.compare_models_on_test_video(test_video)
    
    # Generate comprehensive report
    comprehensive_report = analyzer.generate_comprehensive_report(training_analysis, model_comparison)
    
    # Save analysis results
    output_file = "lipnet_rescue_comprehensive_analysis.json"
    with open(output_file, 'w') as f:
        json.dump(comprehensive_report, f, indent=2, default=str)
    
    print(f"\n💾 Comprehensive analysis saved: {output_file}")
    print(f"\n🎉 LipNet Perfect 10 Rescue Analysis Complete!")

if __name__ == '__main__':
    main()
