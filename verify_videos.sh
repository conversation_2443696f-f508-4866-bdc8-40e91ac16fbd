#!/bin/bash
echo "🔍 LipNet Specification Verification"
echo "Target: 25 FPS, 75 frames, 140x46 resolution, 3.0s duration"
echo "=================================================="

count=0
compliant=0

for video in data/*/*.mp4; do
    if [[ "$video" == *"_processed.mp4" ]]; then
        count=$((count + 1))
        
        # Get video properties
        props=$(ffprobe -v quiet -show_entries stream=width,height,r_frame_rate -show_entries format=duration -count_frames -show_entries stream=nb_frames "$video" 2>/dev/null)
        
        width=$(echo "$props" | grep "width=" | cut -d'=' -f2)
        height=$(echo "$props" | grep "height=" | cut -d'=' -f2)
        fps=$(echo "$props" | grep "r_frame_rate=" | cut -d'=' -f2 | cut -d'/' -f1)
        frames=$(echo "$props" | grep "nb_frames=" | cut -d'=' -f2)
        duration=$(echo "$props" | grep "duration=" | cut -d'=' -f2 | cut -d'.' -f1)
        
        if [[ "$width" == "140" && "$height" == "46" && "$fps" == "25" && "$frames" == "75" && "$duration" == "3" ]]; then
            compliant=$((compliant + 1))
            status="✅"
        else
            status="❌"
        fi
        
        if [[ $count -le 5 ]]; then  # Show first 5 for verification
            echo "$status $(basename "$video"): ${width}x${height}, ${fps}fps, ${frames}frames, ${duration}s"
        fi
    fi
done

echo "=================================================="
echo "📊 Results: $compliant/$count videos are LipNet compliant"
