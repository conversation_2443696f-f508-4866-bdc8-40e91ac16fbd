#!/usr/bin/env python3
"""
Comprehensive analysis of ICU reference videos
"""

import os
import json
import subprocess
import pandas as pd
import re
from pathlib import Path
from datetime import datetime
import yaml

def parse_filename(filename):
    """Parse the structured filename to extract metadata"""
    
    # Pattern: phrase__useruser01__age__gender__ethnicity__timestamp.webm
    pattern = r'^(.+?)__useruser01__(.+?)__(.+?)__(.+?)__(\d{8}T\d{6})\.webm$'
    match = re.match(pattern, filename)
    
    if match:
        phrase_raw, age, gender, ethnicity, timestamp = match.groups()
        
        # Clean up phrase (replace underscores with spaces)
        phrase = phrase_raw.replace('_', ' ')
        
        # Parse timestamp
        try:
            dt = datetime.strptime(timestamp, '%Y%m%dT%H%M%S')
        except:
            dt = None
            
        return {
            'phrase': phrase,
            'age_group': age,
            'gender': gender,
            'ethnicity': ethnicity,
            'timestamp': timestamp,
            'datetime': dt,
            'speaker_id': 'useruser01'
        }
    
    return None

def get_video_properties(video_path):
    """Get technical properties of a video using ffprobe"""
    
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', str(video_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            
            # Extract video stream info
            video_stream = None
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if video_stream:
                format_info = data.get('format', {})
                
                return {
                    'width': video_stream.get('width'),
                    'height': video_stream.get('height'),
                    'codec': video_stream.get('codec_name'),
                    'fps': eval(video_stream.get('r_frame_rate', '0/1')),
                    'duration': float(format_info.get('duration', 0)),
                    'size_bytes': int(format_info.get('size', 0)),
                    'pixel_format': video_stream.get('pix_fmt'),
                    'has_audio': any(s.get('codec_type') == 'audio' for s in data.get('streams', []))
                }
    
    except Exception as e:
        print(f"Error analyzing {video_path}: {e}")
    
    return None

def analyze_reference_videos(video_dir):
    """Analyze all reference videos in the directory"""
    
    print("🔍 Analyzing ICU Reference Videos")
    print("=" * 50)
    
    video_dir = Path(video_dir)
    video_files = list(video_dir.glob("*.webm"))
    
    print(f"Found {len(video_files)} video files")
    
    # Analyze each video
    results = []
    
    for i, video_file in enumerate(video_files):
        print(f"Analyzing {i+1}/{len(video_files)}: {video_file.name}")
        
        # Parse filename metadata
        metadata = parse_filename(video_file.name)
        if not metadata:
            print(f"  ⚠️  Could not parse filename: {video_file.name}")
            continue
        
        # Get video properties
        properties = get_video_properties(video_file)
        if not properties:
            print(f"  ❌ Could not analyze video: {video_file.name}")
            continue
        
        # Combine metadata and properties
        result = {
            'filename': video_file.name,
            'filepath': str(video_file),
            **metadata,
            **properties
        }
        
        results.append(result)
        
        # Show brief info
        print(f"  ✅ {metadata['phrase']} | {properties['width']}x{properties['height']} | {properties['duration']:.1f}s")
    
    return results

def create_quality_report(results):
    """Create a comprehensive quality report"""
    
    if not results:
        print("❌ No valid videos to analyze")
        return
    
    df = pd.DataFrame(results)
    
    print(f"\n📊 Quality Analysis Report")
    print("=" * 50)
    
    # Basic statistics
    print(f"Total videos analyzed: {len(df)}")
    print(f"Unique phrases: {df['phrase'].nunique()}")
    print(f"Videos per phrase: {len(df) / df['phrase'].nunique():.1f} average")
    
    # Phrase distribution
    print(f"\n📝 Phrase Distribution:")
    phrase_counts = df['phrase'].value_counts()
    for phrase, count in phrase_counts.items():
        print(f"  {phrase}: {count} videos")
    
    # Technical specifications
    print(f"\n🎥 Technical Specifications:")
    print(f"  Resolution: {df['width'].mode().iloc[0] if not df['width'].mode().empty else 'N/A'}x{df['height'].mode().iloc[0] if not df['height'].mode().empty else 'N/A'} (most common)")
    print(f"  Frame rate: {df['fps'].mean():.1f} fps (average)")
    print(f"  Duration: {df['duration'].mean():.1f}s ± {df['duration'].std():.1f}s")
    print(f"  File size: {df['size_bytes'].mean()/1024:.0f} KB (average)")
    print(f"  Codec: {df['codec'].mode().iloc[0] if not df['codec'].mode().empty else 'N/A'}")
    print(f"  Pixel format: {df['pixel_format'].mode().iloc[0] if not df['pixel_format'].mode().empty else 'N/A'}")
    
    # Quality issues
    print(f"\n⚠️  Quality Issues:")
    
    # Resolution consistency
    unique_resolutions = df[['width', 'height']].drop_duplicates()
    if len(unique_resolutions) > 1:
        print(f"  ❌ Inconsistent resolutions found:")
        for _, row in unique_resolutions.iterrows():
            count = len(df[(df['width'] == row['width']) & (df['height'] == row['height'])])
            print(f"     {row['width']}x{row['height']}: {count} videos")
    else:
        print(f"  ✅ All videos have consistent resolution")
    
    # Duration consistency
    duration_std = df['duration'].std()
    if duration_std > 1.0:  # More than 1 second variation
        print(f"  ⚠️  High duration variation (std: {duration_std:.1f}s)")
        print(f"     Range: {df['duration'].min():.1f}s - {df['duration'].max():.1f}s")
    else:
        print(f"  ✅ Duration is consistent (std: {duration_std:.1f}s)")
    
    # Frame rate consistency
    unique_fps = df['fps'].unique()
    if len(unique_fps) > 1:
        print(f"  ⚠️  Inconsistent frame rates: {unique_fps}")
    else:
        print(f"  ✅ Consistent frame rate: {unique_fps[0]:.1f} fps")
    
    # Missing phrases check
    expected_phrases = [
        "am i getting better", "i feel anxious", "i have a headache", "i m confused",
        "i m in pain", "i m uncomfortable", "i need a medication", "i need help",
        "i need to lie down", "i need to move", "i need to sit up", "i need to use the toilet",
        "i want to phone my family", "i want to see my husband", "i want to see my wife",
        "my back hurts", "my chest hurts", "please explain again", "stay with me please",
        "what happened to me", "what time is my husband coming", "what time is my wife coming",
        "where am i", "where is my husband", "where is my wife", "who is with me today"
    ]
    
    found_phrases = set(df['phrase'].unique())
    expected_phrases_set = set(expected_phrases)
    
    missing_phrases = expected_phrases_set - found_phrases
    extra_phrases = found_phrases - expected_phrases_set
    
    if missing_phrases:
        print(f"  ❌ Missing phrases ({len(missing_phrases)}):")
        for phrase in sorted(missing_phrases):
            print(f"     - {phrase}")
    
    if extra_phrases:
        print(f"  ⚠️  Extra phrases ({len(extra_phrases)}):")
        for phrase in sorted(extra_phrases):
            print(f"     - {phrase}")
    
    if not missing_phrases and not extra_phrases:
        print(f"  ✅ All expected phrases present")
    
    return df

def save_reference_manifest(df, output_path):
    """Save the reference video manifest"""
    
    if df is None or len(df) == 0:
        print("❌ No data to save")
        return
    
    # Create manifest with required columns
    manifest_df = df[['filepath', 'phrase', 'speaker_id', 'age_group', 'gender', 'ethnicity']].copy()
    manifest_df.columns = ['video_path', 'phrase', 'speaker_id', 'age_group', 'gender', 'ethnicity']
    
    # Save to CSV
    manifest_df.to_csv(output_path, index=False)
    print(f"\n💾 Reference manifest saved to: {output_path}")
    print(f"   {len(manifest_df)} videos cataloged")

def main():
    """Main analysis function"""
    
    # Analyze videos
    video_dir = "/Users/<USER>/Desktop/icu-videos-today"
    results = analyze_reference_videos(video_dir)
    
    if not results:
        print("❌ No videos found or analyzed")
        return
    
    # Create quality report
    df = create_quality_report(results)
    
    # Save manifest
    output_dir = Path("/Users/<USER>/Desktop/app dev 23.5.25")
    manifest_path = output_dir / "reference_videos_manifest.csv"
    save_reference_manifest(df, manifest_path)
    
    # Save detailed analysis
    analysis_path = output_dir / "reference_videos_analysis.json"
    with open(analysis_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    print(f"💾 Detailed analysis saved to: {analysis_path}")
    
    print(f"\n🎯 Summary:")
    print(f"   ✅ Found {len(results)} reference videos")
    print(f"   ✅ Covering {df['phrase'].nunique()} unique phrases")
    print(f"   ✅ Quality analysis complete")
    print(f"   ✅ Reference manifest created")

if __name__ == '__main__':
    main()
