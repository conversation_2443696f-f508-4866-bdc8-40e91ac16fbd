#!/usr/bin/env python3
"""
Custom data loader for focused 13-phrase ICU lipreading training
Handles stratified splitting for balanced phrase representation
"""

import pandas as pd
import torch
from torch.utils.data import DataLoader, Dataset
from sklearn.model_selection import train_test_split
import numpy as np
from pathlib import Path
import sys
from typing import Dict, List, Tuple
from collections import defaultdict

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

class FocusedVideoDataset(Dataset):
    """Dataset for focused 13-phrase training"""
    
    def __init__(self, video_paths: List[str], labels: List[int], 
                 video_processor: VideoProcessor, phrases: List[str]):
        self.video_paths = video_paths
        self.labels = labels
        self.video_processor = video_processor
        self.phrases = phrases
        
    def __len__(self):
        return len(self.video_paths)
    
    def __getitem__(self, idx):
        video_path = self.video_paths[idx]
        label = self.labels[idx]
        phrase = self.phrases[idx]
        
        # Process video
        try:
            video_tensor = self.video_processor.process_video(video_path)
        except Exception as e:
            print(f"Error processing {video_path}: {e}")
            # Return zeros if processing fails
            video_tensor = torch.zeros(1, 32, 96, 96)
        
        metadata = {
            'video_path': video_path,
            'phrase': phrase,
            'label': label
        }
        
        return video_tensor, torch.tensor(label, dtype=torch.long), metadata

def create_focused_dataloaders(config: Dict, manifest_path: str):
    """Create data loaders for focused 13-phrase training with stratified splitting"""
    
    print(f"📊 Creating Focused 13-Phrase Data Loaders")
    print("=" * 45)
    
    # Load manifest
    manifest_df = pd.read_csv(manifest_path)
    print(f"📋 Loaded manifest: {len(manifest_df)} videos")
    
    # Extract data
    video_paths = manifest_df['video_path'].tolist()
    phrases = manifest_df['phrase'].tolist()
    
    # Verify video files exist
    existing_paths = []
    existing_phrases = []
    
    for path, phrase in zip(video_paths, phrases):
        if Path(path).exists():
            existing_paths.append(path)
            existing_phrases.append(phrase)
        else:
            print(f"⚠️  Video not found: {path}")
    
    print(f"✅ Found {len(existing_paths)} existing videos")
    
    # Create phrase mapping for 13 classes
    unique_phrases = sorted(list(set(existing_phrases)))
    phrase_to_idx = {phrase: idx for idx, phrase in enumerate(unique_phrases)}
    labels = [phrase_to_idx[phrase] for phrase in existing_phrases]
    
    print(f"📊 Phrase distribution:")
    phrase_counts = {}
    for phrase in existing_phrases:
        phrase_counts[phrase] = phrase_counts.get(phrase, 0) + 1
    
    for phrase, count in sorted(phrase_counts.items()):
        print(f"   {phrase.title()}: {count} videos")
    
    # Stratified splitting for balanced representation
    train_ratio = config['data']['train_ratio']
    val_ratio = config['data']['val_ratio']
    test_ratio = config['data']['test_ratio']
    
    # Ensure ratios sum to 1
    total_ratio = train_ratio + val_ratio + test_ratio
    train_ratio /= total_ratio
    val_ratio /= total_ratio
    test_ratio /= total_ratio
    
    print(f"📊 Data splits: Train {train_ratio:.2f}, Val {val_ratio:.2f}, Test {test_ratio:.2f}")
    
    # Stratified splitting by phrase
    if config['data'].get('stratify_by_phrase', True):
        print(f"🎯 Using stratified splitting by phrase")
        
        # Group videos by phrase
        phrase_groups = defaultdict(list)
        for i, phrase in enumerate(existing_phrases):
            phrase_groups[phrase].append(i)
        
        train_indices = []
        val_indices = []
        test_indices = []
        
        # Split each phrase group proportionally
        for phrase, indices in phrase_groups.items():
            n_videos = len(indices)
            
            # Calculate split sizes
            n_test = max(1, int(n_videos * test_ratio))
            n_val = max(1, int(n_videos * val_ratio))
            n_train = n_videos - n_test - n_val
            
            # Ensure we have at least 1 video in each split if possible
            if n_videos >= 3:
                if n_train < 1:
                    n_train = 1
                    n_val = max(1, n_videos - n_train - n_test)
                    n_test = n_videos - n_train - n_val
            
            # Shuffle indices for this phrase
            np.random.seed(config['data'].get('random_seed', 42))
            shuffled_indices = np.random.permutation(indices)
            
            # Split indices
            test_end = n_test
            val_end = test_end + n_val
            
            test_indices.extend(shuffled_indices[:test_end])
            val_indices.extend(shuffled_indices[test_end:val_end])
            train_indices.extend(shuffled_indices[val_end:])
            
            print(f"   {phrase.title()}: {n_train} train, {n_val} val, {n_test} test")
        
        # Extract data for each split
        train_paths = [existing_paths[i] for i in train_indices]
        train_phrases = [existing_phrases[i] for i in train_indices]
        train_labels = [labels[i] for i in train_indices]
        
        val_paths = [existing_paths[i] for i in val_indices]
        val_phrases = [existing_phrases[i] for i in val_indices]
        val_labels = [labels[i] for i in val_indices]
        
        test_paths = [existing_paths[i] for i in test_indices]
        test_phrases = [existing_phrases[i] for i in test_indices]
        test_labels = [labels[i] for i in test_indices]
        
    else:
        # Random splitting (fallback)
        print(f"🔀 Using random splitting")
        
        # First split: separate test set
        train_val_paths, test_paths, train_val_phrases, test_phrases, train_val_labels, test_labels = train_test_split(
            existing_paths, existing_phrases, labels,
            test_size=test_ratio,
            random_state=config['data'].get('random_seed', 42),
            stratify=existing_phrases if len(set(existing_phrases)) > 1 else None
        )
        
        # Second split: separate train and validation
        val_size_adjusted = val_ratio / (train_ratio + val_ratio)
        
        if len(train_val_paths) > 1:
            train_paths, val_paths, train_phrases, val_phrases, train_labels, val_labels = train_test_split(
                train_val_paths, train_val_phrases, train_val_labels,
                test_size=val_size_adjusted,
                random_state=config['data'].get('random_seed', 42),
                stratify=train_val_phrases if len(set(train_val_phrases)) > 1 else None
            )
        else:
            train_paths, train_phrases, train_labels = train_val_paths, train_val_phrases, train_val_labels
            val_paths, val_phrases, val_labels = [], [], []
    
    print(f"📊 Final splits:")
    print(f"   Train: {len(train_paths)} videos")
    print(f"   Val: {len(val_paths)} videos")
    print(f"   Test: {len(test_paths)} videos")
    
    # Create video processor
    video_processor = VideoProcessor(
        target_frames=config['data']['frames'],
        target_size=(config['data']['height'], config['data']['width']),
        grayscale=config['data']['grayscale']
    )
    
    # Create datasets
    train_dataset = FocusedVideoDataset(train_paths, train_labels, video_processor, train_phrases)
    val_dataset = FocusedVideoDataset(val_paths, val_labels, video_processor, val_phrases)
    test_dataset = FocusedVideoDataset(test_paths, test_labels, video_processor, test_phrases)
    
    # Create data loaders
    batch_size = config['training']['batch_size']
    num_workers = config['hardware']['num_workers']
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=config['hardware'].get('pin_memory', True)
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=config['hardware'].get('pin_memory', True)
    ) if len(val_dataset) > 0 else None
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=config['hardware'].get('pin_memory', True)
    ) if len(test_dataset) > 0 else None
    
    # Create info dictionary
    data_info = {
        'train_size': len(train_dataset),
        'val_size': len(val_dataset),
        'test_size': len(test_dataset),
        'num_classes': len(unique_phrases),
        'phrase_to_idx': phrase_to_idx,
        'phrases': unique_phrases
    }
    
    print(f"✅ Focused data loaders created successfully")
    
    return train_loader, val_loader, test_loader, data_info

def test_focused_dataloaders():
    """Test the focused data loaders"""
    
    import yaml
    
    print("🧪 Testing Focused Data Loaders")
    print("=" * 35)
    
    # Load config
    with open('configs/focused_13_training.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Create data loaders
    try:
        train_loader, val_loader, test_loader, data_info = create_focused_dataloaders(
            config, config['data']['manifest_path']
        )
        
        print(f"✅ Data loaders created successfully")
        print(f"📊 Train: {data_info['train_size']} videos")
        print(f"📊 Val: {data_info['val_size']} videos")
        print(f"📊 Test: {data_info['test_size']} videos")
        print(f"📊 Classes: {data_info['num_classes']}")
        
        # Test first batch
        if train_loader:
            for videos, labels, metadata in train_loader:
                print(f"📊 Batch shape: {videos.shape}")
                print(f"📊 Label shape: {labels.shape}")
                print(f"📊 Video range: [{videos.min():.3f}, {videos.max():.3f}]")
                print(f"📊 Label range: [{labels.min()}, {labels.max()}]")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ Data loader test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_focused_dataloaders()
