#!/usr/bin/env python3
"""
Final Corrected Video Processor using exact VideoProcessor with proper mouth-cropping
Uses the same backend VideoProcessor with ICU reference training coordinates
"""

import cv2
import numpy as np
import torch
from pathlib import Path
import sys
import os

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

class FinalCorrectVideoProcessor:
    """Final corrected processor using exact VideoProcessor with mouth-cropping"""
    
    def __init__(self, input_folder: str, output_folder: str):
        """Initialize with exact VideoProcessor and ICU mouth-cropping"""
        
        self.input_folder = Path(input_folder)
        self.output_folder = Path(output_folder)
        
        # Create output folder
        self.output_folder.mkdir(parents=True, exist_ok=True)
        
        # Initialize VideoProcessor with ICU reference mouth-cropping coordinates
        # From automated_video_processor.py: crop_region=(133, 0, 133, 100)
        self.video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True,
            mouth_crop=(133, 0, 133, 100)  # ICU reference coordinates
        )
        
        print(f"🎯 Final Correct Video Processor Initialized")
        print(f"   Using exact backend VideoProcessor")
        print(f"   ICU mouth-cropping: (133, 0, 133, 100)")
        print(f"   Target: 32 frames, 96×96, grayscale")
    
    def process_single_video_final(self, input_path: str, output_path: str) -> dict:
        """Process single video with exact VideoProcessor"""
        
        video_name = Path(input_path).name
        print(f"\n🎬 Final processing: {video_name}")
        
        try:
            # Get original video info
            cap = cv2.VideoCapture(input_path)
            orig_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            orig_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            orig_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            cap.release()
            
            print(f"   📊 Original: {orig_width}×{orig_height}, {orig_frames} frames")
            
            # Process with exact VideoProcessor (same as Perfect 10 training)
            processed_tensor = self.video_processor.process_video(input_path)
            
            print(f"   ✅ VideoProcessor successful")
            print(f"   📊 Output tensor: {processed_tensor.shape}")
            print(f"   📊 Value range: [{processed_tensor.min():.3f}, {processed_tensor.max():.3f}]")
            
            # Convert tensor to video file for inspection
            # Tensor shape: (C, T, H, W) -> need (T, H, W)
            if processed_tensor.dim() == 4:
                # Remove channel dimension for grayscale
                frames = processed_tensor.squeeze(0).numpy()  # (T, H, W)
            else:
                frames = processed_tensor.numpy()
            
            # Convert from [0,1] to [0,255] for video writing
            frames_uint8 = (frames * 255).astype(np.uint8)
            
            print(f"   📊 Video frames: {frames_uint8.shape}, range [{frames_uint8.min()}, {frames_uint8.max()}]")
            
            # Create video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = 10.0  # Slower FPS for inspection
            out = cv2.VideoWriter(output_path, fourcc, fps, (96, 96), isColor=False)
            
            # Write frames
            for frame in frames_uint8:
                out.write(frame)
            
            out.release()
            
            # Verify output video
            cap_out = cv2.VideoCapture(output_path)
            out_width = int(cap_out.get(cv2.CAP_PROP_FRAME_WIDTH))
            out_height = int(cap_out.get(cv2.CAP_PROP_FRAME_HEIGHT))
            out_frames = int(cap_out.get(cv2.CAP_PROP_FRAME_COUNT))
            cap_out.release()
            
            print(f"   ✅ Saved: {Path(output_path).name} ({out_width}×{out_height}, {out_frames} frames)")
            
            # Check if output has meaningful content
            mean_intensity = np.mean(frames_uint8)
            has_content = mean_intensity > 5  # Not completely black
            
            return {
                'success': True,
                'input_path': input_path,
                'output_path': output_path,
                'original_specs': f"{orig_width}×{orig_height}, {orig_frames} frames",
                'processed_specs': f"{out_width}×{out_height}, {out_frames} frames",
                'tensor_shape': list(processed_tensor.shape),
                'value_range': [float(processed_tensor.min()), float(processed_tensor.max())],
                'mean_intensity': float(mean_intensity),
                'has_content': has_content,
                'mouth_crop_coords': "(133, 0, 133, 100)"
            }
            
        except Exception as e:
            print(f"   ❌ Processing failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'input_path': input_path,
                'output_path': output_path,
                'error': str(e)
            }
    
    def process_all_videos_final(self) -> list:
        """Process all videos with final correct method"""
        
        print(f"\n🎬 FINAL PROCESSING - Exact VideoProcessor + ICU Coordinates")
        print("=" * 60)
        
        results = []
        
        for i in range(1, 6):
            input_path = self.input_folder / f"{i}.webm"
            output_path = self.output_folder / f"processed_{i}.mp4"
            
            if input_path.exists():
                result = self.process_single_video_final(str(input_path), str(output_path))
                results.append(result)
            else:
                print(f"\n❌ Video not found: {i}.webm")
                results.append({
                    'success': False,
                    'input_path': str(input_path),
                    'output_path': str(output_path),
                    'error': 'Input file not found'
                })
        
        return results
    
    def create_final_verification_report(self, results: list):
        """Create final verification report"""
        
        print(f"\n📊 FINAL VERIFICATION REPORT - EXACT VIDEOPROCESSOR")
        print("=" * 55)
        
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        print(f"📈 Final Processing Summary:")
        print(f"   Total videos: {len(results)}")
        print(f"   Successfully processed: {len(successful_results)}")
        print(f"   Failed: {len(failed_results)}")
        print(f"   Success rate: {len(successful_results)/len(results)*100:.1f}%")
        
        if successful_results:
            print(f"\n📋 VideoProcessor Results:")
            print(f"{'Video':<12} {'Original':<20} {'Processed':<15} {'Content':<10} {'Mean Int':<10} {'Status'}")
            print(f"{'-'*12} {'-'*20} {'-'*15} {'-'*10} {'-'*10} {'-'*10}")
            
            for result in successful_results:
                content_status = "✅ YES" if result.get('has_content', False) else "❌ NO"
                mean_int = result.get('mean_intensity', 0)
                
                print(f"{Path(result['input_path']).name:<12} "
                      f"{result['original_specs']:<20} "
                      f"{result['processed_specs']:<15} "
                      f"{content_status:<10} "
                      f"{mean_int:<10.1f} "
                      f"✅ SUCCESS")
        
        if failed_results:
            print(f"\n❌ Failed Processing:")
            for result in failed_results:
                print(f"   {Path(result['input_path']).name}: {result['error']}")
        
        print(f"\n🎯 VideoProcessor Compliance Check:")
        if successful_results:
            all_correct_shape = all(len(r['tensor_shape']) == 4 for r in successful_results)
            all_correct_range = all(0 <= r['value_range'][0] and r['value_range'][1] <= 1 for r in successful_results)
            all_have_content = all(r.get('has_content', False) for r in successful_results)
            
            print(f"   ✅ Exact VideoProcessor used: YES")
            print(f"   ✅ ICU mouth-cropping (133, 0, 133, 100): APPLIED")
            print(f"   ✅ Tensor shape (C, T, H, W): {'PASSED' if all_correct_shape else 'FAILED'}")
            print(f"   ✅ Value range [0, 1]: {'PASSED' if all_correct_range else 'FAILED'}")
            print(f"   ✅ Content detected: {'PASSED' if all_have_content else 'FAILED'}")
            print(f"   ✅ 32 frames, 96×96, grayscale: APPLIED")
        
        print(f"\n📁 Final Output Files:")
        for result in successful_results:
            content_note = " (HAS CONTENT)" if result.get('has_content', False) else " (NO CONTENT)"
            print(f"   📄 {result['output_path']}{content_note}")
        
        print(f"\n🔍 Final Visual Inspection:")
        print("=" * 30)
        print(f"1. Videos processed with exact same VideoProcessor as Perfect 10 training")
        print(f"2. ICU reference mouth-cropping coordinates applied: (133, 0, 133, 100)")
        print(f"3. Output videos show exactly what Perfect 10 model receives")
        print(f"4. Compare lip movements to understand 'I Feel Anxious' predictions")
        print(f"5. Verify mouth region is properly cropped and centered")

def main():
    """Main function for final correct processing"""
    
    print("🎯 FINAL CORRECT VIDEO PROCESSING")
    print("=" * 40)
    print("Using exact VideoProcessor with ICU mouth-cropping coordinates")
    
    # Folder paths
    input_folder = "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on"
    output_folder = "/Users/<USER>/Desktop/processed_perfect_10_videos"
    
    # Initialize final processor
    processor = FinalCorrectVideoProcessor(input_folder, output_folder)
    
    # Process all videos with final correct method
    results = processor.process_all_videos_final()
    
    # Create final verification report
    processor.create_final_verification_report(results)
    
    # Open output folder
    try:
        os.system(f'open "{output_folder}"')
        print(f"\n✅ Final processed videos folder opened")
    except Exception as e:
        print(f"\n⚠️  Could not auto-open folder: {e}")
    
    # Try to play first video
    first_video = Path(output_folder) / "processed_1.mp4"
    if first_video.exists():
        try:
            os.system(f'open "{first_video}"')
            print(f"🎬 First processed video opened: {first_video.name}")
        except Exception as e:
            print(f"⚠️  Could not auto-play: {e}")
    
    print(f"\n🎉 FINAL PROCESSING COMPLETE!")
    print("=" * 35)
    print("✅ Videos processed with exact VideoProcessor")
    print("✅ ICU mouth-cropping coordinates applied")
    print("✅ Output matches Perfect 10 training pipeline")
    print("✅ Ready for definitive visual inspection")

if __name__ == '__main__':
    main()
