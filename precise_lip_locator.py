#!/usr/bin/env python3
"""
Precise Lip Locator for ICU Video Preprocessing
Analyzes original WebM videos to determine exact lip positioning for accurate mouth region detection.
"""

import cv2
import numpy as np
from pathlib import Path
import argparse
import sys

def extract_frames_from_video(video_path, num_frames=5):
    """Extract sample frames from video for analysis"""
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        raise ValueError(f"Cannot open video: {video_path}")
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"📹 Video: {width}x{height}, {total_frames} frames")
    
    # Extract frames at different positions
    frame_indices = np.linspace(0, total_frames-1, num_frames, dtype=int)
    frames = []
    
    for i, frame_idx in enumerate(frame_indices):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if ret:
            frames.append((frame_idx, frame))
            # Save frame for manual inspection
            cv2.imwrite(f"debug_frame_{i:02d}_idx{frame_idx}.jpg", frame)
            print(f"💾 Saved debug_frame_{i:02d}_idx{frame_idx}.jpg")
    
    cap.release()
    return frames, (width, height)

def analyze_lip_position_manual(frames, video_size):
    """Manual analysis of lip position in frames"""
    width, height = video_size
    print(f"\n🔍 MANUAL LIP POSITION ANALYSIS")
    print(f"Video size: {width}x{height}")
    print(f"Based on ICU dataset application code analysis:")
    print(f"- lipCenterY was set to video.videoHeight * 0.38 = {height * 0.38:.1f}")
    print(f"- Privacy line was at video.videoHeight * 0.57 = {height * 0.57:.1f}")
    print(f"- This suggests lips are around 38% down from top of frame")
    
    # Based on the ICU dataset application analysis
    estimated_lip_center_y = int(height * 0.38)
    estimated_lip_region_top = int(height * 0.30)  # 30% from top
    estimated_lip_region_bottom = int(height * 0.46)  # 46% from top
    
    print(f"\n📐 ESTIMATED LIP REGION:")
    print(f"- Lip center Y: {estimated_lip_center_y}")
    print(f"- Lip region top: {estimated_lip_region_top}")
    print(f"- Lip region bottom: {estimated_lip_region_bottom}")
    print(f"- Lip region height: {estimated_lip_region_bottom - estimated_lip_region_top}")
    
    # Create annotated frames showing estimated lip region
    for i, (frame_idx, frame) in enumerate(frames):
        annotated = frame.copy()
        
        # Draw estimated lip region
        cv2.rectangle(annotated, (0, estimated_lip_region_top), (width-1, estimated_lip_region_bottom), (0, 255, 0), 2)
        cv2.putText(annotated, "ESTIMATED LIP REGION", (10, estimated_lip_region_top-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Draw center line
        cv2.line(annotated, (0, estimated_lip_center_y), (width-1, estimated_lip_center_y), (255, 0, 0), 1)
        cv2.putText(annotated, f"Lip Center Y: {estimated_lip_center_y}", (10, estimated_lip_center_y-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        # Save annotated frame
        cv2.imwrite(f"annotated_frame_{i:02d}_lip_region.jpg", annotated)
        print(f"💾 Saved annotated_frame_{i:02d}_lip_region.jpg")
    
    return {
        "lip_center_y": estimated_lip_center_y,
        "lip_region_top": estimated_lip_region_top,
        "lip_region_bottom": estimated_lip_region_bottom,
        "lip_region_height": estimated_lip_region_bottom - estimated_lip_region_top
    }

def test_current_algorithm(video_path, lip_analysis):
    """Test what the current algorithm would extract"""
    print(f"\n🧪 TESTING CURRENT ALGORITHM")
    
    # Import the current enhanced video preprocessor
    sys.path.append('/Users/<USER>/Desktop/app dev 23.5.25')
    try:
        from enhanced_video_preprocessor import EnhancedVideoPreprocessor
        
        preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
        
        # Read a frame from the video
        cap = cv2.VideoCapture(str(video_path))
        cap.set(cv2.CAP_PROP_POS_FRAMES, 25)  # Middle frame
        ret, frame = cap.read()
        cap.release()
        
        if ret:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Get current algorithm's mouth region
            x1, y1, x2, y2 = preprocessor.detect_mouth_region(gray)
            
            print(f"Current algorithm mouth region: ({x1}, {y1}) to ({x2}, {y2})")
            print(f"Current region height: {y2 - y1}")
            print(f"Current region center Y: {(y1 + y2) // 2}")
            
            # Compare with estimated lip position
            estimated_center = lip_analysis["lip_center_y"]
            current_center = (y1 + y2) // 2
            offset = current_center - estimated_center
            
            print(f"\n📊 COMPARISON:")
            print(f"Estimated lip center Y: {estimated_center}")
            print(f"Current algorithm center Y: {current_center}")
            print(f"Offset (current - estimated): {offset} pixels")
            
            if abs(offset) > 10:
                print(f"⚠️  SIGNIFICANT OFFSET DETECTED! Algorithm needs adjustment.")
            else:
                print(f"✅ Algorithm appears to be close to target.")
            
            # Create comparison image
            comparison = frame.copy()
            
            # Draw current algorithm region in RED
            cv2.rectangle(comparison, (x1, y1), (x2, y2), (0, 0, 255), 2)
            cv2.putText(comparison, "CURRENT ALGORITHM", (x1, y1-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
            
            # Draw estimated lip region in GREEN
            lip_top = lip_analysis["lip_region_top"]
            lip_bottom = lip_analysis["lip_region_bottom"]
            cv2.rectangle(comparison, (0, lip_top), (frame.shape[1]-1, lip_bottom), (0, 255, 0), 2)
            cv2.putText(comparison, "ESTIMATED LIP REGION", (10, lip_top-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            cv2.imwrite("algorithm_comparison.jpg", comparison)
            print(f"💾 Saved algorithm_comparison.jpg")
            
            return {
                "current_region": (x1, y1, x2, y2),
                "offset": offset,
                "needs_adjustment": abs(offset) > 10
            }
    
    except ImportError as e:
        print(f"❌ Could not import enhanced_video_preprocessor: {e}")
        return None

def generate_corrected_coordinates(video_size, lip_analysis):
    """Generate corrected coordinates for mouth region detection"""
    width, height = video_size
    
    print(f"\n🔧 GENERATING CORRECTED COORDINATES")
    
    # For the 400x200 original videos, we want to target the lip region
    # Based on ICU dataset application: lips are at ~38% from top
    
    # For face detection fallback (when no face is detected)
    fallback_width = int(0.8 * width)  # 80% of video width
    fallback_x1 = (width - fallback_width) // 2
    fallback_x2 = fallback_x1 + fallback_width
    
    # Target the lip region specifically
    fallback_y1 = lip_analysis["lip_region_top"]
    fallback_y2 = lip_analysis["lip_region_bottom"]
    
    print(f"CORRECTED FALLBACK COORDINATES:")
    print(f"- X: {fallback_x1} to {fallback_x2} (width: {fallback_width})")
    print(f"- Y: {fallback_y1} to {fallback_y2} (height: {fallback_y2 - fallback_y1})")
    
    # For face detection (when face is detected)
    # Adjust the percentages to target the lip area within detected face
    face_lip_start_percent = 0.20  # Start at 20% of face height
    face_lip_end_percent = 0.50    # End at 50% of face height
    
    print(f"\nCORRECTED FACE DETECTION COORDINATES:")
    print(f"- Face region lip start: {face_lip_start_percent * 100}% of face height")
    print(f"- Face region lip end: {face_lip_end_percent * 100}% of face height")
    
    return {
        "fallback": {
            "x1": fallback_x1,
            "y1": fallback_y1,
            "x2": fallback_x2,
            "y2": fallback_y2
        },
        "face_detection": {
            "lip_start_percent": face_lip_start_percent,
            "lip_end_percent": face_lip_end_percent
        }
    }

def main():
    parser = argparse.ArgumentParser(description="Precise lip locator for ICU video preprocessing")
    parser.add_argument("video_path", help="Path to WebM video file")
    parser.add_argument("--frames", type=int, default=5, help="Number of frames to analyze")
    
    args = parser.parse_args()
    
    video_path = Path(args.video_path)
    if not video_path.exists():
        print(f"❌ Video file not found: {video_path}")
        return
    
    print(f"🎬 Analyzing video: {video_path.name}")
    
    # Extract and analyze frames
    frames, video_size = extract_frames_from_video(video_path, args.frames)
    
    # Analyze lip position
    lip_analysis = analyze_lip_position_manual(frames, video_size)
    
    # Test current algorithm
    algorithm_test = test_current_algorithm(video_path, lip_analysis)
    
    # Generate corrected coordinates
    corrected_coords = generate_corrected_coordinates(video_size, lip_analysis)
    
    print(f"\n📋 SUMMARY:")
    print(f"- Video size: {video_size[0]}x{video_size[1]}")
    print(f"- Estimated lip center Y: {lip_analysis['lip_center_y']}")
    print(f"- Estimated lip region: Y {lip_analysis['lip_region_top']} to {lip_analysis['lip_region_bottom']}")
    
    if algorithm_test:
        print(f"- Current algorithm center Y: {(algorithm_test['current_region'][1] + algorithm_test['current_region'][3]) // 2}")
        print(f"- Offset: {algorithm_test['offset']} pixels")
        print(f"- Needs adjustment: {algorithm_test['needs_adjustment']}")
    
    print(f"\n💡 RECOMMENDED FIXES:")
    print(f"1. Update fallback coordinates to Y {corrected_coords['fallback']['y1']} to {corrected_coords['fallback']['y2']}")
    print(f"2. Update face detection percentages to {corrected_coords['face_detection']['lip_start_percent']*100}% to {corrected_coords['face_detection']['lip_end_percent']*100}%")
    print(f"3. Test with these coordinates and verify lip visibility")

if __name__ == "__main__":
    main()
