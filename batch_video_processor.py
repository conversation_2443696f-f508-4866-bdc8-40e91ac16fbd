#!/usr/bin/env python3
"""
Batch Video Processing and Quality Assessment Tool
Processes and validates large datasets of ICU lipreading videos
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm
import yaml
import sys

# Add current directory to path
sys.path.append('.')

from video_quality_validator import VideoQualityValidator

class BatchVideoProcessor:
    """Batch processor for large video datasets"""
    
    def __init__(self, config_path: str = "large_dataset_training_config.yaml"):
        """Initialize batch processor"""
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Initialize validator
        reference_manifest = self.config['quality_control']['reference_manifest']
        self.validator = VideoQualityValidator(reference_manifest)
        
        # Quality thresholds
        self.similarity_threshold = self.config['quality_control']['similarity_threshold']
        self.technical_validation = self.config['quality_control']['technical_validation']
        self.processing_validation = self.config['quality_control']['processing_validation']
        
        print(f"Batch processor initialized")
        print(f"Similarity threshold: {self.similarity_threshold}")
        print(f"Technical validation: {self.technical_validation}")
        print(f"Processing validation: {self.processing_validation}")
    
    def discover_videos(self, video_directories: List[str], extensions: List[str] = None) -> List[Dict]:
        """Discover all video files in specified directories"""
        
        if extensions is None:
            extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
        
        discovered_videos = []
        
        for video_dir in video_directories:
            video_dir = Path(video_dir)
            
            if not video_dir.exists():
                print(f"⚠️  Directory not found: {video_dir}")
                continue
            
            print(f"🔍 Scanning directory: {video_dir}")
            
            for ext in extensions:
                pattern = f"**/*{ext}"
                video_files = list(video_dir.glob(pattern))
                
                for video_file in video_files:
                    # Try to extract phrase from filename or directory structure
                    phrase = self._extract_phrase_from_path(video_file)
                    
                    discovered_videos.append({
                        'video_path': str(video_file),
                        'filename': video_file.name,
                        'directory': str(video_file.parent),
                        'extension': ext,
                        'phrase': phrase,
                        'size_bytes': video_file.stat().st_size if video_file.exists() else 0
                    })
        
        print(f"📊 Discovered {len(discovered_videos)} video files")
        return discovered_videos
    
    def _extract_phrase_from_path(self, video_path: Path) -> str:
        """Extract phrase from video file path or filename"""
        
        # List of expected phrases
        expected_phrases = self.config['phrases']
        
        # Check filename and parent directories
        path_parts = [video_path.stem.lower()] + [p.name.lower() for p in video_path.parents]
        path_text = ' '.join(path_parts)
        
        # Find matching phrase
        for phrase in expected_phrases:
            phrase_normalized = phrase.replace(' ', '_').lower()
            phrase_spaced = phrase.lower()
            
            if phrase_normalized in path_text or phrase_spaced in path_text:
                return phrase
        
        # Try to extract from structured filename (like reference videos)
        filename = video_path.stem
        if '__' in filename:
            parts = filename.split('__')
            if len(parts) > 0:
                potential_phrase = parts[0].replace('_', ' ').lower()
                for phrase in expected_phrases:
                    if phrase.lower() == potential_phrase:
                        return phrase
        
        return "unknown"
    
    def process_video_batch(self, video_list: List[Dict], max_workers: int = 4) -> List[Dict]:
        """Process a batch of videos with quality validation"""
        
        print(f"🔄 Processing {len(video_list)} videos with {max_workers} workers...")
        
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all validation tasks
            future_to_video = {
                executor.submit(self._process_single_video, video_info): video_info
                for video_info in video_list
            }
            
            # Collect results with progress bar
            for future in tqdm(as_completed(future_to_video), total=len(video_list), desc="Processing videos"):
                video_info = future_to_video[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"❌ Error processing {video_info['filename']}: {e}")
                    # Add failed result
                    results.append({
                        **video_info,
                        'validation_result': {
                            'overall_quality': 'FAILED',
                            'recommendation': 'REJECT',
                            'error': str(e)
                        }
                    })
        
        return results
    
    def _process_single_video(self, video_info: Dict) -> Dict:
        """Process and validate a single video"""
        
        video_path = video_info['video_path']
        phrase = video_info['phrase']
        
        # Skip if phrase is unknown
        if phrase == "unknown":
            validation_result = {
                'overall_quality': 'FAILED',
                'recommendation': 'REJECT',
                'error': 'Unknown phrase'
            }
        else:
            # Validate video
            validation_result = self.validator.validate_video(video_path, phrase)
        
        return {
            **video_info,
            'validation_result': validation_result
        }
    
    def filter_by_quality(self, processed_videos: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """Filter videos by quality thresholds"""
        
        accepted = []
        rejected = []
        
        for video in processed_videos:
            validation = video.get('validation_result', {})
            recommendation = validation.get('recommendation', 'REJECT')
            similarity_score = validation.get('reference_comparison', {}).get('similarity_score', 0.0)
            
            # Apply quality filters
            if recommendation == 'ACCEPT' and similarity_score >= self.similarity_threshold:
                accepted.append(video)
            else:
                rejected.append(video)
        
        print(f"📊 Quality filtering results:")
        print(f"   ✅ Accepted: {len(accepted)} videos")
        print(f"   ❌ Rejected: {len(rejected)} videos")
        
        return accepted, rejected
    
    def create_training_manifest(self, accepted_videos: List[Dict], output_path: str):
        """Create training manifest from accepted videos"""
        
        # Convert to DataFrame
        manifest_data = []
        
        for video in accepted_videos:
            manifest_data.append({
                'video_path': video['video_path'],
                'phrase': video['phrase'],
                'speaker_id': 'unknown',  # Will need to be filled in
                'age_group': 'unknown',
                'gender': 'unknown',
                'ethnicity': 'unknown',
                'quality_score': video['validation_result'].get('reference_comparison', {}).get('similarity_score', 0.0),
                'source': 'additional_dataset'
            })
        
        df = pd.DataFrame(manifest_data)
        
        # Add reference videos
        ref_df = pd.read_csv(self.config['quality_control']['reference_manifest'])
        ref_df['quality_score'] = 1.0  # Reference videos have perfect quality
        ref_df['source'] = 'reference'

        # Ensure all required columns exist
        for col in ['speaker_id', 'age_group', 'gender', 'ethnicity']:
            if col not in ref_df.columns:
                ref_df[col] = 'unknown'
        
        # Combine datasets
        combined_df = pd.concat([ref_df, df], ignore_index=True)
        
        # Save manifest
        combined_df.to_csv(output_path, index=False)
        
        print(f"💾 Training manifest saved: {output_path}")
        print(f"   Total videos: {len(combined_df)}")
        print(f"   Reference videos: {len(ref_df)}")
        print(f"   Additional videos: {len(df)}")
        
        # Show phrase distribution
        phrase_counts = combined_df['phrase'].value_counts()
        print(f"\n📝 Phrase distribution:")
        for phrase, count in phrase_counts.items():
            print(f"   {phrase}: {count} videos")
        
        return combined_df
    
    def generate_quality_report(self, processed_videos: List[Dict], output_path: str):
        """Generate comprehensive quality assessment report"""
        
        report = {
            'summary': {
                'total_videos': len(processed_videos),
                'processed_successfully': sum(1 for v in processed_videos if 'error' not in v.get('validation_result', {})),
                'processing_errors': sum(1 for v in processed_videos if 'error' in v.get('validation_result', {}))
            },
            'quality_distribution': {},
            'recommendation_distribution': {},
            'phrase_analysis': {},
            'technical_issues': [],
            'processing_issues': []
        }
        
        # Analyze quality distribution
        quality_counts = {}
        recommendation_counts = {}
        phrase_quality = {}
        
        for video in processed_videos:
            validation = video.get('validation_result', {})
            quality = validation.get('overall_quality', 'UNKNOWN')
            recommendation = validation.get('recommendation', 'REJECT')
            phrase = video.get('phrase', 'unknown')
            
            # Count quality levels
            quality_counts[quality] = quality_counts.get(quality, 0) + 1
            recommendation_counts[recommendation] = recommendation_counts.get(recommendation, 0) + 1
            
            # Track phrase quality
            if phrase not in phrase_quality:
                phrase_quality[phrase] = {'total': 0, 'accepted': 0, 'avg_similarity': []}
            
            phrase_quality[phrase]['total'] += 1
            if recommendation == 'ACCEPT':
                phrase_quality[phrase]['accepted'] += 1
            
            similarity = validation.get('reference_comparison', {}).get('similarity_score', 0.0)
            phrase_quality[phrase]['avg_similarity'].append(similarity)
            
            # Collect issues
            tech_issues = validation.get('technical_quality', {}).get('issues', [])
            proc_issues = validation.get('processing_compatibility', {}).get('issues', [])
            
            if tech_issues:
                report['technical_issues'].extend(tech_issues)
            if proc_issues:
                report['processing_issues'].extend(proc_issues)
        
        # Calculate phrase statistics
        for phrase, stats in phrase_quality.items():
            stats['acceptance_rate'] = stats['accepted'] / stats['total'] if stats['total'] > 0 else 0
            stats['avg_similarity'] = np.mean(stats['avg_similarity']) if stats['avg_similarity'] else 0
        
        report['quality_distribution'] = quality_counts
        report['recommendation_distribution'] = recommendation_counts
        report['phrase_analysis'] = phrase_quality
        
        # Save report
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📊 Quality report saved: {output_path}")
        
        return report

def main():
    """Main batch processing function"""
    
    parser = argparse.ArgumentParser(description="Batch process ICU lipreading videos")
    parser.add_argument('--video_dirs', nargs='+', required=True, help='Directories containing videos to process')
    parser.add_argument('--output_dir', default='processed_dataset', help='Output directory for results')
    parser.add_argument('--max_workers', type=int, default=4, help='Number of parallel workers')
    parser.add_argument('--config', default='large_dataset_training_config.yaml', help='Configuration file')
    
    args = parser.parse_args()
    
    print("🚀 ICU Lipreading Batch Video Processor")
    print("=" * 50)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Initialize processor
    processor = BatchVideoProcessor(args.config)
    
    # Discover videos
    discovered_videos = processor.discover_videos(args.video_dirs)
    
    if not discovered_videos:
        print("❌ No videos found in specified directories")
        return
    
    # Process videos
    processed_videos = processor.process_video_batch(discovered_videos, args.max_workers)
    
    # Filter by quality
    accepted_videos, rejected_videos = processor.filter_by_quality(processed_videos)
    
    # Create training manifest
    manifest_path = output_dir / "training_manifest.csv"
    training_df = processor.create_training_manifest(accepted_videos, manifest_path)
    
    # Generate quality report
    report_path = output_dir / "quality_report.json"
    quality_report = processor.generate_quality_report(processed_videos, report_path)
    
    # Save detailed results
    results_path = output_dir / "detailed_results.json"
    with open(results_path, 'w') as f:
        json.dump(processed_videos, f, indent=2, default=str)
    
    print(f"\n🎯 Batch processing complete!")
    print(f"   📁 Output directory: {output_dir}")
    print(f"   📊 Training manifest: {manifest_path}")
    print(f"   📈 Quality report: {report_path}")
    print(f"   📋 Detailed results: {results_path}")

if __name__ == '__main__':
    main()
