#!/usr/bin/env python3
"""
Train LipNet Perfect 10 Rescue classifier with fresh balanced dataset
Uses perfect10_rescue dataset with 10 videos per phrase for superior performance
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, WeightedRandomSampler
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import time
import json
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns

# Add current directory to path
sys.path.append('.')

from lipnet_perfect_10 import LipNetPerfect10, LipNetPerfect10Manager
from backend.lightweight_vsr.utils_video import VideoProcessor
from enhanced_video_augmentation import EnhancedVideoAugmentation

class Perfect10RescueDataset(Dataset):
    """Dataset for LipNet Perfect 10 Rescue with balanced class representation"""
    
    def __init__(self, video_files: list, labels: list, video_processor: VideoProcessor, 
                 augmentation: EnhancedVideoAugmentation = None, is_training: bool = True):
        
        self.video_files = video_files
        self.labels = labels
        self.video_processor = video_processor
        self.augmentation = augmentation
        self.is_training = is_training
        
        # Perfect 10 phrases mapping (exact order)
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        
        print(f"📊 Perfect 10 Rescue Dataset: {len(video_files)} samples")
        print(f"   Mode: {'Training' if is_training else 'Validation/Test'}")
        print(f"   Enhanced preprocessing: 64 frames, 112×112, z-score normalization")
        print(f"   Augmentation: {'Enabled' if augmentation and is_training else 'Disabled'}")
    
    def __len__(self):
        return len(self.video_files)
    
    def __getitem__(self, idx):
        video_path = self.video_files[idx]
        label = self.labels[idx]
        
        try:
            # Detect if video is pre-cropped
            is_pre_cropped = "_mouth_cropped" in Path(video_path).name or "processed_" in Path(video_path).name

            # Set mouth cropping based on video type
            if is_pre_cropped:
                self.video_processor.mouth_crop = None
            else:
                self.video_processor.mouth_crop = (133, 0, 133, 100)

            # Process video with enhanced LipNet preprocessing
            video_tensor = self.video_processor.process_video(video_path)

            # Ensure exact 64-frame output
            if video_tensor.shape[1] != 64:
                C, T, H, W = video_tensor.shape
                if T > 64:
                    # Crop to exactly 64 frames from center
                    start_idx = (T - 64) // 2
                    video_tensor = video_tensor[:, start_idx:start_idx + 64, :, :]
                elif T < 64:
                    # Pad to exactly 64 frames
                    padding_needed = 64 - T
                    last_frame = video_tensor[:, -1:, :, :].repeat(1, padding_needed, 1, 1)
                    video_tensor = torch.cat([video_tensor, last_frame], dim=1)

            # Apply augmentation during training (after ensuring correct shape)
            if self.augmentation and self.is_training:
                video_tensor = self.augmentation(video_tensor)

            # Final shape validation
            assert video_tensor.shape == (1, 64, 112, 112), f"Shape mismatch: {video_tensor.shape}"

            return video_tensor, label

        except Exception as e:
            print(f"⚠️  Failed to load {Path(video_path).name}: {e}")
            # Return dummy data with correct dimensions
            dummy_tensor = torch.zeros(1, 64, 112, 112)
            return dummy_tensor, label

class LipNetPerfect10RescueTrainer:
    """LipNet trainer for Perfect 10 Rescue dataset targeting >95% accuracy"""
    
    def __init__(self, dataset_path: str):
        """Initialize LipNet Perfect 10 Rescue trainer"""
        
        self.dataset_path = Path(dataset_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Perfect 10 phrases (exact folder names expected)
        self.phrase_folders = [
            "am_i_getting_better", "i_feel_anxious", "i_m_confused",
            "i_need_to_move", "i_need_to_sit_up", "i_want_to_phone_my_family",
            "what_happened_to_me", "what_time_is_my_wife_coming",
            "where_am_i", "who_is_with_me_today"
        ]
        
        self.phrase_to_idx = {
            "am_i_getting_better": 0, "i_feel_anxious": 1, "i_m_confused": 2,
            "i_need_to_move": 3, "i_need_to_sit_up": 4, "i_want_to_phone_my_family": 5,
            "what_happened_to_me": 6, "what_time_is_my_wife_coming": 7,
            "where_am_i": 8, "who_is_with_me_today": 9
        }
        
        # Training components
        self.model = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        
        # Training tracking
        self.training_history = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': [],
            'learning_rates': []
        }
        
        self.best_val_accuracy = 0.0
        self.best_model_path = None
        
        print(f"🎯 LipNet Perfect 10 Rescue Trainer Initialized")
        print(f"   Dataset path: {self.dataset_path}")
        print(f"   Device: {self.device}")
        print(f"   Target: >95% accuracy with balanced dataset")
    
    def validate_dataset(self) -> dict:
        """Validate dataset structure and integrity"""
        
        print(f"\n📊 Validating Perfect 10 Rescue Dataset")
        print("=" * 45)
        
        if not self.dataset_path.exists():
            # Search common desktop locations
            search_locations = [
                "/Users/<USER>/Desktop/perfect10_rescue/",
                "/Users/<USER>/Desktop/perfect_10_rescue/",
                "/Users/<USER>/Desktop/Perfect10_Rescue/",
                "/Users/<USER>/Desktop/"
            ]
            
            print(f"❌ Dataset path not found: {self.dataset_path}")
            print(f"🔍 Searching common locations...")
            
            for location in search_locations:
                location_path = Path(location)
                if location_path.exists():
                    if location.endswith('/'):
                        # Check if it's the dataset folder
                        phrase_folders = [location_path / folder for folder in self.phrase_folders]
                        if any(folder.exists() for folder in phrase_folders):
                            print(f"   ✅ Found dataset at: {location}")
                            self.dataset_path = location_path
                            break
                    else:
                        # List available folders
                        folders = [f.name for f in location_path.iterdir() if f.is_dir()]
                        if folders:
                            print(f"   📁 Available folders in {location}: {folders}")
            
            if not self.dataset_path.exists():
                return {
                    'success': False,
                    'error': 'Dataset folder not found',
                    'searched_locations': search_locations
                }
        
        print(f"📁 Dataset location: {self.dataset_path}")
        
        # Validate phrase folders and count videos
        validation_report = {
            'success': True,
            'total_videos': 0,
            'phrase_counts': {},
            'missing_folders': [],
            'insufficient_videos': [],
            'video_files': [],
            'labels': []
        }
        
        video_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
        
        for phrase_folder in self.phrase_folders:
            folder_path = self.dataset_path / phrase_folder
            
            if not folder_path.exists():
                validation_report['missing_folders'].append(phrase_folder)
                print(f"   ❌ Missing folder: {phrase_folder}")
                continue
            
            # Count video files
            video_files = []
            for ext in video_extensions:
                video_files.extend(list(folder_path.glob(f"*{ext}")))
            
            video_count = len(video_files)
            validation_report['phrase_counts'][phrase_folder] = video_count
            
            if video_count < 10:
                validation_report['insufficient_videos'].append((phrase_folder, video_count))
                print(f"   ⚠️  {phrase_folder}: {video_count} videos (expected 10)")
            else:
                print(f"   ✅ {phrase_folder}: {video_count} videos")
            
            # Add videos to dataset
            phrase_idx = self.phrase_to_idx[phrase_folder]
            for video_file in video_files:
                validation_report['video_files'].append(str(video_file))
                validation_report['labels'].append(phrase_idx)
            
            validation_report['total_videos'] += video_count
        
        print(f"\n📊 Dataset Summary:")
        print(f"   Total videos: {validation_report['total_videos']}")
        print(f"   Expected: 100 videos (10 per phrase)")
        print(f"   Missing folders: {len(validation_report['missing_folders'])}")
        print(f"   Insufficient videos: {len(validation_report['insufficient_videos'])}")
        
        if validation_report['total_videos'] < 50:
            validation_report['success'] = False
            validation_report['error'] = f"Insufficient videos: {validation_report['total_videos']} < 50 minimum"
        
        return validation_report
    
    def setup_data_loaders(self, validation_report: dict):
        """Setup data loaders with balanced class representation"""
        
        print(f"\n📊 Setting up LipNet Data Loaders")
        print("=" * 35)
        
        video_files = validation_report['video_files']
        labels = validation_report['labels']
        
        # Enhanced video processor for LipNet
        video_processor = VideoProcessor(
            target_frames=64,
            target_size=(112, 112),
            grayscale=True,
            fps=25.0,
            mouth_crop=None,  # Will be set adaptively
            use_dataset_normalization=True
        )
        
        # Simplified augmentation for training (shape-preserving)
        augmentation = EnhancedVideoAugmentation(
            temporal_jitter=0,  # Disabled to preserve exact 64-frame shape
            time_warping=0.0,   # Disabled to preserve exact 64-frame shape
            temporal_probability=0.0,
            random_crops=0.03,  # Reduced for stability
            random_translations=2,  # Reduced for stability
            photometric_jitter=0.1,  # Reduced for stability
            gaussian_blur_sigma=(0.5, 1.0),
            gaussian_blur_prob=0.05,  # Reduced probability
            horizontal_flip=False
        )
        
        # Split data: 80% train, 10% val, 10% test
        np.random.seed(42)  # For reproducible splits
        indices = np.random.permutation(len(video_files))
        
        train_size = int(0.8 * len(video_files))
        val_size = int(0.1 * len(video_files))
        
        train_indices = indices[:train_size]
        val_indices = indices[train_size:train_size + val_size]
        test_indices = indices[train_size + val_size:]
        
        # Create split datasets
        train_files = [video_files[i] for i in train_indices]
        train_labels = [labels[i] for i in train_indices]
        
        val_files = [video_files[i] for i in val_indices]
        val_labels = [labels[i] for i in val_indices]
        
        test_files = [video_files[i] for i in test_indices]
        test_labels = [labels[i] for i in test_indices]
        
        print(f"📊 Data splits: Train={len(train_files)}, Val={len(val_files)}, Test={len(test_files)}")
        
        # Create datasets
        train_dataset = Perfect10RescueDataset(train_files, train_labels, video_processor, augmentation, is_training=True)
        val_dataset = Perfect10RescueDataset(val_files, val_labels, video_processor, None, is_training=False)
        test_dataset = Perfect10RescueDataset(test_files, test_labels, video_processor, None, is_training=False)
        
        # Perfect class balancing with WeightedRandomSampler
        class_counts = torch.bincount(torch.tensor(train_labels), minlength=10)
        class_weights = 1.0 / class_counts.float()
        class_weights[class_counts == 0] = 0.0  # Handle empty classes
        
        sample_weights = class_weights[train_labels]
        train_sampler = WeightedRandomSampler(
            weights=sample_weights,
            num_samples=len(sample_weights),
            replacement=True
        )
        
        print(f"⚖️  Class balancing:")
        for i, count in enumerate(class_counts):
            phrase = list(self.phrase_to_idx.keys())[i]
            print(f"   {phrase}: {count} samples, weight: {class_weights[i]:.3f}")
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=4,
            sampler=train_sampler,
            num_workers=0,
            pin_memory=False
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=4,
            shuffle=False,
            num_workers=0,
            pin_memory=False
        )
        
        self.test_loader = DataLoader(
            test_dataset,
            batch_size=4,
            shuffle=False,
            num_workers=0,
            pin_memory=False
        )
        
        print(f"✅ LipNet data loaders created with perfect class balancing")
    
    def setup_model(self):
        """Setup LipNet Perfect 10 model"""
        
        print(f"\n🤖 Setting up LipNet Perfect 10 Model")
        print("=" * 35)
        
        # Create LipNet model manager
        manager = LipNetPerfect10Manager()
        
        # Create LipNet model with specified configuration
        self.model = manager.create_model(
            hidden_dim=256,
            num_rnn_layers=2,
            rnn_type='LSTM',
            dropout=0.3
        )
        
        self.model.to(self.device)
        
        print(f"✅ LipNet model setup complete")
        print(f"   Parameters: {self.model.get_num_parameters():,}")
        print(f"   Architecture: 3D CNN + BiLSTM + 8-head Attention")
        print(f"   Target: >95% accuracy on Perfect 10 Rescue dataset")
    
    def setup_training_components(self):
        """Setup optimizer, scheduler, and loss function"""
        
        print(f"\n⚙️  Setting up Training Components")
        print("=" * 35)
        
        # Optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=0.0001,
            weight_decay=0.0001
        )
        
        # Scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=80
        )
        
        # Loss function with label smoothing
        self.criterion = nn.CrossEntropyLoss(label_smoothing=0.1)
        
        print(f"✅ Training components ready")
        print(f"   Optimizer: AdamW (lr=0.0001, weight_decay=0.0001)")
        print(f"   Scheduler: CosineAnnealingLR (T_max=80)")
        print(f"   Loss: CrossEntropyLoss (label_smoothing=0.1)")

    def train_epoch(self, epoch: int):
        """Train one epoch"""

        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0

        pbar = tqdm(self.train_loader, desc=f"LipNet Rescue Epoch {epoch+1}")

        for batch_idx, (videos, labels) in enumerate(pbar):
            videos, labels = videos.to(self.device), labels.to(self.device)

            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(videos)
            loss = self.criterion(outputs, labels)

            # Backward pass
            loss.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)

            self.optimizer.step()

            # Statistics
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()

            # Update progress bar
            pbar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'Acc': f"{100.*correct/total:.2f}%"
            })

        epoch_loss = total_loss / len(self.train_loader)
        epoch_acc = correct / total

        return epoch_loss, epoch_acc

    def validate(self):
        """Validate model"""

        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for videos, labels in self.val_loader:
                videos, labels = videos.to(self.device), labels.to(self.device)

                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)

                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()

                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        val_loss = total_loss / len(self.val_loader)
        val_acc = correct / total

        return val_loss, val_acc, all_predictions, all_labels

    def save_checkpoint(self, epoch: int, val_accuracy: float, is_best: bool = False):
        """Save model checkpoint"""

        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_accuracy': self.best_val_accuracy,
            'training_history': self.training_history,
            'model_info': {
                'architecture': 'LipNet Perfect 10 Rescue',
                'parameters': self.model.get_num_parameters(),
                'dataset': 'Perfect 10 Rescue (balanced)',
                'target_accuracy': '>95%'
            }
        }

        # Save regular checkpoint
        checkpoint_dir = Path("checkpoints/lipnet_perfect_10_rescue")
        checkpoint_dir.mkdir(parents=True, exist_ok=True)

        checkpoint_path = checkpoint_dir / f"lipnet_rescue_epoch_{epoch+1}.pth"
        torch.save(checkpoint, checkpoint_path)

        # Save best model
        if is_best:
            best_path = checkpoint_dir / "best_lipnet_perfect_10_rescue_model.pth"
            torch.save(checkpoint, best_path)
            self.best_model_path = best_path
            print(f"💾 New best LipNet Rescue model saved: {val_accuracy:.1%} accuracy")

    def generate_confusion_matrix(self, predictions, labels, epoch: int):
        """Generate and save confusion matrix"""

        cm = confusion_matrix(labels, predictions)

        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=[f"Class {i}" for i in range(10)],
                    yticklabels=[f"Class {i}" for i in range(10)])
        plt.title(f'LipNet Perfect 10 Rescue - Confusion Matrix (Epoch {epoch+1})')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')

        # Save confusion matrix
        cm_path = f"checkpoints/lipnet_perfect_10_rescue/confusion_matrix_epoch_{epoch+1}.png"
        plt.savefig(cm_path, dpi=300, bbox_inches='tight')
        plt.close()

        # Calculate per-class accuracy
        per_class_acc = cm.diagonal() / cm.sum(axis=1)

        print(f"📊 Per-class accuracy (Epoch {epoch+1}):")
        for i, acc in enumerate(per_class_acc):
            phrase = list(self.phrase_to_idx.keys())[i]
            print(f"   {phrase}: {acc:.1%}")

        return per_class_acc

    def train(self):
        """Execute LipNet Perfect 10 Rescue training"""

        print(f"\n🚀 Starting LipNet Perfect 10 Rescue Training")
        print("=" * 50)
        print(f"Target: >95% accuracy with balanced dataset")

        num_epochs = 80

        for epoch in range(num_epochs):
            # Train epoch
            train_loss, train_acc = self.train_epoch(epoch)

            # Validate
            val_loss, val_acc, val_predictions, val_labels = self.validate()

            # Update scheduler
            self.scheduler.step()

            # Track history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            self.training_history['learning_rates'].append(self.optimizer.param_groups[0]['lr'])

            # Check for best model
            is_best = val_acc > self.best_val_accuracy
            if is_best:
                self.best_val_accuracy = val_acc

            # Save checkpoint
            if (epoch + 1) % 10 == 0 or is_best:
                self.save_checkpoint(epoch, val_acc, is_best)

            # Generate confusion matrix every 20 epochs or if best
            if (epoch + 1) % 20 == 0 or is_best:
                per_class_acc = self.generate_confusion_matrix(val_predictions, val_labels, epoch)

            # Print progress
            print(f"Epoch {epoch+1:3d}: Train Loss={train_loss:.4f}, Train Acc={train_acc:.1%}, "
                  f"Val Loss={val_loss:.4f}, Val Acc={val_acc:.1%}, LR={self.optimizer.param_groups[0]['lr']:.6f}")

            # Target achieved check
            if val_acc >= 0.95:  # >95% target
                print(f"🎉 LipNet Rescue target accuracy achieved: {val_acc:.1%}")
                self.save_checkpoint(epoch, val_acc, True)
                self.generate_confusion_matrix(val_predictions, val_labels, epoch)
                break

        print(f"\n🎉 LipNet Perfect 10 Rescue Training Complete!")
        print(f"   Best validation accuracy: {self.best_val_accuracy:.1%}")
        print(f"   Best model saved: {self.best_model_path}")

        return self.best_val_accuracy

def main():
    """Main training function"""
    
    print("🎯 LipNet Perfect 10 Rescue Training")
    print("=" * 40)
    print("Training with fresh balanced dataset for >95% accuracy")
    
    # Initialize trainer
    dataset_path = "/Users/<USER>/Desktop/perfect10_rescue/"
    trainer = LipNetPerfect10RescueTrainer(dataset_path)
    
    # Validate dataset
    validation_report = trainer.validate_dataset()
    
    if not validation_report['success']:
        print(f"\n❌ Dataset validation failed: {validation_report['error']}")
        return
    
    # Setup components
    trainer.setup_data_loaders(validation_report)
    trainer.setup_model()
    trainer.setup_training_components()

    print(f"\n🚀 LipNet Perfect 10 Rescue Training Ready!")
    print(f"   Dataset: {validation_report['total_videos']} videos")
    print(f"   Model: LipNet (3D CNN + BiLSTM + Attention)")
    print(f"   Target: >95% accuracy with balanced classes")

    # Execute training
    best_accuracy = trainer.train()

    # Generate final report
    print(f"\n📊 FINAL TRAINING REPORT")
    print("=" * 30)
    print(f"✅ LipNet Perfect 10 Rescue training completed")
    print(f"   Best validation accuracy: {best_accuracy:.1%}")
    print(f"   Target achieved: {'YES' if best_accuracy >= 0.95 else 'NO'}")
    print(f"   Model saved: {trainer.best_model_path}")

    # Save training history
    history_file = "checkpoints/lipnet_perfect_10_rescue/training_history.json"
    with open(history_file, 'w') as f:
        json.dump(trainer.training_history, f, indent=2)

    print(f"   Training history: {history_file}")

    # Save dataset validation report
    report_file = "checkpoints/lipnet_perfect_10_rescue/dataset_validation_report.json"
    with open(report_file, 'w') as f:
        json.dump(validation_report, f, indent=2, default=str)

    print(f"   Dataset report: {report_file}")

    print(f"\n🎉 LipNet Perfect 10 Rescue Training Complete!")

    return best_accuracy

if __name__ == '__main__':
    main()
