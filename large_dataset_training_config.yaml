# ICU Lipreading Training Configuration for Large Dataset (~1,638 videos)
# Optimized for 26-class phrase classification

# Model Configuration
model:
  name: "Mobile3DTiny"
  num_classes: 26
  dropout: 0.3
  use_attention: true

# Data Configuration
data:
  # Video preprocessing
  frames: 32
  height: 96
  width: 96
  grayscale: true
  
  # Data splits (speaker-wise to prevent data leakage)
  train_split: 0.7    # ~1,147 videos
  val_split: 0.15     # ~246 videos  
  test_split: 0.15    # ~245 videos
  
  # Data loading
  batch_size: 16      # Increased for larger dataset
  num_workers: 4      # Parallel data loading
  pin_memory: true
  
  # Augmentation (re-enabled for large dataset)
  augmentation:
    enabled: true
    temporal_jitter: 0.1
    spatial_jitter: 0.1
    brightness_jitter: 0.1
    horizontal_flip: 0.5
    scale_jitter: 0.1

# Training Configuration
training:
  epochs: 50          # More epochs for larger dataset
  learning_rate: 0.001
  weight_decay: 0.0001
  
  # Learning rate scheduling
  scheduler:
    type: "cosine"
    warmup_epochs: 5
    min_lr: 0.00001
  
  # Optimization
  optimizer: "adamw"
  gradient_clip: 1.0
  
  # Mixed precision training
  amp: true
  
  # Early stopping
  early_stopping:
    patience: 10
    min_delta: 0.001
    monitor: "val_f1"

# Validation Configuration
validation:
  frequency: 1        # Validate every epoch
  metrics: ["accuracy", "f1", "precision", "recall"]
  save_best: true
  
# Logging and Checkpointing
logging:
  log_frequency: 50   # Log every 50 batches
  save_frequency: 5   # Save checkpoint every 5 epochs
  tensorboard: true
  wandb: false        # Set to true if using Weights & Biases
  
# Hardware Configuration
hardware:
  device: "auto"      # Auto-detect GPU/CPU
  mixed_precision: true
  compile_model: false # Set to true for PyTorch 2.0+

# Quality Control
quality_control:
  reference_manifest: "reference_videos_manifest.csv"
  similarity_threshold: 0.4  # Minimum similarity to reference videos
  technical_validation: true
  processing_validation: true

# Expected 26 ICU Phrases
phrases:
  - "am i getting better"
  - "i feel anxious"
  - "i have a headache"
  - "i m confused"
  - "i m in pain"
  - "i m uncomfortable"
  - "i need a medication"
  - "i need help"
  - "i need to lie down"
  - "i need to move"
  - "i need to sit up"
  - "i need to use the toilet"
  - "i want to phone my family"
  - "i want to see my husband"
  - "i want to see my wife"
  - "my back hurts"
  - "my chest hurts"
  - "please explain again"
  - "stay with me please"
  - "what happened to me"
  - "what time is my husband coming"
  - "what time is my wife coming"
  - "where am i"
  - "where is my husband"
  - "where is my wife"
  - "who is with me today"

# Output Configuration
output:
  model_name: "icu_lipreading_26phrases_v1"
  save_dir: "artifacts"
  export_onnx: true
  export_torchscript: true
