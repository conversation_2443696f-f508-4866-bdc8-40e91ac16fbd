#!/usr/bin/env python3
"""
Debug video processing to find shape inconsistencies
"""

import sys
sys.path.append('.')

import pandas as pd
import torch
from backend.lightweight_vsr.utils_video import VideoProcessor
from pathlib import Path

def test_video_processing():
    """Test video processing on all videos to find shape issues"""
    
    # Load manifest
    df = pd.read_csv('data/manifest.csv')
    
    # Create processor
    processor = VideoProcessor(target_frames=32, target_size=(96, 96), grayscale=True)
    
    print(f"Testing {len(df)} videos...")
    
    shape_issues = []
    successful = 0
    
    for idx, row in df.iterrows():
        video_path = row['video_path']
        phrase = row['phrase']
        
        try:
            video_tensor = processor.process_video(video_path)
            expected_shape = (1, 32, 96, 96)
            
            if video_tensor.shape != expected_shape:
                shape_issues.append({
                    'path': video_path,
                    'phrase': phrase,
                    'shape': video_tensor.shape,
                    'expected': expected_shape
                })
                print(f"❌ {Path(video_path).name}: {video_tensor.shape} != {expected_shape}")
            else:
                successful += 1
                if successful <= 5:  # Show first few successes
                    print(f"✅ {Path(video_path).name}: {video_tensor.shape}")
                    
        except Exception as e:
            shape_issues.append({
                'path': video_path,
                'phrase': phrase,
                'error': str(e)
            })
            print(f"💥 {Path(video_path).name}: ERROR - {e}")
    
    print(f"\nSummary:")
    print(f"Successful: {successful}/{len(df)}")
    print(f"Shape issues: {len([x for x in shape_issues if 'shape' in x])}")
    print(f"Processing errors: {len([x for x in shape_issues if 'error' in x])}")
    
    if shape_issues:
        print(f"\nFirst 10 issues:")
        for issue in shape_issues[:10]:
            if 'shape' in issue:
                print(f"  {Path(issue['path']).name}: {issue['shape']}")
            else:
                print(f"  {Path(issue['path']).name}: {issue['error']}")
    
    return successful == len(df)


if __name__ == '__main__':
    success = test_video_processing()
    if success:
        print("\n🎉 All videos processed successfully!")
    else:
        print("\n⚠️ Some videos have issues. Fix before training.")
