<!DOCTYPE html>
<html>
<head>
    <title>ICU Lipreading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #e8f5e9;
            display: none;
        }
        .phrase {
            font-size: 24px;
            font-weight: bold;
            color: #2e7d32;
        }
        .confidence {
            font-size: 16px;
            color: #555;
        }
    </style>
</head>
<body>
    <h1>ICU Lipreading Test</h1>
    
    <div class="container">
        <form id="prediction-form">
            <div class="form-group">
                <label for="video">Select a video file:</label>
                <input type="file" id="video" name="video" accept="video/*" required>
            </div>
            <button type="submit">Predict Phrase</button>
        </form>
        
        <div id="result">
            <h2>Prediction Result:</h2>
            <div class="phrase" id="predicted-phrase"></div>
            <div class="confidence" id="confidence-level"></div>
        </div>
    </div>

    <script>
        document.getElementById('prediction-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileField = document.getElementById('video');
            
            if (fileField.files.length === 0) {
                alert('Please select a video file');
                return;
            }
            
            formData.append('video', fileField.files[0]);
            
            try {
                const response = await fetch('http://127.0.0.1:5000/predict', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    document.getElementById('predicted-phrase').textContent = result.phrase;
                    document.getElementById('confidence-level').textContent = 
                        `Confidence: ${(result.confidence * 100).toFixed(2)}%`;
                    document.getElementById('result').style.display = 'block';
                } else {
                    alert(`Error: ${result.error}`);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while making the prediction');
            }
        });
    </script>
</body>
</html>
