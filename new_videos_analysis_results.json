{"folder_path": "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on", "model_path": "checkpoints/perfect_10_training/best_perfect_10_model.pth", "analysis_timestamp": "/Users/<USER>/Desktop/app dev 23.5.25", "perfect_phrases": ["am i getting better", "i feel anxious", "i m confused", "i need to move", "i need to sit up", "i want to phone my family", "what happened to me", "what time is my wife coming", "where am i", "who is with me today"], "results": [{"success": true, "video_name": "1.webm", "video_path": "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on/1.webm", "video_shape": [1, 32, 96, 96], "value_range": [0.0, 0.8941176533699036], "top_prediction": "i feel anxious", "top_confidence": 0.4330040514469147, "confidence_level": "Low", "confidence_emoji": "🟠", "top3_phrases": ["i feel anxious", "i need to sit up", "where am i"], "top3_probabilities": [0.4330040514469147, 0.367768794298172, 0.14017397165298462], "all_probabilities": [0.011406143195927143, 0.4330040514469147, 0.013615595176815987, 0.013202990405261517, 0.367768794298172, 0.0036961217410862446, 0.009058703668415546, 0.006423931568861008, 0.14017397165298462, 0.0016498154727742076], "raw_outputs": [-0.6699455380439758, 2.9666495323181152, -0.4928816556930542, -0.5236542820930481, 2.803356885910034, -1.7968134880065918, -0.9003714323043823, -1.2440670728683472, 1.8387867212295532, -2.6034343242645264]}, {"success": true, "video_name": "2.webm", "video_path": "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on/2.webm", "video_shape": [1, 32, 96, 96], "value_range": [0.003921568859368563, 0.9098039269447327], "top_prediction": "i feel anxious", "top_confidence": 0.46600818634033203, "confidence_level": "Low", "confidence_emoji": "🟠", "top3_phrases": ["i feel anxious", "i need to sit up", "where am i"], "top3_probabilities": [0.46600818634033203, 0.33098095655441284, 0.14642904698848724], "all_probabilities": [0.009515810757875443, 0.46600818634033203, 0.014140916056931019, 0.012049457989633083, 0.33098095655441284, 0.0035045084077864885, 0.00791173055768013, 0.0079348748549819, 0.14642904698848724, 0.0015244638780131936], "raw_outputs": [-0.798539936542511, 3.0927085876464844, -0.40242213010787964, -0.5624750256538391, 2.750566244125366, -1.7974443435668945, -0.9831480979919434, -0.9802271127700806, 1.9350463151931763, -2.629851818084717]}, {"success": true, "video_name": "3.webm", "video_path": "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on/3.webm", "video_shape": [1, 32, 96, 96], "value_range": [0.0, 0.9137254953384399], "top_prediction": "i feel anxious", "top_confidence": 0.42850765585899353, "confidence_level": "Low", "confidence_emoji": "🟠", "top3_phrases": ["i feel anxious", "i need to sit up", "where am i"], "top3_probabilities": [0.42850765585899353, 0.37749171257019043, 0.1357589215040207], "all_probabilities": [0.010288918390870094, 0.42850765585899353, 0.013689628802239895, 0.013024956919252872, 0.37749171257019043, 0.003375801956281066, 0.008508464321494102, 0.0077048479579389095, 0.1357589215040207, 0.0016490250127390027], "raw_outputs": [-0.7468298077583313, 2.9824113845825195, -0.46125856041908264, -0.5110299587249756, 2.855651378631592, -1.8612642288208008, -0.9368358254432678, -1.036047339439392, 1.8329834938049316, -2.5777132511138916]}, {"success": true, "video_name": "4.webm", "video_path": "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on/4.webm", "video_shape": [1, 32, 96, 96], "value_range": [0.0, 0.886274516582489], "top_prediction": "i feel anxious", "top_confidence": 0.4289185106754303, "confidence_level": "Low", "confidence_emoji": "🟠", "top3_phrases": ["i feel anxious", "i need to sit up", "where am i"], "top3_probabilities": [0.4289185106754303, 0.3969191908836365, 0.11782904714345932], "all_probabilities": [0.009221675805747509, 0.4289185106754303, 0.013828057795763016, 0.013524936512112617, 0.3969191908836365, 0.0036004206631332636, 0.007757535204291344, 0.006644033826887608, 0.11782904714345932, 0.0017565450398251414], "raw_outputs": [-0.8488003015518188, 2.990910053253174, -0.4436571002006531, -0.46582186222076416, 2.9133758544921875, -1.7893059253692627, -1.0216923952102661, -1.1766377687454224, 1.6988779306411743, -2.5070078372955322]}, {"success": true, "video_name": "5.webm", "video_path": "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on/5.webm", "video_shape": [1, 32, 96, 96], "value_range": [0.0, 0.886274516582489], "top_prediction": "i feel anxious", "top_confidence": 0.4241672456264496, "confidence_level": "Low", "confidence_emoji": "🟠", "top3_phrases": ["i feel anxious", "i need to sit up", "where am i"], "top3_probabilities": [0.4241672456264496, 0.399111270904541, 0.11980228126049042], "all_probabilities": [0.009510314092040062, 0.4241672456264496, 0.014228208921849728, 0.01287190243601799, 0.399111270904541, 0.003459865925833583, 0.008209277875721455, 0.0068939016200602055, 0.11980228126049042, 0.001745830406434834], "raw_outputs": [-0.8196233510971069, 2.9781277179718018, -0.4167734980583191, -0.5169532299041748, 2.9172401428222656, -1.8307701349258423, -0.9667351245880127, -1.1413626670837402, 1.7138426303863525, -2.5147697925567627]}]}