#!/usr/bin/env python3
"""
Test the training setup before running full training
"""

import sys
import yaml
import torch
import pandas as pd
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.model import Mobile3DTiny
from reference_data_loader import create_reference_dataloaders

def test_training_setup():
    """Test all components of the training setup"""
    
    print("🧪 Testing Reference Video Training Setup")
    print("=" * 45)
    
    # Test 1: Configuration loading
    print("1️⃣ Testing configuration loading...")
    try:
        with open('configs/reference_training.yaml', 'r') as f:
            config = yaml.safe_load(f)
        print("   ✅ Configuration loaded successfully")
        print(f"   📊 Batch size: {config['training']['batch_size']}")
        print(f"   📊 Learning rate: {config['training']['learning_rate']}")
        print(f"   📊 Epochs: {config['training']['num_epochs']}")
    except Exception as e:
        print(f"   ❌ Configuration loading failed: {e}")
        return False
    
    # Test 2: Manifest loading
    print("\n2️⃣ Testing manifest loading...")
    try:
        manifest_path = config['data']['manifest_path']
        manifest_df = pd.read_csv(manifest_path)
        print(f"   ✅ Manifest loaded: {len(manifest_df)} videos")
        
        # Check phrase distribution
        phrase_counts = manifest_df['phrase'].value_counts()
        print(f"   📊 Unique phrases: {len(phrase_counts)}")
        print(f"   📊 Videos per phrase: {phrase_counts.min()}-{phrase_counts.max()}")
        
    except Exception as e:
        print(f"   ❌ Manifest loading failed: {e}")
        return False
    
    # Test 3: Model initialization
    print("\n3️⃣ Testing model initialization...")
    try:
        model = Mobile3DTiny(num_classes=config['model']['num_classes'])
        print(f"   ✅ Model created: {model.__class__.__name__}")
        print(f"   📊 Parameters: {model.get_num_parameters():,}")
        print(f"   📊 Classes: {config['model']['num_classes']}")
    except Exception as e:
        print(f"   ❌ Model initialization failed: {e}")
        return False
    
    # Test 4: Data loader creation
    print("\n4️⃣ Testing data loader creation...")
    try:
        # Modify config for testing
        test_config = config.copy()
        test_config['training']['batch_size'] = 2
        test_config['hardware']['num_workers'] = 0
        
        train_loader, val_loader, test_loader, data_info = create_reference_dataloaders(
            test_config, manifest_path
        )
        
        print(f"   ✅ Data loaders created successfully")
        print(f"   📊 Train: {data_info['train_size']} videos")
        print(f"   📊 Val: {data_info['val_size']} videos")
        print(f"   📊 Test: {data_info['test_size']} videos")
        
    except Exception as e:
        print(f"   ❌ Data loader creation failed: {e}")
        return False
    
    # Test 5: Sample batch processing
    print("\n5️⃣ Testing sample batch processing...")
    try:
        # Get first batch
        for videos, labels, metadata in train_loader:
            print(f"   ✅ Batch loaded successfully")
            print(f"   📊 Video shape: {videos.shape}")
            print(f"   📊 Label shape: {labels.shape}")
            print(f"   📊 Video range: [{videos.min():.3f}, {videos.max():.3f}]")
            
            # Test forward pass
            model.eval()
            with torch.no_grad():
                outputs = model(videos)
                print(f"   📊 Output shape: {outputs.shape}")
                print(f"   ✅ Forward pass successful")
            
            break  # Only test first batch
            
    except Exception as e:
        print(f"   ❌ Batch processing failed: {e}")
        return False
    
    # Test 6: Directory creation
    print("\n6️⃣ Testing directory creation...")
    try:
        checkpoint_dir = Path(config['checkpoints']['save_dir'])
        log_dir = Path(config['logging']['log_dir'])
        
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        log_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"   ✅ Directories created")
        print(f"   📁 Checkpoints: {checkpoint_dir}")
        print(f"   📁 Logs: {log_dir}")
        
    except Exception as e:
        print(f"   ❌ Directory creation failed: {e}")
        return False
    
    print(f"\n🎉 All Tests Passed!")
    print("=" * 20)
    print("✅ Configuration loading")
    print("✅ Manifest loading")
    print("✅ Model initialization")
    print("✅ Data loader creation")
    print("✅ Batch processing")
    print("✅ Directory creation")
    print("\n🚀 Ready to start training!")
    
    return True

def show_training_preview():
    """Show preview of what training will do"""
    
    print(f"\n📋 Training Preview")
    print("=" * 20)
    
    with open('configs/reference_training.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    manifest_df = pd.read_csv(config['data']['manifest_path'])
    
    print(f"🎯 Training Objective:")
    print(f"   Dataset: 80 mouth-cropped reference videos")
    print(f"   Task: 26-class ICU phrase classification")
    print(f"   Model: Mobile3DTiny ({Mobile3DTiny(26).get_num_parameters():,} parameters)")
    
    print(f"\n📊 Dataset Composition:")
    phrase_counts = manifest_df['phrase'].value_counts()
    for phrase, count in phrase_counts.head(10).items():
        print(f"   {phrase.title()}: {count} videos")
    if len(phrase_counts) > 10:
        print(f"   ... and {len(phrase_counts) - 10} more phrases")
    
    print(f"\n⚙️ Training Configuration:")
    print(f"   Batch size: {config['training']['batch_size']}")
    print(f"   Learning rate: {config['training']['learning_rate']}")
    print(f"   Max epochs: {config['training']['num_epochs']}")
    print(f"   Early stopping: {config['training']['early_stopping']['enabled']}")
    print(f"   Regularization: Dropout, Label smoothing, Weight decay")
    
    print(f"\n📈 Expected Outcomes:")
    print(f"   Training curves and metrics")
    print(f"   Model checkpoints")
    print(f"   Confusion matrix")
    print(f"   Comprehensive training report")

def main():
    """Main test function"""
    
    # Run setup tests
    success = test_training_setup()
    
    if success:
        # Show training preview
        show_training_preview()
        
        print(f"\n🎬 Ready to Train!")
        print("=" * 20)
        print("Run: python train_reference_classifier.py")
    else:
        print(f"\n❌ Setup tests failed. Please fix issues before training.")

if __name__ == '__main__':
    main()
