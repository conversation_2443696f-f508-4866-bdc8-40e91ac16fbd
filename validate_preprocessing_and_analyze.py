#!/usr/bin/env python3
"""
Validate video preprocessing pipeline and analyze specific desktop video
Tests enhanced preprocessing and analyzes /Users/<USER>/Desktop/630pm.webm
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
from pathlib import Path
import sys
import json
import time
from typing import List, Dict, Optional, Tuple

# Add current directory to path
sys.path.append('.')

from lipnet_perfect_10 import LipNetPerfect10, LipNetPerfect10Manager
from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class PreprocessingValidator:
    """Validator for enhanced preprocessing pipeline and video analysis"""
    
    def __init__(self):
        """Initialize the preprocessing validator"""
        
        self.device = torch.device('cpu')
        
        # Perfect 10 phrases
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        
        # Model paths
        self.lipnet_model_path = "checkpoints/lipnet_perfect_10/best_lipnet_perfect_10_model.pth"
        self.mobile3d_model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
        
        # Models
        self.active_model = None
        self.active_model_name = None
        
        # Enhanced video processor with exact specifications
        self.video_processor = VideoProcessor(
            target_frames=64,
            target_size=(112, 112),
            grayscale=True,
            fps=25.0,
            mouth_crop=None,  # Will be set based on video type
            use_dataset_normalization=True
        )
        
        print(f"🔧 Preprocessing Validator Initialized")
        print(f"   Enhanced specifications: 64 frames, 112×112, z-score normalization")
        print(f"   Dataset normalization: mean=0.578564, std=0.141477")
        print(f"   ICU mouth-cropping: (133, 0, 133, 100) for non-pre-cropped videos")
    
    def validate_preprocessing_pipeline(self) -> bool:
        """Validate the enhanced preprocessing pipeline specifications"""
        
        print(f"\n🔧 PREPROCESSING PIPELINE VALIDATION")
        print("=" * 45)
        
        validation_passed = True
        
        # Check VideoProcessor configuration
        print(f"📊 Enhanced Preprocessing Specifications:")
        print(f"   Target frames: {self.video_processor.target_frames} (expected: 64)")
        print(f"   Target size: {self.video_processor.target_size} (expected: (112, 112))")
        print(f"   FPS standardization: {self.video_processor.fps} (expected: 25.0)")
        print(f"   Z-score normalization: {self.video_processor.use_dataset_normalization} (expected: True)")
        
        # Validate specifications
        if self.video_processor.target_frames != 64:
            print(f"   ❌ Frame count mismatch: {self.video_processor.target_frames} != 64")
            validation_passed = False
        else:
            print(f"   ✅ Frame count: 64 frames")
        
        if self.video_processor.target_size != (112, 112):
            print(f"   ❌ Resolution mismatch: {self.video_processor.target_size} != (112, 112)")
            validation_passed = False
        else:
            print(f"   ✅ Resolution: 112×112 pixels")
        
        if self.video_processor.fps != 25.0:
            print(f"   ❌ FPS mismatch: {self.video_processor.fps} != 25.0")
            validation_passed = False
        else:
            print(f"   ✅ FPS standardization: 25 FPS")
        
        if not self.video_processor.use_dataset_normalization:
            print(f"   ❌ Z-score normalization disabled")
            validation_passed = False
        else:
            print(f"   ✅ Z-score normalization: enabled")
        
        # Check normalization constants
        expected_mean = 0.578564
        expected_std = 0.141477
        
        if abs(self.video_processor.DATASET_MEAN - expected_mean) > 0.001:
            print(f"   ❌ Dataset mean mismatch: {self.video_processor.DATASET_MEAN} != {expected_mean}")
            validation_passed = False
        else:
            print(f"   ✅ Dataset mean: {self.video_processor.DATASET_MEAN}")
        
        if abs(self.video_processor.DATASET_STD - expected_std) > 0.001:
            print(f"   ❌ Dataset std mismatch: {self.video_processor.DATASET_STD} != {expected_std}")
            validation_passed = False
        else:
            print(f"   ✅ Dataset std: {self.video_processor.DATASET_STD}")
        
        return validation_passed
    
    def test_preprocessing_on_samples(self) -> bool:
        """Test preprocessing pipeline on sample videos"""
        
        print(f"\n🧪 Testing Preprocessing on Sample Videos")
        print("=" * 40)
        
        # Find sample videos for testing
        test_sources = [
            "/Users/<USER>/Desktop/processed_perfect_10_videos/",
            "/Users/<USER>/Desktop/reference videos for training",
            "/Users/<USER>/Desktop/icu-videos-today"
        ]
        
        sample_videos = []
        for source in test_sources:
            source_path = Path(source)
            if source_path.exists():
                video_files = list(source_path.glob("*.mp4")) + list(source_path.glob("*.webm"))
                if video_files:
                    sample_videos.extend(video_files[:2])  # Take first 2 from each source
                    break
        
        if not sample_videos:
            print(f"⚠️  No sample videos found for testing")
            return False
        
        # Test on up to 3 sample videos
        test_videos = sample_videos[:3]
        all_tests_passed = True
        
        for i, video_path in enumerate(test_videos, 1):
            print(f"\n🎬 Test Video {i}: {video_path.name}")
            
            try:
                # Detect if pre-cropped
                is_pre_cropped = "_mouth_cropped" in video_path.name or "processed_" in video_path.name
                
                # Set mouth cropping
                if is_pre_cropped:
                    self.video_processor.mouth_crop = None
                    print(f"   🔧 Pre-cropped video detected, no additional cropping")
                else:
                    self.video_processor.mouth_crop = (133, 0, 133, 100)
                    print(f"   🔧 Original video, applying ICU mouth-cropping: (133, 0, 133, 100)")
                
                # Process video
                start_time = time.time()
                video_tensor = self.video_processor.process_video(str(video_path))
                processing_time = (time.time() - start_time) * 1000
                
                # Validate tensor properties
                expected_shape = torch.Size([1, 64, 112, 112])
                actual_shape = video_tensor.shape
                
                min_val = float(video_tensor.min())
                max_val = float(video_tensor.max())
                
                print(f"   📊 Tensor shape: {actual_shape} (expected: {expected_shape})")
                print(f"   📊 Value range: [{min_val:.3f}, {max_val:.3f}] (expected: ~[-4, +3])")
                print(f"   ⏱️  Processing time: {processing_time:.0f}ms")
                
                # Validate shape
                if actual_shape != expected_shape:
                    print(f"   ❌ Shape validation failed")
                    all_tests_passed = False
                else:
                    print(f"   ✅ Shape validation passed")
                
                # Validate value range (z-score normalized should be roughly [-4, +3])
                if min_val < -5 or max_val > 5:
                    print(f"   ⚠️  Value range outside expected bounds")
                else:
                    print(f"   ✅ Value range validation passed")
                
            except Exception as e:
                print(f"   ❌ Processing failed: {e}")
                all_tests_passed = False
        
        return all_tests_passed
    
    def load_best_available_model(self) -> bool:
        """Load the best available model in priority order"""
        
        print(f"\n🤖 Loading Best Available Model")
        print("=" * 35)
        
        # Try LipNet first (priority)
        if Path(self.lipnet_model_path).exists():
            try:
                print(f"🧠 Loading LipNet Perfect 10...")
                checkpoint = torch.load(self.lipnet_model_path, map_location=self.device)
                
                manager = LipNetPerfect10Manager()
                self.active_model = manager.create_model(
                    hidden_dim=256,
                    num_rnn_layers=2,
                    rnn_type='LSTM',
                    dropout=0.3
                )
                
                self.active_model.load_state_dict(checkpoint['model_state_dict'])
                self.active_model.to(self.device)
                self.active_model.eval()
                self.active_model_name = "LipNet Perfect 10"
                
                accuracy = checkpoint.get('best_val_accuracy', 0.0)
                print(f"   ✅ LipNet loaded: {self.active_model.get_num_parameters():,} params, {accuracy:.1%} accuracy")
                return True
                
            except Exception as e:
                print(f"   ❌ LipNet loading failed: {e}")
        else:
            print(f"🧠 LipNet model not found: {self.lipnet_model_path}")
        
        # Try Mobile3DTiny (fallback)
        if Path(self.mobile3d_model_path).exists():
            try:
                print(f"📱 Loading Enhanced Mobile3DTiny...")
                checkpoint = torch.load(self.mobile3d_model_path, map_location=self.device)
                
                self.active_model = Perfect10Mobile3DTiny(num_classes=10)
                self.active_model.load_state_dict(checkpoint['model_state_dict'])
                self.active_model.to(self.device)
                self.active_model.eval()
                self.active_model_name = "Enhanced Mobile3DTiny"
                
                accuracy = checkpoint.get('best_val_accuracy', 0.0)
                print(f"   ✅ Mobile3DTiny loaded: {self.active_model.get_num_parameters():,} params, {accuracy:.1%} accuracy")
                return True
                
            except Exception as e:
                print(f"   ❌ Mobile3DTiny loading failed: {e}")
        else:
            print(f"📱 Mobile3DTiny model not found: {self.mobile3d_model_path}")
        
        print(f"\n❌ No models available for analysis")
        print(f"💡 Training instructions:")
        print(f"   LipNet: python lipnet_perfect_10_training.py")
        print(f"   Mobile3DTiny: python enhanced_perfect_10_training.py")
        
        return False
    
    def create_temporal_crops(self, video_tensor: torch.Tensor) -> List[torch.Tensor]:
        """Create 3 temporal crops for Test-Time Augmentation"""
        
        C, T, H, W = video_tensor.shape
        target_frames = 64
        
        if T <= target_frames:
            return [video_tensor]
        
        crops = []
        
        # Crop 1: Beginning
        crop1 = video_tensor[:, :target_frames, :, :]
        crops.append(crop1)
        
        # Crop 2: Middle
        middle = T // 2
        start_middle = max(0, middle - target_frames // 2)
        end_middle = min(T, start_middle + target_frames)
        crop2 = video_tensor[:, start_middle:end_middle, :, :]
        
        if crop2.shape[1] < target_frames:
            padding_needed = target_frames - crop2.shape[1]
            last_frame = crop2[:, -1:, :, :].repeat(1, padding_needed, 1, 1)
            crop2 = torch.cat([crop2, last_frame], dim=1)
        
        crops.append(crop2)
        
        # Crop 3: End
        crop3 = video_tensor[:, -target_frames:, :, :]
        crops.append(crop3)
        
        return crops
    
    def analyze_target_video(self, video_path: str) -> Dict:
        """Analyze the target video with enhanced preprocessing and best model"""
        
        video_name = Path(video_path).name
        print(f"\n📹 Analyzing Target Video: {video_name}")
        print("=" * 50)
        
        if not Path(video_path).exists():
            # Search for video in common locations
            search_locations = [
                "/Users/<USER>/Desktop/",
                "/Users/<USER>/Desktop/processed_perfect_10_videos/",
                "/Users/<USER>/Desktop/reference videos for training/",
                "/Users/<USER>/Desktop/icu-videos-today/"
            ]
            
            print(f"❌ Video not found: {video_path}")
            print(f"🔍 Searching common locations...")
            
            for location in search_locations:
                location_path = Path(location)
                if location_path.exists():
                    found_files = list(location_path.glob("630pm*")) + list(location_path.glob("*630pm*"))
                    if found_files:
                        print(f"   📁 Found in {location}: {[f.name for f in found_files]}")
                        video_path = str(found_files[0])
                        video_name = found_files[0].name
                        break
                    else:
                        video_files = list(location_path.glob("*.mp4")) + list(location_path.glob("*.webm"))
                        if video_files:
                            print(f"   📁 Available in {location}: {len(video_files)} videos")
            
            if not Path(video_path).exists():
                return {
                    'success': False,
                    'error': f'Video file not found: {video_path}',
                    'searched_locations': search_locations
                }
        
        if not self.active_model:
            return {
                'success': False,
                'error': 'No model loaded for analysis'
            }
        
        try:
            # Detect video type and set preprocessing
            is_pre_cropped = "_mouth_cropped" in video_name or "processed_" in video_name
            
            if is_pre_cropped:
                self.video_processor.mouth_crop = None
                print(f"🔧 Pre-cropped video detected, no additional mouth-cropping")
            else:
                self.video_processor.mouth_crop = (133, 0, 133, 100)
                print(f"🔧 Original video, applying ICU mouth-cropping: (133, 0, 133, 100)")
            
            # Start timing
            start_time = time.time()
            
            # Process video with enhanced pipeline
            video_tensor = self.video_processor.process_video(video_path)
            
            print(f"📊 Processing: {list(video_tensor.shape)}, range [{video_tensor.min():.3f}, {video_tensor.max():.3f}]")
            
            # Apply TTA if video is long enough
            if video_tensor.shape[1] > 64:
                temporal_crops = self.create_temporal_crops(video_tensor)
                print(f"🔄 TTA: {len(temporal_crops)} temporal crops")
                
                all_logits = []
                for crop in temporal_crops:
                    crop_batch = crop.unsqueeze(0).to(self.device)
                    with torch.no_grad():
                        logits = self.active_model(crop_batch)
                        all_logits.append(logits[0])
                
                final_logits = torch.stack(all_logits).mean(dim=0)
                probabilities = F.softmax(final_logits, dim=0)
                tta_used = True
                
            else:
                video_batch = video_tensor.unsqueeze(0).to(self.device)
                with torch.no_grad():
                    logits = self.active_model(video_batch)
                    final_logits = logits[0]
                    probabilities = F.softmax(final_logits, dim=0)
                tta_used = False
            
            # End timing
            inference_time = (time.time() - start_time) * 1000
            
            # Get predictions
            top3_probs, top3_indices = torch.topk(probabilities, 3)
            top3_probs = top3_probs.cpu().numpy()
            top3_indices = top3_indices.cpu().numpy()
            top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
            
            # Determine confidence level
            top_confidence = float(top3_probs[0])
            if top_confidence >= 0.8:
                confidence_level = "Very High"
                confidence_emoji = "🟢"
            elif top_confidence >= 0.6:
                confidence_level = "High"
                confidence_emoji = "🟡"
            elif top_confidence >= 0.4:
                confidence_level = "Moderate"
                confidence_emoji = "🟠"
            else:
                confidence_level = "Low"
                confidence_emoji = "🔴"
            
            return {
                'success': True,
                'video_path': video_path,
                'video_name': video_name,
                'model_used': self.active_model_name,
                'tensor_shape': list(video_tensor.shape),
                'value_range': [float(video_tensor.min()), float(video_tensor.max())],
                'tta_used': tta_used,
                'is_pre_cropped': is_pre_cropped,
                'top_prediction': top3_phrases[0],
                'top_confidence': top_confidence,
                'confidence_level': confidence_level,
                'confidence_emoji': confidence_emoji,
                'top3_phrases': top3_phrases,
                'top3_probabilities': top3_probs.tolist(),
                'all_probabilities': probabilities.cpu().numpy().tolist(),
                'inference_time_ms': inference_time
            }
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'video_path': video_path
            }
    
    def display_results(self, preprocessing_passed: bool, sample_tests_passed: bool, analysis_result: Dict):
        """Display formatted results"""
        
        print(f"\n" + "="*60)
        print(f"🎯 PREPROCESSING VALIDATION & VIDEO ANALYSIS RESULTS")
        print(f"="*60)
        
        # Preprocessing validation
        preprocessing_status = "PASSED" if preprocessing_passed else "FAILED"
        print(f"🔧 PREPROCESSING VALIDATION: {preprocessing_status}")
        
        if not preprocessing_passed:
            print(f"   ⚠️  Enhanced preprocessing pipeline has configuration issues")
        
        if not sample_tests_passed:
            print(f"   ⚠️  Sample video testing failed")
        
        # Video analysis results
        if not analysis_result['success']:
            print(f"❌ Video Analysis Failed: {analysis_result['error']}")
            return
        
        result = analysis_result
        
        print(f"📹 Video: {result['video_name']}")
        print(f"🤖 Model: {result['model_used']}")
        print(f"📊 Processing: {result['tensor_shape']}, range [{result['value_range'][0]:.3f}, {result['value_range'][1]:.3f}], TTA: {'Yes' if result['tta_used'] else 'No'}")
        
        print(f"\n🏆 TOP PREDICTION: \"{result['top_prediction'].title()}\" ({result['top_confidence']:.1%} confidence)")
        print(f"Confidence Level: {result['confidence_emoji']} {result['confidence_level']}")
        
        print(f"\n📊 TOP-3 PREDICTIONS:")
        rank_emojis = ["🥇", "🥈", "🥉"]
        for i, (phrase, prob) in enumerate(zip(result['top3_phrases'], result['top3_probabilities'])):
            print(f"{rank_emojis[i]} {phrase.title()}: {prob:.1%}")
        
        print(f"\n⏱️ Inference Time: {result['inference_time_ms']:.0f}ms")
        
        # Perfect 10 phrase comparison
        print(f"\n🎯 Perfect 10 ICU Phrases Comparison:")
        all_probs = result['all_probabilities']
        for i, (phrase, prob) in enumerate(zip(self.perfect_phrases, all_probs)):
            if prob >= 0.05:  # Show significant predictions
                status = "🎯" if phrase == result['top_prediction'] else "  "
                print(f"   {status} {phrase.title()}: {prob:.1%}")

def main():
    """Main validation and analysis function"""
    
    print("🔧 Enhanced Preprocessing Validation & Video Analysis")
    print("=" * 55)
    print("Validating preprocessing pipeline and analyzing /Users/<USER>/Desktop/630pm.webm")
    
    # Initialize validator
    validator = PreprocessingValidator()
    
    # Step 1: Validate preprocessing pipeline
    preprocessing_passed = validator.validate_preprocessing_pipeline()
    
    # Step 2: Test preprocessing on sample videos
    sample_tests_passed = validator.test_preprocessing_on_samples()
    
    # Step 3: Load best available model
    model_loaded = validator.load_best_available_model()
    
    if not model_loaded:
        print(f"\n❌ Cannot proceed without a trained model")
        return
    
    # Step 4: Analyze target video
    target_video = "/Users/<USER>/Desktop/630pm.webm"
    analysis_result = validator.analyze_target_video(target_video)
    
    # Step 5: Display comprehensive results
    validator.display_results(preprocessing_passed, sample_tests_passed, analysis_result)
    
    # Save results
    output_data = {
        'preprocessing_validation': {
            'passed': preprocessing_passed,
            'sample_tests_passed': sample_tests_passed
        },
        'video_analysis': analysis_result,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    output_file = "preprocessing_validation_and_analysis.json"
    with open(output_file, 'w') as f:
        json.dump(output_data, f, indent=2, default=str)
    
    print(f"\n💾 Results saved: {output_file}")
    print(f"\n🎉 Preprocessing Validation & Video Analysis Complete!")

if __name__ == '__main__':
    main()
