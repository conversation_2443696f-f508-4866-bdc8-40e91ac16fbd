#!/usr/bin/env python3
"""
Visual validation tool for corrected mouth region detection.
Extracts frames from the corrected processed video to verify lip visibility.
"""

import cv2
import numpy as np
from pathlib import Path

def extract_validation_frames(video_path, output_prefix="corrected_frame"):
    """Extract frames from corrected video for visual validation"""
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        raise ValueError(f"Cannot open video: {video_path}")
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"📹 Corrected Video Analysis:")
    print(f"   Resolution: {width}x{height}")
    print(f"   FPS: {fps}")
    print(f"   Total frames: {total_frames}")
    print(f"   Duration: {total_frames/fps:.2f} seconds")
    
    # Extract frames at different positions to show lip movement progression
    frame_indices = [0, 15, 30, 45, 60, 74]  # Beginning, middle, end
    
    frames_extracted = []
    for i, frame_idx in enumerate(frame_indices):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if ret:
            # Scale up the frame for better visibility (140x46 is very small)
            scaled_frame = cv2.resize(frame, (560, 184), interpolation=cv2.INTER_NEAREST)
            
            # Add frame info
            cv2.putText(scaled_frame, f"Frame {frame_idx}/{total_frames-1}", (10, 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(scaled_frame, f"Time: {frame_idx/fps:.2f}s", (10, 45), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(scaled_frame, "CORRECTED MOUTH DETECTION", (10, 170), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            output_file = f"{output_prefix}_{i:02d}_frame{frame_idx}.jpg"
            cv2.imwrite(output_file, scaled_frame)
            frames_extracted.append(output_file)
            print(f"   💾 Saved {output_file} (scaled 4x for visibility)")
    
    cap.release()
    return frames_extracted

def create_comparison_grid(frames):
    """Create a grid showing the progression of lip movements"""
    if len(frames) < 6:
        print("❌ Not enough frames for grid")
        return
    
    # Load all frames
    frame_images = []
    for frame_file in frames:
        img = cv2.imread(frame_file)
        if img is not None:
            frame_images.append(img)
    
    if len(frame_images) < 6:
        print("❌ Could not load all frames")
        return
    
    # Create 2x3 grid
    top_row = np.hstack([frame_images[0], frame_images[1], frame_images[2]])
    bottom_row = np.hstack([frame_images[3], frame_images[4], frame_images[5]])
    grid = np.vstack([top_row, bottom_row])
    
    # Add title
    title_height = 50
    title_img = np.zeros((title_height, grid.shape[1], 3), dtype=np.uint8)
    cv2.putText(title_img, "CORRECTED MOUTH DETECTION - LIP MOVEMENT PROGRESSION", 
               (20, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
    
    final_grid = np.vstack([title_img, grid])
    
    cv2.imwrite("corrected_lip_movement_grid.jpg", final_grid)
    print(f"   💾 Saved corrected_lip_movement_grid.jpg")
    
    return "corrected_lip_movement_grid.jpg"

def main():
    video_path = "data/where_am_i/where_am_i__useruser01__18to39__male__not_specified__20250809T053449_processed.mp4"
    
    if not Path(video_path).exists():
        print(f"❌ Video file not found: {video_path}")
        return
    
    print(f"🔍 Validating corrected mouth detection in: {Path(video_path).name}")
    print(f"🎯 Expected: Clear lip movements visible in 140x46 pixel frames")
    print(f"🎯 Success criteria: Lips should be prominently displayed, not chin/neck area")
    print()
    
    # Extract validation frames
    frames = extract_validation_frames(video_path)
    
    # Create comparison grid
    grid_file = create_comparison_grid(frames)
    
    print()
    print(f"✅ Validation frames extracted!")
    print(f"📁 Individual frames: {len(frames)} files")
    print(f"📁 Comparison grid: {grid_file}")
    print()
    print(f"🔍 VISUAL INSPECTION CHECKLIST:")
    print(f"   ✅ Are lips clearly visible in all frames?")
    print(f"   ✅ Is the mouth region properly centered?")
    print(f"   ✅ Can you see lip movement progression for 'Where am I?'")
    print(f"   ✅ Is there NO chin/neck area visible?")
    print(f"   ✅ Are the frames suitable for lipreading training?")

if __name__ == "__main__":
    main()
