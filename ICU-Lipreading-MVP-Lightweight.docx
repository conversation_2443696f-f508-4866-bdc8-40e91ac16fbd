ICU‑Lipreading MVP (Lightweight, Commercial‑Safe)

A Python 3.9+ backend for ICU lipreading that **does not use LipNet**.
It implements a **lightweight, visual‑only phrase classifier** (26 phrases) you own end‑to‑end (code + weights), and serves predictions via FastAPI/Flask.

* **No GRID, no research‑only weights.**
* **Direct phrase classification (softmax over 26 classes).**
* **Demographics evaluation** (age, gender, ethnicity) alongside overall metrics.
* **Feature‑flagged** so you can flip between legacy `/predict` and the new `/predict_v2` without touching old code.

---

## Why this change (in 15 seconds)

* LipNet's licence / pretrained weights are research‑only → not safe commercially.
* Your product needs **fixed vocabulary** (26 ICU phrases), not open‑ended lipreading.
* A **small 3D‑CNN + BiGRU** trained only on your dataset is faster, simpler, and typically **more accurate** for this task.

---

## Repo layout (new bits only)

```
backend/
  api/
    app.py                 # legacy endpoints stay; add /predict_v2 (new)
  lightweight_vsr/         # NEW: isolated module (no legacy deps)
    dataset.py
    model.py
    train.py
    infer.py
    utils_video.py
    metrics.py
    requirements.txt
    README.md
configs/
  phrases26.yaml           # class list + model dims + threshold
artifacts/                 # checkpoints, exports (gitignored)
scripts/
  run_train.sh
  export_torchscript.sh
  benchmark_infer.sh
```

> Everything in `backend/lightweight_vsr/` is **new and standalone**. You can keep your legacy code untouched and switch with an env flag.

---

## Quick start — **10 steps to ship by tomorrow**

1. **Create feature branch**

   ```bash
   git checkout -b feat/lightweight-vsr
   ```

2. **Set up a clean env**

   ```bash
   python -m venv .venv_vsr
   source .venv_vsr/bin/activate
   pip install -r backend/lightweight_vsr/requirements.txt
   ```

3. **Prepare a manifest CSV** (one row per video)

   ```
   data/manifest.csv
   video_path,speaker_id,phrase,age_group,gender,ethnicity,lighting
   /abs/path/vid001.mp4,spk001,i'm in pain,65-80,F,Aboriginal,indoor_bright
   ...
   ```

4. **Fill labels/config**

   ```yaml
   # configs/phrases26.yaml
   phrases:
     - "i'm in pain"
     - "i can't breathe"
     # ... 26 total, fixed order
   frames: 32
   height: 96
   width: 96
   grayscale: true
   confidence_threshold: 0.6
   ```

5. **Train (speaker‑wise split; class‑weighted loss)**

   ```bash
   python backend/lightweight_vsr/train.py \
     --manifest data/manifest.csv \
     --out_dir artifacts/vsr_26p_v1 \
     --config configs/phrases26.yaml \
     --epochs 40 --batch_size 16 --val_split 0.1 --test_split 0.1
   ```

6. **Target acceptance (stop if not met)**

   * **Val macro‑F1 ≥ 0.90**
   * **Worst‑phrase F1 ≥ 0.80**
   * **No demographic subgroup < (overall − 10 pts)**

7. **Export TorchScript**

   ```bash
   bash scripts/export_torchscript.sh
   # creates artifacts/vsr_26p_v1/model.ts
   ```

8. **Local inference smoke test**

   ```bash
   python -c "from backend.lightweight_vsr.infer import predict_phrase; \
              print(predict_phrase('test_webm_videos/sample.mp4'))"
   ```

9. **Serve API (new endpoint)**
   Add `/predict_v2` in `backend/api/app.py` to call `lightweight_vsr.infer.predict_phrase`, then:

   ```bash
   uvicorn backend.api.app:app --reload
   # POST /predict_v2 with a mouth video
   ```

10. **Flip switch (optional)**
    Route legacy `/predict` to new model only when ready:

```bash
export VSR_IMPL=lightweight   # default stays legacy if unset
```

Rollback: `unset VSR_IMPL` and restart.

---

## Data & preprocessing

* **Expected input**: short MP4/WebM mouth‑focused videos.
* **Frame pipeline**:

  * Extract **32 frames** around speech (trim/pad as needed).
  * Convert to **grayscale**; **resize to 96×96**.
  * Keep some nose/chin context; add **vertical jitter** (±8 px) to handle "mouth near top".
  * Augment: brightness/contrast ±15%, scale ±10%, slight temporal jitter (±4 frames).
* **Splits**: **speaker‑wise** 80/10/10 (no speaker leakage).
* **Imbalance**: automatic class weights; optional focal loss.

**Folder option (if you don't use a manifest):**

```
data/
  i'm in pain/
    spk001_clip01.mp4
    ...
  i can't breathe/
    ...
```

(Manifest is strongly preferred so you can track demographics and lighting.)

---

## Model (Mobile3D‑Tiny + BiGRU)

* **Backbone**: Depthwise‑separable 3D conv blocks (≤ \~8M params).
* **Temporal**: Spatial GAP → **BiGRU(256×2)** → mean over time.
* **Head**: LayerNorm → Dropout(0.2) → Linear(26) → Softmax.
* **Loss**: Weighted cross‑entropy (or focal).
* **Optim**: AdamW (lr=1e‑3, wd=1e‑2), cosine decay, warmup 1–2 epochs.
* **AMP**: Mixed precision on by default.

---

## Training

```bash
python backend/lightweight_vsr/train.py \
  --manifest data/manifest.csv \
  --out_dir artifacts/vsr_26p_v1 \
  --config configs/phrases26.yaml \
  --epochs 40 --batch_size 16 --val_split 0.1 --test_split 0.1 \
  --num_workers 4 --amp 1
```

Outputs in `artifacts/vsr_26p_v1/`:

* `best.ckpt`, `last.ckpt`
* `metrics.json` (overall macro‑F1, per‑phrase F1, confusion)
* `metrics_by_demo.json` (age, gender, ethnicity, lighting slices)
* `model.ts` (TorchScript after export)

**Tip to breach 0.90 quickly**

* Use **class weights** + oversample rare phrases.
* Verify **speaker‑wise** split (leakage will lie to you).
* Tune `confidence_threshold` (0.55–0.65 often best).
* If a phrase lags: add 10–20 extra samples or perform **hard‑negative mining** from its nearest confusions.

---

## Demographics evaluation (what you asked for)

After training, the trainer writes both:

* `artifacts/.../metrics.json` → overall + per‑phrase.
* `artifacts/.../metrics_by_demo.json` → the **same metrics per subgroup** for:

  * `age_group` (e.g., 18–40, 41–64, 65–80, 80+)
  * `gender` (as recorded)
  * `ethnicity` (as recorded)
  * `lighting` (indoor\_bright, indoor\_dim, etc.)

We report:

* Macro‑F1 and per‑phrase F1 for each subgroup.
* Deltas vs overall; flags any subgroup **>10 pts below** overall macro‑F1.

**Command to print a short fairness report:**

```bash
python - << 'PY'
import json, sys, os
p = 'artifacts/vsr_26p_v1/metrics_by_demo.json'
d = json.load(open(p))
overall = d['overall']['macro_f1']
for axis, groups in d['slices'].items():
    print(f'\n== {axis.upper()} ==')
    for g, m in groups.items():
        delta = m['macro_f1'] - overall
        flag = '  <-- CHECK' if delta < -0.10 else ''
        print(f'{g:15s} macroF1={m["macro_f1"]:.3f} (Δ {delta:+.3f}){flag}')
PY
```

---

## Export & inference

**Export TorchScript**

```bash
bash scripts/export_torchscript.sh
# or:
python - << 'PY'
import torch, os
from backend.lightweight_vsr.model import Mobile3DTiny
from backend.lightweight_vsr.infer import load_config
cfg = load_config('configs/phrases26.yaml')
m = Mobile3DTiny(num_classes=len(cfg['phrases']))
m.eval()
scripted = torch.jit.script(m)
os.makedirs('artifacts/vsr_26p_v1', exist_ok=True)
scripted.save('artifacts/vsr_26p_v1/model.ts')
print('saved TorchScript → artifacts/vsr_26p_v1/model.ts')
PY
```

**Run inference (Python)**

```python
from backend.lightweight_vsr.infer import predict_phrase
print(predict_phrase("test_webm_videos/sample.mp4"))
```

---

## API

**New endpoint (safe to test without touching legacy):**

```
POST /predict_v2
Form-data: file=<video>
Response:
{
  "phrase": "i'm in pain",
  "confidence": 0.82,
  "topk": [["i'm in pain",0.82], ["i can't breathe",0.09], ...]
}
```

**Optional: switch legacy `/predict` behind an env flag**

```bash
export VSR_IMPL=lightweight
# unset to roll back immediately
```

---

## Performance targets

* **Accuracy**: Val macro‑F1 ≥ 0.90, worst‑phrase F1 ≥ 0.80.
* **Fairness**: No subgroup macro‑F1 < (overall − 0.10).
* **Latency**: <150 ms total per 32‑frame clip on a T4; CPU feasible for small loads.
* **Model size**: <10 MB TorchScript.

---

## Docker (optional)

```bash
docker build -t icu-lightweight .
docker run -p 5000:5000 -e VSR_IMPL=lightweight icu-lightweight
```

---

## License

All code in `backend/lightweight_vsr/*` and new training artifacts are **your IP**.
Suggested licence: **Apache‑2.0** or **MIT** (choose one and add a LICENSE file).

---

## Troubleshooting quickies

* **Accuracy plateauing \~80%** → check **speaker leakage**; increase vertical/scale jitter; up‑weight rare classes.
* **Confusing similar phrases** → add 10–20 curated, clean examples per confusing pair; lower fps jitter.
* **Low subgroup score** → oversample that subgroup; add brightness/contrast aug tuned to its lighting; review crop alignment.

---

If you want, I can also generate the **exact files** (`train.py`, `model.py`, `infer.py`, etc.) that this README expects, ready to paste into `backend/lightweight_vsr/` so you can kick off a run immediately.
