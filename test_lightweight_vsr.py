#!/usr/bin/env python3
"""
Test script for lightweight VSR system
"""

import sys
import os
sys.path.append('.')

def test_imports():
    """Test all module imports"""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch not available: {e}")
        return False
    
    try:
        import yaml
        print("✅ PyYAML")
    except ImportError as e:
        print(f"❌ PyYAML not available: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV")
    except ImportError as e:
        print(f"❌ OpenCV not available: {e}")
        return False
    
    try:
        from backend.lightweight_vsr.model import Mobile3DTiny
        print("✅ Model module")
    except ImportError as e:
        print(f"❌ Model module: {e}")
        return False
    
    try:
        from backend.lightweight_vsr.utils_video import VideoProcessor
        print("✅ Video processing module")
    except ImportError as e:
        print(f"❌ Video processing module: {e}")
        return False
    
    try:
        from backend.lightweight_vsr.dataset import ICUVideoDataset
        print("✅ Dataset module")
    except ImportError as e:
        print(f"❌ Dataset module: {e}")
        return False
    
    return True


def test_model():
    """Test model creation and forward pass"""
    print("\nTesting model...")
    
    try:
        import torch
        from backend.lightweight_vsr.model import Mobile3DTiny
        
        # Create model
        model = Mobile3DTiny(num_classes=26)
        print(f"✅ Model created with {model.get_num_parameters():,} parameters")
        
        # Test forward pass
        x = torch.randn(2, 1, 32, 96, 96)
        logits = model(x)
        print(f"✅ Forward pass: {x.shape} -> {logits.shape}")
        
        # Check parameter count
        param_count = model.get_num_parameters()
        if param_count <= 8_000_000:
            print(f"✅ Parameter count {param_count:,} meets <8M target")
        else:
            print(f"⚠️  Parameter count {param_count:,} exceeds 8M target")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False


def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        import yaml
        
        config_path = "configs/phrases26.yaml"
        if not os.path.exists(config_path):
            print(f"❌ Config file not found: {config_path}")
            return False
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        print(f"✅ Config loaded with {len(config['phrases'])} phrases")
        
        # Check required fields
        required_fields = ['phrases', 'frames', 'height', 'width', 'grayscale', 'confidence_threshold']
        for field in required_fields:
            if field in config:
                print(f"✅ {field}: {config[field]}")
            else:
                print(f"❌ Missing required field: {field}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False


def test_manifest():
    """Test manifest file"""
    print("\nTesting manifest...")
    
    try:
        import pandas as pd
        
        manifest_path = "data/manifest.csv"
        if not os.path.exists(manifest_path):
            print(f"❌ Manifest file not found: {manifest_path}")
            return False
        
        df = pd.read_csv(manifest_path)
        print(f"✅ Manifest loaded with {len(df)} videos")
        
        # Check required columns
        required_columns = ['video_path', 'speaker_id', 'phrase', 'age_group', 'gender', 'ethnicity', 'lighting']
        for col in required_columns:
            if col in df.columns:
                print(f"✅ Column {col}: {df[col].nunique()} unique values")
            else:
                print(f"❌ Missing required column: {col}")
                return False
        
        # Check if video files exist
        existing_videos = df['video_path'].apply(os.path.exists).sum()
        print(f"✅ {existing_videos}/{len(df)} video files exist")
        
        if existing_videos == 0:
            print("❌ No video files found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Manifest test failed: {e}")
        return False


def test_video_processing():
    """Test video processing on a sample video"""
    print("\nTesting video processing...")
    
    try:
        from backend.lightweight_vsr.utils_video import VideoProcessor
        import pandas as pd
        
        # Load manifest to get a sample video
        df = pd.read_csv("data/manifest.csv")
        sample_video = df.iloc[0]['video_path']
        
        if not os.path.exists(sample_video):
            print(f"❌ Sample video not found: {sample_video}")
            return False
        
        # Create processor
        processor = VideoProcessor(target_frames=32, target_size=(96, 96), grayscale=True)
        
        # Process video
        video_tensor = processor.process_video(sample_video)
        print(f"✅ Video processed: {sample_video}")
        print(f"✅ Output shape: {video_tensor.shape}")
        print(f"✅ Value range: [{video_tensor.min():.3f}, {video_tensor.max():.3f}]")
        
        # Check expected shape
        expected_shape = (1, 32, 96, 96)  # (C, T, H, W)
        if video_tensor.shape == expected_shape:
            print(f"✅ Shape matches expected {expected_shape}")
        else:
            print(f"⚠️  Shape {video_tensor.shape} doesn't match expected {expected_shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Video processing test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("=" * 50)
    print("LIGHTWEIGHT VSR SYSTEM TEST")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Model", test_model),
        ("Configuration", test_config),
        ("Manifest", test_manifest),
        ("Video Processing", test_video_processing),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20s} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! System is ready for training.")
    else:
        print("⚠️  Some tests failed. Please fix issues before training.")
    
    return passed == len(results)


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
