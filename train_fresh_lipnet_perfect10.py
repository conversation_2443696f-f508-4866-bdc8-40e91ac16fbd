#!/usr/bin/env python3
"""
Train Fresh LipNet Perfect 10 Classifier from Scratch
Uses validated dataset to achieve >85% accuracy, replacing 30% baseline model
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, WeightedRandomSampler
import numpy as np
from pathlib import Path
import sys
import json
import time
from tqdm import tqdm
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, confusion_matrix
import logging

# Add current directory to path
sys.path.append('.')

from lipnet_perfect_10 import LipNetPerfect10, LipNetPerfect10Manager
from backend.lightweight_vsr.utils_video import VideoProcessor

class ValidatedPerfect10Dataset(Dataset):
    """Dataset using validated videos with perfect preprocessing"""
    
    def __init__(self, video_files: list, labels: list, video_processor: VideoProcessor, is_training: bool = True):
        
        self.video_files = video_files
        self.labels = labels
        self.video_processor = video_processor
        self.is_training = is_training
        
        # Perfect 10 phrases
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        print(f"📊 Validated Perfect 10 Dataset: {len(video_files)} samples")
        print(f"   Mode: {'Training' if is_training else 'Validation'}")
        print(f"   Validated preprocessing: [1, 64, 112, 112], z-score normalization")
    
    def __len__(self):
        return len(self.video_files)
    
    def __getitem__(self, idx):
        video_path = self.video_files[idx]
        label = self.labels[idx]
        
        try:
            # Detect if pre-cropped (all validated videos should be consistent)
            is_pre_cropped = "_mouth_cropped" in Path(video_path).name or "processed_" in Path(video_path).name
            
            # Set mouth cropping (same as validation pipeline)
            if is_pre_cropped:
                self.video_processor.mouth_crop = None
            else:
                self.video_processor.mouth_crop = (133, 0, 133, 100)
            
            # Process video with validated pipeline
            video_tensor = self.video_processor.process_video(video_path)
            
            # Ensure exact shape (should be guaranteed by validation)
            if video_tensor.shape != (1, 64, 112, 112):
                # Apply same corrections as validation
                C, T, H, W = video_tensor.shape
                if T != 64:
                    if T > 64:
                        start_idx = (T - 64) // 2
                        video_tensor = video_tensor[:, start_idx:start_idx + 64, :, :]
                    else:
                        padding_needed = 64 - T
                        last_frame = video_tensor[:, -1:, :, :].repeat(1, padding_needed, 1, 1)
                        video_tensor = torch.cat([video_tensor, last_frame], dim=1)
            
            # Final validation
            assert video_tensor.shape == (1, 64, 112, 112), f"Shape error: {video_tensor.shape}"
            
            return video_tensor, label
            
        except Exception as e:
            print(f"⚠️  Failed to load {Path(video_path).name}: {e}")
            # Return dummy tensor (should not happen with validated dataset)
            dummy_tensor = torch.zeros(1, 64, 112, 112)
            return dummy_tensor, label

class FreshLipNetTrainer:
    """Fresh LipNet trainer for validated dataset targeting >85% accuracy"""
    
    def __init__(self, validated_dataset_path: str):
        """Initialize fresh LipNet trainer"""
        
        self.validated_path = Path(validated_dataset_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Training configuration
        self.batch_size = 8  # Adjust based on memory
        self.learning_rate = 1e-4
        self.num_epochs = 50
        self.target_accuracy = 0.85  # >85% target
        
        # Model and training components
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        
        # Data loaders
        self.train_loader = None
        self.val_loader = None
        
        # Training tracking
        self.training_history = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': [],
            'learning_rates': []
        }
        
        self.best_val_accuracy = 0.0
        self.best_model_path = None
        
        # Setup logging
        self.setup_logging()
        
        print(f"🚀 Fresh LipNet Perfect 10 Trainer Initialized")
        print(f"   Validated dataset: {self.validated_path}")
        print(f"   Device: {self.device}")
        print(f"   Target accuracy: >{self.target_accuracy:.0%}")
        print(f"   Training epochs: {self.num_epochs}")
    
    def setup_logging(self):
        """Setup comprehensive logging"""
        
        log_dir = Path("checkpoints/lipnet_perfect10_fresh")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / "training_log.txt"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Fresh LipNet training started")
    
    def load_validated_dataset(self) -> bool:
        """Load validated dataset using training manifest"""
        
        print(f"\n📊 Loading Validated Dataset")
        print("=" * 30)
        
        # Load training manifest
        manifest_file = self.validated_path / "training_manifest.json"
        if not manifest_file.exists():
            print(f"❌ Training manifest not found: {manifest_file}")
            return False
        
        with open(manifest_file, 'r') as f:
            manifest = json.load(f)
        
        video_files = manifest['video_files']
        labels = manifest['labels']
        
        print(f"📊 Dataset loaded from manifest:")
        print(f"   Total videos: {len(video_files)}")
        print(f"   Phrase folders: {len(manifest['phrase_folders'])}")
        print(f"   Class mapping: {len(manifest['class_mapping'])} classes")
        
        # Enhanced video processor (identical to validation)
        video_processor = VideoProcessor(
            target_frames=64,
            target_size=(112, 112),
            grayscale=True,
            fps=25.0,
            mouth_crop=None,  # Set adaptively
            use_dataset_normalization=True
        )
        
        # Split data: 80% train, 20% validation
        np.random.seed(42)  # Reproducible splits
        indices = np.random.permutation(len(video_files))
        
        train_size = int(0.8 * len(video_files))
        train_indices = indices[:train_size]
        val_indices = indices[train_size:]
        
        # Create split datasets
        train_files = [video_files[i] for i in train_indices]
        train_labels = [labels[i] for i in train_indices]
        
        val_files = [video_files[i] for i in val_indices]
        val_labels = [labels[i] for i in val_indices]
        
        print(f"📊 Data splits: Train={len(train_files)}, Val={len(val_files)}")
        
        # Create datasets
        train_dataset = ValidatedPerfect10Dataset(train_files, train_labels, video_processor, is_training=True)
        val_dataset = ValidatedPerfect10Dataset(val_files, val_labels, video_processor, is_training=False)
        
        # Class balancing for training
        class_counts = torch.bincount(torch.tensor(train_labels), minlength=10)
        class_weights = 1.0 / class_counts.float()
        class_weights[class_counts == 0] = 0.0
        
        sample_weights = class_weights[train_labels]
        train_sampler = WeightedRandomSampler(
            weights=sample_weights,
            num_samples=len(sample_weights),
            replacement=True
        )
        
        print(f"⚖️  Class balancing applied for training")
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            sampler=train_sampler,
            num_workers=0,
            pin_memory=False
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=False
        )
        
        print(f"✅ Data loaders created successfully")
        return True
    
    def create_fresh_model(self):
        """Create fresh LipNet model (no pre-trained weights)"""
        
        print(f"\n🤖 Creating Fresh LipNet Perfect 10 Model")
        print("=" * 40)
        
        # Create fresh LipNet model
        manager = LipNetPerfect10Manager()
        self.model = manager.create_model(
            hidden_dim=256,
            num_rnn_layers=2,
            rnn_type='LSTM',
            dropout=0.3
        )
        
        self.model.to(self.device)
        
        print(f"✅ Fresh LipNet model created")
        print(f"   Parameters: {self.model.get_num_parameters():,}")
        print(f"   Architecture: 3D CNN + BiLSTM + 8-head Attention")
        print(f"   No pre-trained weights loaded (fresh training)")
    
    def setup_training_components(self):
        """Setup optimizer, scheduler, and loss function"""
        
        print(f"\n⚙️  Setting up Training Components")
        print("=" * 35)
        
        # Optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=0.0001
        )
        
        # Scheduler - ReduceLROnPlateau for adaptive learning
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='max',  # Monitor validation accuracy
            factor=0.5,
            patience=5
        )
        
        # Loss function
        self.criterion = nn.CrossEntropyLoss()
        
        print(f"✅ Training components ready")
        print(f"   Optimizer: AdamW (lr={self.learning_rate}, weight_decay=0.0001)")
        print(f"   Scheduler: ReduceLROnPlateau (patience=5)")
        print(f"   Loss: CrossEntropyLoss")
    
    def train_epoch(self, epoch: int):
        """Train one epoch"""
        
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        pbar = tqdm(self.train_loader, desc=f"Fresh LipNet Epoch {epoch+1}")
        
        for batch_idx, (videos, labels) in enumerate(pbar):
            videos, labels = videos.to(self.device), labels.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(videos)
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            
            self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'Acc': f"{100.*correct/total:.2f}%"
            })
        
        epoch_loss = total_loss / len(self.train_loader)
        epoch_acc = correct / total
        
        return epoch_loss, epoch_acc
    
    def validate(self):
        """Validate model"""
        
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for videos, labels in self.val_loader:
                videos, labels = videos.to(self.device), labels.to(self.device)
                
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                
                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()
                
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        val_loss = total_loss / len(self.val_loader)
        val_acc = correct / total
        
        return val_loss, val_acc, all_predictions, all_labels

    def save_checkpoint(self, epoch: int, val_accuracy: float, is_best: bool = False):
        """Save model checkpoint"""

        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_accuracy': self.best_val_accuracy,
            'training_history': self.training_history,
            'model_info': {
                'architecture': 'Fresh LipNet Perfect 10',
                'parameters': self.model.get_num_parameters(),
                'dataset': 'Validated Perfect 10 (100 videos)',
                'target_accuracy': f'>{self.target_accuracy:.0%}',
                'training_type': 'Fresh from scratch (no pre-trained weights)'
            }
        }

        # Save checkpoint directory
        checkpoint_dir = Path("checkpoints/lipnet_perfect10_fresh")
        checkpoint_dir.mkdir(parents=True, exist_ok=True)

        # Save regular checkpoint
        checkpoint_path = checkpoint_dir / f"fresh_lipnet_epoch_{epoch+1}.pth"
        torch.save(checkpoint, checkpoint_path)

        # Save best model
        if is_best:
            best_path = checkpoint_dir / "best_model.pth"
            torch.save(checkpoint, best_path)
            self.best_model_path = best_path
            self.logger.info(f"💾 New best fresh LipNet model saved: {val_accuracy:.1%} accuracy")
            print(f"💾 New best model saved: {val_accuracy:.1%} accuracy")

    def plot_training_curves(self):
        """Plot and save training curves"""

        if not self.training_history['train_acc']:
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

        epochs = range(1, len(self.training_history['train_acc']) + 1)

        # Accuracy plot
        ax1.plot(epochs, [acc * 100 for acc in self.training_history['train_acc']], 'b-', label='Training')
        ax1.plot(epochs, [acc * 100 for acc in self.training_history['val_acc']], 'r-', label='Validation')
        ax1.axhline(y=85, color='g', linestyle='--', label='Target (85%)')
        ax1.axhline(y=30, color='orange', linestyle='--', label='Baseline (30%)')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy (%)')
        ax1.set_title('Fresh LipNet Training Accuracy')
        ax1.legend()
        ax1.grid(True)

        # Loss plot
        ax2.plot(epochs, self.training_history['train_loss'], 'b-', label='Training')
        ax2.plot(epochs, self.training_history['val_loss'], 'r-', label='Validation')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.set_title('Fresh LipNet Training Loss')
        ax2.legend()
        ax2.grid(True)

        plt.tight_layout()

        # Save plot
        plot_path = Path("checkpoints/lipnet_perfect10_fresh/training_curves.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 Training curves saved: {plot_path}")

    def train(self):
        """Execute fresh LipNet training"""

        print(f"\n🚀 Starting Fresh LipNet Training")
        print("=" * 35)

        start_time = time.time()
        epochs_without_improvement = 0
        early_stopping_patience = 10

        for epoch in range(self.num_epochs):
            # Train epoch
            train_loss, train_acc = self.train_epoch(epoch)

            # Validate
            val_loss, val_acc, val_predictions, val_labels = self.validate()

            # Update scheduler
            self.scheduler.step(val_acc)

            # Track history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            self.training_history['learning_rates'].append(self.optimizer.param_groups[0]['lr'])

            # Check for best model
            is_best = val_acc > self.best_val_accuracy
            if is_best:
                self.best_val_accuracy = val_acc
                epochs_without_improvement = 0
            else:
                epochs_without_improvement += 1

            # Save checkpoint
            if (epoch + 1) % 5 == 0 or is_best:
                self.save_checkpoint(epoch, val_acc, is_best)

            # Log progress
            self.logger.info(f"Epoch {epoch+1:3d}: Train Loss={train_loss:.4f}, Train Acc={train_acc:.1%}, "
                           f"Val Loss={val_loss:.4f}, Val Acc={val_acc:.1%}, LR={self.optimizer.param_groups[0]['lr']:.6f}")

            print(f"Epoch {epoch+1:3d}: Train Acc={train_acc:.1%}, Val Acc={val_acc:.1%}, "
                  f"Best={self.best_val_accuracy:.1%}")

            # Target achieved check
            if val_acc >= self.target_accuracy:
                print(f"🎉 Target accuracy achieved: {val_acc:.1%} >= {self.target_accuracy:.1%}")
                self.save_checkpoint(epoch, val_acc, True)
                break

            # Early stopping
            if epochs_without_improvement >= early_stopping_patience:
                print(f"⏹️  Early stopping: No improvement for {early_stopping_patience} epochs")
                break

        training_time = time.time() - start_time

        print(f"\n🎉 Fresh LipNet Training Complete!")
        print(f"   Training time: {training_time/60:.1f} minutes")
        print(f"   Best validation accuracy: {self.best_val_accuracy:.1%}")
        print(f"   Target achieved: {'✅ YES' if self.best_val_accuracy >= self.target_accuracy else '❌ NO'}")
        print(f"   Improvement over baseline: {self.best_val_accuracy - 0.30:+.1%}")

        # Plot training curves
        self.plot_training_curves()

        # Save final training history
        history_file = Path("checkpoints/lipnet_perfect10_fresh/training_history.json")
        with open(history_file, 'w') as f:
            json.dump(self.training_history, f, indent=2)

        return self.best_val_accuracy

def main():
    """Main training function"""
    
    print("🚀 Fresh LipNet Perfect 10 Training")
    print("=" * 40)
    print("Training from scratch with validated dataset for >85% accuracy")
    
    # Initialize trainer
    validated_dataset_path = "perfect10_rescue_validated"
    trainer = FreshLipNetTrainer(validated_dataset_path)
    
    # Load validated dataset
    if not trainer.load_validated_dataset():
        print("❌ Failed to load validated dataset")
        return
    
    # Create fresh model
    trainer.create_fresh_model()
    
    # Setup training components
    trainer.setup_training_components()
    
    print(f"\n🎯 Training Configuration:")
    print(f"   Target accuracy: >{trainer.target_accuracy:.0%}")
    print(f"   Baseline to beat: 30% (corrupted data model)")
    print(f"   Expected trajectory: 20% → 40% → 70% → 85%+")
    
    print(f"\n🚀 Starting Fresh LipNet Training...")

    # Execute training
    best_accuracy = trainer.train()

    # Generate performance comparison report
    print(f"\n📊 PERFORMANCE COMPARISON REPORT")
    print("=" * 35)

    baseline_accuracy = 0.30  # Previous corrupted model
    improvement = best_accuracy - baseline_accuracy
    improvement_percent = (improvement / baseline_accuracy) * 100

    print(f"📈 Accuracy Improvement:")
    print(f"   Baseline (corrupted data): 30.0%")
    print(f"   Fresh LipNet (validated data): {best_accuracy:.1%}")
    print(f"   Absolute improvement: {improvement:+.1%}")
    print(f"   Relative improvement: {improvement_percent:+.1f}%")

    print(f"\n🎯 Target Achievement:")
    target_met = best_accuracy >= trainer.target_accuracy
    print(f"   Target accuracy: >{trainer.target_accuracy:.0%}")
    print(f"   Achieved: {'✅ YES' if target_met else '❌ NO'}")

    if target_met:
        print(f"   🏆 Model ready for production ICU deployment")
        print(f"   🎯 Expected confidence improvement on test videos")
    else:
        print(f"   ⚠️  Additional training or optimization needed")
        print(f"   📊 Current model still superior to 30% baseline")

    print(f"\n💾 Model Deliverables:")
    print(f"   Fresh model: checkpoints/lipnet_perfect10_fresh/best_model.pth")
    print(f"   Training curves: checkpoints/lipnet_perfect10_fresh/training_curves.png")
    print(f"   Training history: checkpoints/lipnet_perfect10_fresh/training_history.json")
    print(f"   Training logs: checkpoints/lipnet_perfect10_fresh/training_log.txt")

    print(f"\n🚀 Next Steps:")
    if target_met:
        print(f"   1. Re-test target video: python test_fresh_lipnet_on_video.py")
        print(f"   2. Deploy for ICU production use")
        print(f"   3. Monitor performance on real patient data")
    else:
        print(f"   1. Continue training with more epochs")
        print(f"   2. Tune hyperparameters (learning rate, batch size)")
        print(f"   3. Consider data augmentation strategies")

    print(f"\n🎉 Fresh LipNet Training Complete!")

    return best_accuracy

if __name__ == '__main__':
    main()
