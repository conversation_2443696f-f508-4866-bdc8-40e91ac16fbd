#!/usr/bin/env python3
"""
Test Fresh LipNet Perfect 10 model on target video
Compare performance against 30% baseline model
"""

import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import sys
import time
import json
from typing import Dict, Optional

# Add current directory to path
sys.path.append('.')

from lipnet_perfect_10 import LipNetPerfect10, LipNetPerfect10Manager
from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class FreshLipNetTester:
    """Test fresh LipNet model against baseline performance"""
    
    def __init__(self):
        """Initialize the fresh LipNet tester"""
        
        self.device = torch.device('cpu')
        
        # Perfect 10 phrases
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        
        # Model paths
        self.fresh_model_path = "checkpoints/lipnet_perfect10_fresh/best_model.pth"
        self.baseline_model_path = "checkpoints/lipnet_perfect_10_rescue/best_lipnet_perfect_10_rescue_model.pth"
        self.mobile3d_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
        
        # Models
        self.fresh_model = None
        self.baseline_model = None
        self.mobile3d_model = None
        
        # Enhanced video processor (identical to validation)
        self.video_processor = VideoProcessor(
            target_frames=64,
            target_size=(112, 112),
            grayscale=True,
            fps=25.0,
            mouth_crop=None,  # Set adaptively
            use_dataset_normalization=True
        )
        
        print(f"🧪 Fresh LipNet Tester Initialized")
        print(f"   Target video: test me.mov")
        print(f"   Models to compare: Fresh LipNet vs 30% Baseline vs Mobile3DTiny")
    
    def load_models(self) -> Dict:
        """Load all available models for comparison"""
        
        print(f"\n🤖 Loading Models for Performance Comparison")
        print("=" * 45)
        
        results = {
            'fresh_loaded': False,
            'baseline_loaded': False,
            'mobile3d_loaded': False,
            'fresh_accuracy': 0.0,
            'baseline_accuracy': 0.0,
            'mobile3d_accuracy': 0.0
        }
        
        # Load Fresh LipNet model
        if Path(self.fresh_model_path).exists():
            try:
                print(f"🚀 Loading Fresh LipNet Perfect 10...")
                checkpoint = torch.load(self.fresh_model_path, map_location=self.device)
                
                manager = LipNetPerfect10Manager()
                self.fresh_model = manager.create_model(
                    hidden_dim=256,
                    num_rnn_layers=2,
                    rnn_type='LSTM',
                    dropout=0.3
                )
                
                self.fresh_model.load_state_dict(checkpoint['model_state_dict'])
                self.fresh_model.to(self.device)
                self.fresh_model.eval()
                
                results['fresh_loaded'] = True
                results['fresh_accuracy'] = checkpoint.get('best_val_accuracy', 0.0)
                
                print(f"   ✅ Fresh LipNet loaded: {self.fresh_model.get_num_parameters():,} params, {results['fresh_accuracy']:.1%} accuracy")
                
            except Exception as e:
                print(f"   ❌ Fresh LipNet loading failed: {e}")
        else:
            print(f"🚀 Fresh LipNet model not found: {self.fresh_model_path}")
        
        # Load Baseline model (30% accuracy)
        if Path(self.baseline_model_path).exists():
            try:
                print(f"📊 Loading Baseline LipNet (30% accuracy)...")
                checkpoint = torch.load(self.baseline_model_path, map_location=self.device)
                
                manager = LipNetPerfect10Manager()
                self.baseline_model = manager.create_model(
                    hidden_dim=256,
                    num_rnn_layers=2,
                    rnn_type='LSTM',
                    dropout=0.3
                )
                
                self.baseline_model.load_state_dict(checkpoint['model_state_dict'])
                self.baseline_model.to(self.device)
                self.baseline_model.eval()
                
                results['baseline_loaded'] = True
                results['baseline_accuracy'] = checkpoint.get('best_val_accuracy', 0.0)
                
                print(f"   ✅ Baseline LipNet loaded: {self.baseline_model.get_num_parameters():,} params, {results['baseline_accuracy']:.1%} accuracy")
                
            except Exception as e:
                print(f"   ❌ Baseline LipNet loading failed: {e}")
        else:
            print(f"📊 Baseline LipNet model not found: {self.baseline_model_path}")
        
        # Load Mobile3DTiny for reference
        if Path(self.mobile3d_path).exists():
            try:
                print(f"📱 Loading Mobile3DTiny (reference)...")
                checkpoint = torch.load(self.mobile3d_path, map_location=self.device)
                
                self.mobile3d_model = Perfect10Mobile3DTiny(num_classes=10)
                self.mobile3d_model.load_state_dict(checkpoint['model_state_dict'])
                self.mobile3d_model.to(self.device)
                self.mobile3d_model.eval()
                
                results['mobile3d_loaded'] = True
                results['mobile3d_accuracy'] = checkpoint.get('best_val_accuracy', 0.0)
                
                print(f"   ✅ Mobile3DTiny loaded: {self.mobile3d_model.get_num_parameters():,} params, {results['mobile3d_accuracy']:.1%} accuracy")
                
            except Exception as e:
                print(f"   ❌ Mobile3DTiny loading failed: {e}")
        else:
            print(f"📱 Mobile3DTiny model not found: {self.mobile3d_path}")
        
        return results
    
    def test_model_on_video(self, video_path: str, model, model_name: str) -> Dict:
        """Test a specific model on the target video"""
        
        if not Path(video_path).exists():
            return {
                'success': False,
                'error': f'Video not found: {video_path}',
                'model_name': model_name
            }
        
        try:
            # Detect if pre-cropped
            is_pre_cropped = "_mouth_cropped" in Path(video_path).name or "processed_" in Path(video_path).name
            if not is_pre_cropped:
                self.video_processor.mouth_crop = (133, 0, 133, 100)
            else:
                self.video_processor.mouth_crop = None
            
            # Process video
            start_time = time.time()
            video_tensor = self.video_processor.process_video(video_path)
            processing_time = (time.time() - start_time) * 1000
            
            # Model inference
            video_batch = video_tensor.unsqueeze(0).to(self.device)
            with torch.no_grad():
                logits = model(video_batch)
                probabilities = F.softmax(logits[0], dim=0)
            
            inference_time = (time.time() - start_time) * 1000
            
            # Get predictions
            top3_probs, top3_indices = torch.topk(probabilities, 3)
            top3_probs = top3_probs.cpu().numpy()
            top3_indices = top3_indices.cpu().numpy()
            top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
            
            return {
                'success': True,
                'model_name': model_name,
                'video_path': video_path,
                'processing_time_ms': processing_time,
                'inference_time_ms': inference_time,
                'tensor_shape': list(video_tensor.shape),
                'value_range': [float(video_tensor.min()), float(video_tensor.max())],
                'top_prediction': top3_phrases[0],
                'top_confidence': float(top3_probs[0]),
                'top3_phrases': top3_phrases,
                'top3_probabilities': top3_probs.tolist(),
                'all_probabilities': probabilities.cpu().numpy().tolist()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'model_name': model_name,
                'video_path': video_path
            }
    
    def compare_models_on_video(self, video_path: str) -> Dict:
        """Compare all models on the target video"""
        
        print(f"\n🎬 Model Performance Comparison")
        print("=" * 35)
        print(f"Video: {Path(video_path).name}")
        
        comparison_results = {
            'video_path': video_path,
            'video_name': Path(video_path).name,
            'fresh_result': None,
            'baseline_result': None,
            'mobile3d_result': None,
            'performance_analysis': {}
        }
        
        # Test Fresh LipNet
        if self.fresh_model:
            print(f"\n🚀 Testing Fresh LipNet...")
            comparison_results['fresh_result'] = self.test_model_on_video(video_path, self.fresh_model, "Fresh LipNet")
            
            if comparison_results['fresh_result']['success']:
                fresh_res = comparison_results['fresh_result']
                print(f"   ✅ Prediction: \"{fresh_res['top_prediction'].title()}\" ({fresh_res['top_confidence']:.1%})")
                print(f"   ⏱️  Inference: {fresh_res['inference_time_ms']:.0f}ms")
            else:
                print(f"   ❌ Failed: {comparison_results['fresh_result']['error']}")
        
        # Test Baseline LipNet
        if self.baseline_model:
            print(f"\n📊 Testing Baseline LipNet (30%)...")
            comparison_results['baseline_result'] = self.test_model_on_video(video_path, self.baseline_model, "Baseline LipNet")
            
            if comparison_results['baseline_result']['success']:
                baseline_res = comparison_results['baseline_result']
                print(f"   ✅ Prediction: \"{baseline_res['top_prediction'].title()}\" ({baseline_res['top_confidence']:.1%})")
                print(f"   ⏱️  Inference: {baseline_res['inference_time_ms']:.0f}ms")
            else:
                print(f"   ❌ Failed: {comparison_results['baseline_result']['error']}")
        
        # Test Mobile3DTiny
        if self.mobile3d_model:
            print(f"\n📱 Testing Mobile3DTiny (reference)...")
            comparison_results['mobile3d_result'] = self.test_model_on_video(video_path, self.mobile3d_model, "Mobile3DTiny")
            
            if comparison_results['mobile3d_result']['success']:
                mobile_res = comparison_results['mobile3d_result']
                print(f"   ✅ Prediction: \"{mobile_res['top_prediction'].title()}\" ({mobile_res['top_confidence']:.1%})")
                print(f"   ⏱️  Inference: {mobile_res['inference_time_ms']:.0f}ms")
            else:
                print(f"   ❌ Failed: {comparison_results['mobile3d_result']['error']}")
        
        # Generate performance analysis
        self.analyze_performance_comparison(comparison_results)
        
        return comparison_results
    
    def analyze_performance_comparison(self, comparison_results: Dict):
        """Analyze and display performance comparison"""
        
        print(f"\n📊 PERFORMANCE ANALYSIS")
        print("=" * 25)
        
        fresh_res = comparison_results.get('fresh_result')
        baseline_res = comparison_results.get('baseline_result')
        mobile_res = comparison_results.get('mobile3d_result')
        
        # Confidence comparison
        if fresh_res and fresh_res['success'] and baseline_res and baseline_res['success']:
            fresh_conf = fresh_res['top_confidence']
            baseline_conf = baseline_res['top_confidence']
            conf_improvement = fresh_conf - baseline_conf
            
            print(f"🎯 Confidence Comparison:")
            print(f"   Fresh LipNet: {fresh_conf:.1%}")
            print(f"   Baseline (30%): {baseline_conf:.1%}")
            print(f"   Improvement: {conf_improvement:+.1%}")
            
            if conf_improvement > 0:
                print(f"   ✅ Fresh model shows higher confidence")
            else:
                print(f"   ⚠️  Fresh model confidence lower than baseline")
        
        # Prediction comparison
        predictions = []
        if fresh_res and fresh_res['success']:
            predictions.append(f"Fresh: {fresh_res['top_prediction'].title()}")
        if baseline_res and baseline_res['success']:
            predictions.append(f"Baseline: {baseline_res['top_prediction'].title()}")
        if mobile_res and mobile_res['success']:
            predictions.append(f"Mobile3D: {mobile_res['top_prediction'].title()}")
        
        print(f"\n🎯 Prediction Comparison:")
        for pred in predictions:
            print(f"   {pred}")
        
        # Check consensus
        if len(predictions) >= 2:
            unique_predictions = set([p.split(': ')[1] for p in predictions])
            if len(unique_predictions) == 1:
                print(f"   ✅ All models agree on prediction")
            else:
                print(f"   ⚠️  Models disagree on prediction")
    
    def display_comprehensive_results(self, model_load_results: Dict, comparison_results: Dict):
        """Display comprehensive test results"""
        
        print(f"\n" + "="*60)
        print(f"🧪 FRESH LIPNET PERFORMANCE TEST RESULTS")
        print(f"="*60)
        
        # Model loading summary
        print(f"🤖 Model Loading Summary:")
        print(f"   Fresh LipNet: {'✅ Loaded' if model_load_results['fresh_loaded'] else '❌ Not available'}")
        if model_load_results['fresh_loaded']:
            print(f"     Training accuracy: {model_load_results['fresh_accuracy']:.1%}")
        
        print(f"   Baseline LipNet: {'✅ Loaded' if model_load_results['baseline_loaded'] else '❌ Not available'}")
        if model_load_results['baseline_loaded']:
            print(f"     Training accuracy: {model_load_results['baseline_accuracy']:.1%}")
        
        print(f"   Mobile3DTiny: {'✅ Loaded' if model_load_results['mobile3d_loaded'] else '❌ Not available'}")
        if model_load_results['mobile3d_loaded']:
            print(f"     Training accuracy: {model_load_results['mobile3d_accuracy']:.1%}")
        
        # Performance improvement analysis
        if (model_load_results['fresh_loaded'] and model_load_results['baseline_loaded']):
            fresh_acc = model_load_results['fresh_accuracy']
            baseline_acc = model_load_results['baseline_accuracy']
            improvement = fresh_acc - baseline_acc
            
            print(f"\n📈 Training Accuracy Improvement:")
            print(f"   Fresh LipNet: {fresh_acc:.1%}")
            print(f"   Baseline: {baseline_acc:.1%}")
            print(f"   Improvement: {improvement:+.1%}")
            
            if improvement > 0:
                print(f"   ✅ Fresh training successful")
            else:
                print(f"   ⚠️  Fresh training needs more epochs")
        
        # Video test results
        fresh_res = comparison_results.get('fresh_result')
        if fresh_res and fresh_res['success']:
            print(f"\n🎬 Fresh LipNet Video Test:")
            print(f"   Video: {fresh_res['video_name']}")
            print(f"   Prediction: \"{fresh_res['top_prediction'].title()}\"")
            print(f"   Confidence: {fresh_res['top_confidence']:.1%}")
            print(f"   Processing time: {fresh_res['inference_time_ms']:.0f}ms")
        
        # Deployment readiness
        if model_load_results['fresh_loaded']:
            fresh_acc = model_load_results['fresh_accuracy']
            deployment_ready = fresh_acc >= 0.85  # >85% target
            
            print(f"\n🚀 Deployment Readiness:")
            print(f"   Target accuracy: >85%")
            print(f"   Achieved accuracy: {fresh_acc:.1%}")
            print(f"   Deployment ready: {'✅ YES' if deployment_ready else '❌ NO'}")
            
            if deployment_ready:
                print(f"   🏆 Model ready for production ICU deployment")
            else:
                print(f"   📊 Continue training for higher accuracy")

def main():
    """Main testing function"""
    
    print("🧪 Fresh LipNet Perfect 10 Performance Test")
    print("=" * 45)
    
    # Initialize tester
    tester = FreshLipNetTester()
    
    # Load models
    model_load_results = tester.load_models()
    
    # Test on target video
    video_path = "/Users/<USER>/Desktop/test me.mov"
    comparison_results = tester.compare_models_on_video(video_path)
    
    # Display comprehensive results
    tester.display_comprehensive_results(model_load_results, comparison_results)
    
    # Save results
    output_data = {
        'model_load_results': model_load_results,
        'comparison_results': comparison_results,
        'test_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    output_file = "fresh_lipnet_performance_test.json"
    with open(output_file, 'w') as f:
        json.dump(output_data, f, indent=2, default=str)
    
    print(f"\n💾 Test results saved: {output_file}")
    print(f"\n🎉 Fresh LipNet Performance Test Complete!")

if __name__ == '__main__':
    main()
