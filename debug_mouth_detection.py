#!/usr/bin/env python3
"""
Debug tool for mouth region detection in video preprocessing pipeline.
Analyzes the detect_mouth_region() function and visualizes detection results.

This tool helps identify why the current algorithm is cropping chin/neck area
instead of mouth/lips in pre-cropped lower-face videos.
"""

import os
import sys
import cv2
import numpy as np
from pathlib import Path
import argparse
from typing import Tuple, List, Optional

# Add the current directory to path to import enhanced_video_preprocessor
sys.path.append('/Users/<USER>/Desktop/app dev 23.5.25')

try:
    from enhanced_video_preprocessor import EnhancedVideoPreprocessor
except ImportError as e:
    print(f"❌ Error importing enhanced_video_preprocessor: {e}")
    print("Make sure you're running this from the correct directory")
    sys.exit(1)

class MouthDetectionDebugger:
    def __init__(self, video_path: str, output_dir: str = "debug_output"):
        self.video_path = Path(video_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize the enhanced preprocessor for comparison
        self.preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
        
        print(f"🔍 Debugging mouth detection for: {self.video_path.name}")
        print(f"📁 Debug output directory: {self.output_dir}")

    def analyze_video_format(self) -> dict:
        """Analyze the input video format and characteristics"""
        cap = cv2.VideoCapture(str(self.video_path))
        
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {self.video_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Read a few sample frames
        sample_frames = []
        frame_indices = [0, frame_count//4, frame_count//2, 3*frame_count//4, frame_count-1]
        
        for idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
            ret, frame = cap.read()
            if ret:
                sample_frames.append((idx, frame))
        
        cap.release()
        
        analysis = {
            "fps": fps,
            "frame_count": frame_count,
            "width": width,
            "height": height,
            "sample_frames": sample_frames,
            "aspect_ratio": width / height,
            "is_pre_cropped": height < width * 0.8  # Heuristic for lower-face crop
        }
        
        print(f"📊 Video Analysis:")
        print(f"   Resolution: {width}x{height} (aspect ratio: {analysis['aspect_ratio']:.2f})")
        print(f"   FPS: {fps}, Frames: {frame_count}")
        print(f"   Pre-cropped lower-face: {analysis['is_pre_cropped']}")
        
        return analysis

    def debug_face_detection(self, frame: np.ndarray, frame_idx: int) -> dict:
        """Debug face detection on a single frame"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        h, w = gray.shape
        
        # Use the same face detection as enhanced_video_preprocessor
        faces = self.preprocessor.face_cascade.detectMultiScale(
            gray, 
            scaleFactor=1.1, 
            minNeighbors=5, 
            flags=cv2.CASCADE_SCALE_IMAGE, 
            minSize=(60, 60)
        )
        
        debug_info = {
            "frame_size": (w, h),
            "faces_detected": len(faces),
            "faces": faces.tolist() if len(faces) > 0 else [],
            "largest_face": None,
            "mouth_region_current": None,
            "mouth_region_corrected": None
        }
        
        # Create debug visualization
        debug_frame = frame.copy()
        
        if len(faces) > 0:
            # Find largest face (same logic as preprocessor)
            largest_face = max(faces, key=lambda f: f[2] * f[3])
            x, y, fw, fh = largest_face
            debug_info["largest_face"] = (x, y, fw, fh)
            
            # Draw detected face rectangle
            cv2.rectangle(debug_frame, (x, y), (x + fw, y + fh), (0, 255, 0), 2)
            cv2.putText(debug_frame, f"Face: {fw}x{fh}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # Use the actual enhanced_video_preprocessor logic
            mx1_current, my1_current, mx2_current, my2_current = self.preprocessor.detect_mouth_region(gray)
            debug_info["mouth_region_current"] = (mx1_current, my1_current, mx2_current, my2_current)

            # Draw current mouth region from enhanced_video_preprocessor in GREEN (should be fixed now)
            cv2.rectangle(debug_frame, (mx1_current, my1_current), (mx2_current, my2_current), (0, 255, 0), 2)
            cv2.putText(debug_frame, "ENHANCED PREPROCESSOR", (mx1_current, my1_current-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            # For comparison, show the old (incorrect) algorithm
            mx1_old = x + int(0.15 * fw)
            mx2_old = x + int(0.85 * fw)
            my1_old = y + int(0.55 * fh)  # Old incorrect logic
            my2_old = y + int(0.98 * fh)

            # Ensure bounds
            mx1_old = max(0, mx1_old)
            my1_old = max(0, my1_old)
            mx2_old = min(w - 1, mx2_old)
            my2_old = min(h - 1, my2_old)

            debug_info["mouth_region_corrected"] = (mx1_old, my1_old, mx2_old, my2_old)

            # Draw old (incorrect) mouth region in RED for comparison
            cv2.rectangle(debug_frame, (mx1_old, my1_old), (mx2_old, my2_old), (0, 0, 255), 2)
            cv2.putText(debug_frame, "OLD (WRONG)", (mx1_old, my1_old-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
            
        else:
            # No face detected - use enhanced_video_preprocessor fallback
            mx1_current, my1_current, mx2_current, my2_current = self.preprocessor.detect_mouth_region(gray)
            debug_info["mouth_region_current"] = (mx1_current, my1_current, mx2_current, my2_current)

            # Draw enhanced preprocessor fallback in GREEN
            cv2.rectangle(debug_frame, (mx1_current, my1_current), (mx2_current, my2_current), (0, 255, 0), 2)
            cv2.putText(debug_frame, "ENHANCED FALLBACK", (mx1_current, my1_current-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            # For comparison, show old fallback
            bw = int(0.5 * w)
            bh = int(0.5 * h)
            x1_old = (w - bw) // 2
            y1_old = int(h * 0.45)  # Old logic
            x2_old = x1_old + bw
            y2_old = min(h - 1, y1_old + bh)

            debug_info["mouth_region_corrected"] = (x1_old, y1_old, x2_old, y2_old)

            # Draw old fallback in RED
            cv2.rectangle(debug_frame, (x1_old, y1_old), (x2_old, y2_old), (0, 0, 255), 2)
            cv2.putText(debug_frame, "OLD FALLBACK", (x1_old, y1_old-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
        
        # Add frame info
        cv2.putText(debug_frame, f"Frame {frame_idx}: {w}x{h}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(debug_frame, f"Faces: {len(faces)}", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Save debug frame
        debug_filename = self.output_dir / f"debug_frame_{frame_idx:04d}.jpg"
        cv2.imwrite(str(debug_filename), debug_frame)
        
        return debug_info

    def create_crop_comparison(self, frame: np.ndarray, debug_info: dict, frame_idx: int):
        """Create side-by-side comparison of current vs corrected crops"""
        if debug_info["mouth_region_current"] is None:
            return
        
        # Extract current crop
        mx1_c, my1_c, mx2_c, my2_c = debug_info["mouth_region_current"]
        current_crop = frame[my1_c:my2_c, mx1_c:mx2_c]
        
        # Extract corrected crop
        mx1_cor, my1_cor, mx2_cor, my2_cor = debug_info["mouth_region_corrected"]
        corrected_crop = frame[my1_cor:my2_cor, mx1_cor:mx2_cor]
        
        # Resize both to same size for comparison
        target_size = (140, 46)  # LipNet target size
        current_resized = cv2.resize(current_crop, target_size) if current_crop.size > 0 else np.zeros((46, 140, 3), dtype=np.uint8)
        corrected_resized = cv2.resize(corrected_crop, target_size) if corrected_crop.size > 0 else np.zeros((46, 140, 3), dtype=np.uint8)
        
        # Create comparison image
        comparison = np.hstack([current_resized, corrected_resized])
        
        # Add labels
        cv2.putText(comparison, "CURRENT (WRONG)", (5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
        cv2.putText(comparison, "CORRECTED", (145, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        
        # Save comparison
        comparison_filename = self.output_dir / f"crop_comparison_{frame_idx:04d}.jpg"
        cv2.imwrite(str(comparison_filename), comparison)
        
        print(f"   💾 Saved crop comparison: {comparison_filename.name}")

    def run_debug_analysis(self, max_frames: int = 5) -> dict:
        """Run complete debug analysis on the video"""
        print(f"\n🔍 Starting debug analysis...")
        
        # Analyze video format
        video_analysis = self.analyze_video_format()
        
        # Debug face detection on sample frames
        debug_results = []
        
        for i, (frame_idx, frame) in enumerate(video_analysis["sample_frames"][:max_frames]):
            print(f"\n📋 Analyzing frame {frame_idx}...")
            
            debug_info = self.debug_face_detection(frame, frame_idx)
            debug_results.append(debug_info)
            
            # Create crop comparison
            self.create_crop_comparison(frame, debug_info, frame_idx)
            
            print(f"   Faces detected: {debug_info['faces_detected']}")
            if debug_info["largest_face"]:
                x, y, fw, fh = debug_info["largest_face"]
                print(f"   Largest face: {fw}x{fh} at ({x}, {y})")
            
            if debug_info["mouth_region_current"]:
                mx1, my1, mx2, my2 = debug_info["mouth_region_current"]
                print(f"   Current mouth region: ({mx1}, {my1}) to ({mx2}, {my2})")
            
            if debug_info["mouth_region_corrected"]:
                mx1, my1, mx2, my2 = debug_info["mouth_region_corrected"]
                print(f"   Corrected mouth region: ({mx1}, {my1}) to ({mx2}, {my2})")
        
        # Generate summary report
        summary = {
            "video_analysis": video_analysis,
            "debug_results": debug_results,
            "recommendations": self.generate_recommendations(video_analysis, debug_results)
        }
        
        self.save_summary_report(summary)
        
        return summary

    def generate_recommendations(self, video_analysis: dict, debug_results: List[dict]) -> List[str]:
        """Generate recommendations based on debug analysis"""
        recommendations = []
        
        if video_analysis["is_pre_cropped"]:
            recommendations.append("✅ CONFIRMED: Video appears to be pre-cropped to lower-face region")
            recommendations.append("🔧 FIX REQUIRED: Mouth region calculation needs adjustment for pre-cropped videos")
            recommendations.append("📐 SOLUTION: Change mouth region from 55%-98% to 10%-45% of detected face height")
        
        faces_detected = sum(1 for result in debug_results if result["faces_detected"] > 0)
        if faces_detected < len(debug_results) * 0.5:
            recommendations.append("⚠️ WARNING: Low face detection rate - fallback algorithm also needs adjustment")
            recommendations.append("📐 FALLBACK FIX: Change fallback from 45%-95% to 15%-50% of frame height")
        
        recommendations.append("🧪 TESTING: Use debug crops to verify mouth/lips are properly captured")
        recommendations.append("🔄 REPROCESS: All 107 videos need reprocessing with corrected algorithm")
        
        return recommendations

    def save_summary_report(self, summary: dict):
        """Save a comprehensive summary report"""
        report_file = self.output_dir / "debug_summary_report.txt"
        
        with open(report_file, 'w') as f:
            f.write("🔍 MOUTH DETECTION DEBUG ANALYSIS REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("📊 VIDEO ANALYSIS:\n")
            va = summary["video_analysis"]
            f.write(f"   Resolution: {va['width']}x{va['height']}\n")
            f.write(f"   Aspect Ratio: {va['aspect_ratio']:.2f}\n")
            f.write(f"   FPS: {va['fps']}\n")
            f.write(f"   Frame Count: {va['frame_count']}\n")
            f.write(f"   Pre-cropped: {va['is_pre_cropped']}\n\n")
            
            f.write("🎯 DETECTION RESULTS:\n")
            for i, result in enumerate(summary["debug_results"]):
                f.write(f"   Frame {i}: {result['faces_detected']} faces detected\n")
                if result["largest_face"]:
                    x, y, fw, fh = result["largest_face"]
                    f.write(f"      Face: {fw}x{fh} at ({x}, {y})\n")
            f.write("\n")
            
            f.write("💡 RECOMMENDATIONS:\n")
            for rec in summary["recommendations"]:
                f.write(f"   {rec}\n")
            f.write("\n")
            
            f.write("📁 DEBUG FILES GENERATED:\n")
            f.write(f"   - debug_frame_*.jpg: Annotated frames showing detection rectangles\n")
            f.write(f"   - crop_comparison_*.jpg: Side-by-side current vs corrected crops\n")
            f.write(f"   - debug_summary_report.txt: This report\n")
        
        print(f"\n📄 Summary report saved: {report_file}")


def main():
    parser = argparse.ArgumentParser(description="Debug mouth region detection in video preprocessing")
    parser.add_argument("video_path", help="Path to video file to debug")
    parser.add_argument("--output_dir", default="debug_output", help="Output directory for debug files")
    parser.add_argument("--max_frames", type=int, default=5, help="Maximum frames to analyze")
    
    args = parser.parse_args()
    
    if not Path(args.video_path).exists():
        print(f"❌ Video file not found: {args.video_path}")
        sys.exit(1)
    
    debugger = MouthDetectionDebugger(args.video_path, args.output_dir)
    summary = debugger.run_debug_analysis(args.max_frames)
    
    print(f"\n✅ Debug analysis complete!")
    print(f"📁 Check {args.output_dir}/ for debug images and report")
    print(f"\n💡 Key findings:")
    for rec in summary["recommendations"][:3]:
        print(f"   {rec}")


if __name__ == "__main__":
    main()
