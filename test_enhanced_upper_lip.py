#!/usr/bin/env python3
"""
Test the enhanced upper lip visibility in mouth detection algorithm.
"""

import cv2
import numpy as np
import sys
from pathlib import Path

# Add the current directory to path
sys.path.append('/Users/<USER>/Desktop/app dev 23.5.25')

try:
    from enhanced_video_preprocessor import EnhancedVideoPreprocessor
except ImportError as e:
    print(f"❌ Error importing enhanced_video_preprocessor: {e}")
    sys.exit(1)

def test_enhanced_coordinates(video_path):
    """Test the enhanced coordinates for better upper lip visibility"""
    print(f"🔧 TESTING ENHANCED UPPER LIP VISIBILITY")
    print(f"Video: {Path(video_path).name}")
    
    # Extract a frame for analysis
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ Cannot open video: {video_path}")
        return
    
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    cap.set(cv2.CAP_PROP_POS_FRAMES, 25)  # Middle frame
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        print(f"❌ Cannot read frame from video")
        return
    
    print(f"📹 Video size: {width}x{height}")
    
    # Test mouth detection with enhanced algorithm
    preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    x1, y1, x2, y2 = preprocessor.detect_mouth_region(gray)
    
    print(f"🎯 Enhanced coordinates:")
    print(f"   Region: ({x1}, {y1}) to ({x2}, {y2})")
    print(f"   Size: {x2-x1}x{y2-y1}")
    print(f"   Y percentage: {y1/height*100:.1f}% to {y2/height*100:.1f}%")
    
    # Compare with previous coordinates
    prev_x1, prev_y1, prev_x2, prev_y2 = 146, 8, 253, 68
    y1_shift = y1 - prev_y1
    y2_shift = y2 - prev_y2
    height_change = (y2 - y1) - (prev_y2 - prev_y1)
    
    print(f"📊 Comparison with previous:")
    print(f"   Previous: ({prev_x1}, {prev_y1}) to ({prev_x2}, {prev_y2})")
    print(f"   Y1 shift: {y1_shift} pixels ({'upward' if y1_shift < 0 else 'downward'})")
    print(f"   Y2 shift: {y2_shift} pixels ({'upward' if y2_shift < 0 else 'downward'})")
    print(f"   Height change: {height_change} pixels ({'expanded' if height_change > 0 else 'reduced'})")
    
    # Create comparison visualization
    comparison = frame.copy()
    
    # Draw previous coordinates in BLUE
    cv2.rectangle(comparison, (prev_x1, prev_y1), (prev_x2, prev_y2), (255, 0, 0), 2)
    cv2.putText(comparison, "PREVIOUS", (prev_x1, prev_y1-10), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
    
    # Draw enhanced coordinates in GREEN
    cv2.rectangle(comparison, (x1, y1), (x2, y2), (0, 255, 0), 3)
    cv2.putText(comparison, "ENHANCED (more upper lip)", (x1, y1-10), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # Add grid overlay for reference
    col_width = width // 3
    row_height = height // 2
    cv2.line(comparison, (col_width, 0), (col_width, height), (255, 255, 255), 1)
    cv2.line(comparison, (2*col_width, 0), (2*col_width, height), (255, 255, 255), 1)
    cv2.line(comparison, (0, row_height), (width, row_height), (255, 255, 255), 1)
    
    # Add enhancement info
    cv2.putText(comparison, f"Y1: {prev_y1} -> {y1} ({y1_shift:+d}px)", (10, height-60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(comparison, f"Y2: {prev_y2} -> {y2} ({y2_shift:+d}px)", (10, height-30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    cv2.imwrite("enhanced_upper_lip_comparison.jpg", comparison)
    print(f"💾 Saved enhanced_upper_lip_comparison.jpg")
    
    # Extract and compare crops
    prev_crop = frame[prev_y1:prev_y2, prev_x1:prev_x2]
    enhanced_crop = frame[y1:y2, x1:x2]
    
    if prev_crop.size > 0 and enhanced_crop.size > 0:
        # Scale up for visibility
        prev_scaled = cv2.resize(prev_crop, (320, 180), interpolation=cv2.INTER_NEAREST)
        enhanced_scaled = cv2.resize(enhanced_crop, (320, 200), interpolation=cv2.INTER_NEAREST)
        
        # Add labels
        cv2.putText(prev_scaled, "PREVIOUS", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        cv2.putText(prev_scaled, f"{prev_x2-prev_x1}x{prev_y2-prev_y1}", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        cv2.putText(enhanced_scaled, "ENHANCED (more upper lip)", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(enhanced_scaled, f"{x2-x1}x{y2-y1}", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Create side-by-side comparison (pad to same height)
        max_height = max(prev_scaled.shape[0], enhanced_scaled.shape[0])
        prev_padded = np.zeros((max_height, prev_scaled.shape[1], 3), dtype=np.uint8)
        enhanced_padded = np.zeros((max_height, enhanced_scaled.shape[1], 3), dtype=np.uint8)
        
        prev_padded[:prev_scaled.shape[0]] = prev_scaled
        enhanced_padded[:enhanced_scaled.shape[0]] = enhanced_scaled
        
        crop_comparison = np.hstack([prev_padded, enhanced_padded])
        cv2.imwrite("enhanced_crop_comparison.jpg", crop_comparison)
        print(f"💾 Saved enhanced_crop_comparison.jpg")
    
    return x1, y1, x2, y2

def test_processing_pipeline(video_path):
    """Test the complete processing pipeline with enhanced algorithm"""
    print(f"\n🔧 TESTING COMPLETE PROCESSING PIPELINE")
    
    # Remove existing processed video
    processed_path = Path("data/where_am_i/where_am_i__useruser01__18to39__male__not_specified__20250809T053449_processed.mp4")
    if processed_path.exists():
        processed_path.unlink()
        print(f"🗑️ Removed existing processed video")
    
    # Process the video
    preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
    
    # Find phrase index for "Where am I?"
    phrase_idx = 0  # First phrase
    
    try:
        result = preprocessor.process_single_video(Path(video_path), phrase_idx)
        
        if result and processed_path.exists():
            print(f"✅ Processing successful!")
            
            # Analyze processed video
            cap = cv2.VideoCapture(str(processed_path))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            print(f"📹 Processed video: {width}x{height}, {fps} FPS, {frames} frames")
            
            # Extract sample frames to check enhanced upper lip visibility
            sample_frames = []
            for frame_idx in [0, 15, 30, 45, 60, 74]:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if ret:
                    sample_frames.append((frame_idx, frame))
            
            cap.release()
            
            # Create validation grid
            if len(sample_frames) >= 6:
                scaled_frames = []
                for frame_idx, frame in sample_frames:
                    # Scale up for visibility (140x46 -> 560x184)
                    scaled = cv2.resize(frame, (560, 184), interpolation=cv2.INTER_NEAREST)
                    cv2.putText(scaled, f"Frame {frame_idx}", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                    cv2.putText(scaled, "ENHANCED: Better Upper Lip Visibility", (10, 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    scaled_frames.append(scaled)
                
                # Create 2x3 grid
                top_row = np.hstack([scaled_frames[0], scaled_frames[1], scaled_frames[2]])
                bottom_row = np.hstack([scaled_frames[3], scaled_frames[4], scaled_frames[5]])
                grid = np.vstack([top_row, bottom_row])
                
                # Add title
                title_height = 50
                title_img = np.zeros((title_height, grid.shape[1], 3), dtype=np.uint8)
                cv2.putText(title_img, "ENHANCED ALGORITHM - IMPROVED UPPER LIP VISIBILITY", 
                           (20, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
                
                final_grid = np.vstack([title_img, grid])
                cv2.imwrite("enhanced_upper_lip_validation.jpg", final_grid)
                print(f"💾 Saved enhanced_upper_lip_validation.jpg")
            
            return True
        else:
            print(f"❌ Processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Processing error: {e}")
        return False

def main():
    video_path = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    
    if not Path(video_path).exists():
        print(f"❌ Video file not found: {video_path}")
        return
    
    print("🔧 ENHANCED UPPER LIP VISIBILITY TEST")
    print("=" * 50)
    
    # Test enhanced coordinates
    coords = test_enhanced_coordinates(video_path)
    
    # Test complete processing pipeline
    success = test_processing_pipeline(video_path)
    
    print(f"\n📋 ENHANCEMENT TEST RESULTS:")
    print(f"Coordinates test: {'✅ PASSED' if coords else '❌ FAILED'}")
    print(f"Processing test: {'✅ PASSED' if success else '❌ FAILED'}")
    
    if coords and success:
        print(f"\n🎉 ENHANCEMENT SUCCESSFUL!")
        print(f"✅ Algorithm now shows more upper lip area")
        print(f"✅ Enhanced framing while maintaining lip focus")
        print(f"✅ Ready for full dataset reprocessing")
        print(f"\n📁 Generated files:")
        print(f"- enhanced_upper_lip_comparison.jpg: Shows coordinate enhancement")
        print(f"- enhanced_crop_comparison.jpg: Shows before/after lip capture")
        print(f"- enhanced_upper_lip_validation.jpg: Shows processed output")
    else:
        print(f"\n❌ ENHANCEMENT NEEDS ADJUSTMENT")
        print(f"❌ Further coordinate refinement required")

if __name__ == "__main__":
    main()
