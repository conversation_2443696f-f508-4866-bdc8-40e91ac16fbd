#!/usr/bin/env python3
"""
Comprehensive validation script for lightweight VSR system
Tests current implementation and validates against reference videos
"""

import sys
import os
sys.path.append('.')

import pandas as pd
import yaml
import torch
import numpy as np
from pathlib import Path
import json
from collections import defaultdict
import time

from backend.lightweight_vsr.utils_video import VideoProcessor
from backend.lightweight_vsr.model import Mobile3DTiny


class VSRValidator:
    """Comprehensive VSR system validator"""
    
    def __init__(self, config_path='configs/phrases26.yaml'):
        """Initialize validator with configuration"""
        
        # Load config
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.phrases = self.config['phrases']
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.phrases)}
        self.idx_to_phrase = {idx: phrase for phrase, idx in self.phrase_to_idx.items()}
        
        # Create video processor
        self.processor = VideoProcessor(
            target_frames=self.config.get('frames', 32),
            target_size=(self.config.get('height', 96), self.config.get('width', 96)),
            grayscale=self.config.get('grayscale', True)
        )
        
        # Initialize model (will be loaded later if checkpoint exists)
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        print(f"VSR Validator initialized")
        print(f"Device: {self.device}")
        print(f"Phrases: {len(self.phrases)}")
        print(f"Expected phrases: {self.phrases}")
    
    def test_video_processing(self, manifest_path='data/manifest.csv'):
        """Test video processing on all training videos"""
        
        print("\n" + "="*60)
        print("TESTING VIDEO PROCESSING")
        print("="*60)
        
        if not Path(manifest_path).exists():
            print(f"❌ Manifest not found: {manifest_path}")
            return False
        
        df = pd.read_csv(manifest_path)
        print(f"Testing {len(df)} videos from manifest...")
        
        results = {
            'successful': 0,
            'failed': 0,
            'shape_issues': [],
            'processing_errors': []
        }
        
        expected_shape = (1, self.config.get('frames', 32), 
                         self.config.get('height', 96), 
                         self.config.get('width', 96))
        
        for idx, row in df.iterrows():
            video_path = row['video_path']
            phrase = row['phrase']
            
            try:
                video_tensor = self.processor.process_video(video_path)
                
                if video_tensor.shape != expected_shape:
                    results['shape_issues'].append({
                        'path': video_path,
                        'phrase': phrase,
                        'shape': video_tensor.shape,
                        'expected': expected_shape
                    })
                    print(f"⚠️  {Path(video_path).name}: {video_tensor.shape} != {expected_shape}")
                else:
                    results['successful'] += 1
                    if results['successful'] <= 5:
                        print(f"✅ {Path(video_path).name}: {video_tensor.shape}")
                        
            except Exception as e:
                results['failed'] += 1
                results['processing_errors'].append({
                    'path': video_path,
                    'phrase': phrase,
                    'error': str(e)
                })
                print(f"❌ {Path(video_path).name}: ERROR - {e}")
        
        # Summary
        total = len(df)
        success_rate = results['successful'] / total * 100
        
        print(f"\nVideo Processing Results:")
        print(f"✅ Successful: {results['successful']}/{total} ({success_rate:.1f}%)")
        print(f"⚠️  Shape issues: {len(results['shape_issues'])}")
        print(f"❌ Processing errors: {results['failed']}")
        
        return results['successful'] == total
    
    def test_model_creation(self):
        """Test model creation and basic functionality"""
        
        print("\n" + "="*60)
        print("TESTING MODEL CREATION")
        print("="*60)
        
        try:
            # Create model
            self.model = Mobile3DTiny(
                num_classes=len(self.phrases),
                hidden_dim=self.config.get('model', {}).get('hidden_dim', 256),
                num_gru_layers=self.config.get('model', {}).get('num_gru_layers', 2),
                dropout=self.config.get('model', {}).get('dropout', 0.2)
            ).to(self.device)
            
            param_count = self.model.get_num_parameters()
            print(f"✅ Model created successfully")
            print(f"✅ Parameters: {param_count:,}")
            print(f"✅ Target <8M: {'✅' if param_count < 8_000_000 else '❌'}")
            
            # Test forward pass
            channels = 1 if self.config.get('grayscale', True) else 3
            test_input = torch.randn(
                2, channels, 
                self.config.get('frames', 32),
                self.config.get('height', 96),
                self.config.get('width', 96)
            ).to(self.device)
            
            with torch.no_grad():
                output = self.model(test_input)
            
            expected_output_shape = (2, len(self.phrases))
            if output.shape == expected_output_shape:
                print(f"✅ Forward pass: {test_input.shape} → {output.shape}")
                print(f"✅ Output probabilities sum: {torch.softmax(output, dim=1).sum(dim=1)}")
                return True
            else:
                print(f"❌ Output shape {output.shape} != expected {expected_output_shape}")
                return False
                
        except Exception as e:
            print(f"❌ Model creation failed: {e}")
            return False
    
    def test_inference_pipeline(self, test_videos=None):
        """Test inference pipeline on sample videos"""
        
        print("\n" + "="*60)
        print("TESTING INFERENCE PIPELINE")
        print("="*60)
        
        if self.model is None:
            print("❌ Model not created. Run test_model_creation() first.")
            return False
        
        # Get test videos
        if test_videos is None:
            manifest_path = 'data/manifest.csv'
            if Path(manifest_path).exists():
                df = pd.read_csv(manifest_path)
                test_videos = df.head(5)['video_path'].tolist()
            else:
                print("❌ No test videos available")
                return False
        
        print(f"Testing inference on {len(test_videos)} videos...")
        
        results = []
        self.model.eval()
        
        for video_path in test_videos:
            try:
                start_time = time.time()
                
                # Process video
                video_tensor = self.processor.process_video(video_path)
                video_batch = video_tensor.unsqueeze(0).to(self.device)  # Add batch dimension
                
                # Run inference
                with torch.no_grad():
                    logits = self.model(video_batch)
                    probabilities = torch.softmax(logits, dim=1)
                    
                # Get predictions
                probs = probabilities.cpu().numpy()[0]
                top_indices = np.argsort(probs)[::-1]
                
                inference_time = (time.time() - start_time) * 1000
                
                # Top prediction
                best_idx = top_indices[0]
                best_phrase = self.phrases[best_idx]
                best_confidence = float(probs[best_idx])
                
                # Top 3 predictions
                top3 = [(self.phrases[idx], float(probs[idx])) for idx in top_indices[:3]]
                
                result = {
                    'video_path': video_path,
                    'video_name': Path(video_path).name,
                    'predicted_phrase': best_phrase,
                    'confidence': best_confidence,
                    'top3': top3,
                    'inference_time_ms': inference_time
                }
                
                results.append(result)
                
                print(f"✅ {result['video_name']}")
                print(f"   Predicted: '{result['predicted_phrase']}' ({result['confidence']:.3f})")
                print(f"   Time: {result['inference_time_ms']:.1f}ms")
                print(f"   Top3: {result['top3']}")
                
            except Exception as e:
                print(f"❌ {Path(video_path).name}: {e}")
        
        # Performance summary
        if results:
            avg_time = np.mean([r['inference_time_ms'] for r in results])
            avg_confidence = np.mean([r['confidence'] for r in results])
            
            print(f"\nInference Performance:")
            print(f"✅ Average time: {avg_time:.1f}ms")
            print(f"✅ Average confidence: {avg_confidence:.3f}")
            print(f"✅ Target <150ms: {'✅' if avg_time < 150 else '❌'}")
        
        return results
    
    def validate_reference_videos(self, reference_dir):
        """Validate against reference videos in desktop folder"""
        
        print("\n" + "="*60)
        print("VALIDATING REFERENCE VIDEOS")
        print("="*60)
        
        reference_path = Path.home() / "Desktop" / reference_dir
        
        if not reference_path.exists():
            print(f"❌ Reference directory not found: {reference_path}")
            return None
        
        print(f"Looking for reference videos in: {reference_path}")
        
        # Find video files
        video_extensions = ['.mp4', '.webm', '.avi', '.mov', '.MP4', '.MOV']
        reference_videos = []
        
        for ext in video_extensions:
            reference_videos.extend(reference_path.glob(f'*{ext}'))
        
        if not reference_videos:
            print(f"❌ No video files found in {reference_path}")
            return None
        
        print(f"Found {len(reference_videos)} reference videos")
        
        # Process each reference video
        validation_results = []
        
        for video_path in reference_videos:
            try:
                # Try to extract expected phrase from filename
                filename = video_path.stem.lower()
                expected_phrase = self.extract_phrase_from_filename(filename)
                
                print(f"\n📹 Processing: {video_path.name}")
                print(f"   Expected phrase: '{expected_phrase}'")
                
                # Run inference
                start_time = time.time()
                video_tensor = self.processor.process_video(video_path)
                video_batch = video_tensor.unsqueeze(0).to(self.device)
                
                with torch.no_grad():
                    logits = self.model(video_batch)
                    probabilities = torch.softmax(logits, dim=1)
                
                probs = probabilities.cpu().numpy()[0]
                top_indices = np.argsort(probs)[::-1]
                
                inference_time = (time.time() - start_time) * 1000
                
                # Get predictions
                predicted_phrase = self.phrases[top_indices[0]]
                confidence = float(probs[top_indices[0]])
                
                # Check if prediction matches expected
                is_correct = predicted_phrase == expected_phrase if expected_phrase else None
                
                result = {
                    'video_name': video_path.name,
                    'video_path': str(video_path),
                    'expected_phrase': expected_phrase,
                    'predicted_phrase': predicted_phrase,
                    'confidence': confidence,
                    'is_correct': is_correct,
                    'inference_time_ms': inference_time,
                    'top5': [(self.phrases[idx], float(probs[idx])) for idx in top_indices[:5]]
                }
                
                validation_results.append(result)
                
                status = "✅" if is_correct else "❌" if is_correct is False else "❓"
                print(f"   {status} Predicted: '{predicted_phrase}' ({confidence:.3f})")
                print(f"   Time: {inference_time:.1f}ms")
                
            except Exception as e:
                print(f"❌ Error processing {video_path.name}: {e}")
        
        return validation_results
    
    def extract_phrase_from_filename(self, filename):
        """Extract expected phrase from filename"""
        
        # Clean filename
        filename = filename.lower().replace('_', ' ').replace('-', ' ')
        
        # Try to match against known phrases
        for phrase in self.phrases:
            phrase_clean = phrase.lower()
            
            # Direct match
            if phrase_clean in filename:
                return phrase
            
            # Try without punctuation
            phrase_no_punct = phrase_clean.replace("'", "").replace(",", "").replace(".", "")
            if phrase_no_punct in filename:
                return phrase
            
            # Try key words
            phrase_words = phrase_clean.split()
            if len(phrase_words) >= 2:
                key_words = [w for w in phrase_words if len(w) > 2]  # Skip short words
                if all(word in filename for word in key_words[:3]):  # Check first 3 key words
                    return phrase
        
        return None  # Could not determine expected phrase
    
    def generate_validation_report(self, validation_results, output_path='validation_report.json'):
        """Generate comprehensive validation report"""
        
        print("\n" + "="*60)
        print("GENERATING VALIDATION REPORT")
        print("="*60)
        
        if not validation_results:
            print("❌ No validation results to report")
            return
        
        # Calculate metrics
        total_videos = len(validation_results)
        correct_predictions = sum(1 for r in validation_results if r['is_correct'] is True)
        unknown_expected = sum(1 for r in validation_results if r['expected_phrase'] is None)
        
        accuracy = correct_predictions / (total_videos - unknown_expected) if (total_videos - unknown_expected) > 0 else 0
        avg_confidence = np.mean([r['confidence'] for r in validation_results])
        avg_inference_time = np.mean([r['inference_time_ms'] for r in validation_results])
        
        # Per-phrase analysis
        phrase_analysis = defaultdict(list)
        for result in validation_results:
            if result['expected_phrase']:
                phrase_analysis[result['expected_phrase']].append(result)
        
        phrase_accuracy = {}
        for phrase, results in phrase_analysis.items():
            correct = sum(1 for r in results if r['is_correct'])
            phrase_accuracy[phrase] = correct / len(results) if results else 0
        
        # Create report
        report = {
            'summary': {
                'total_videos': total_videos,
                'correct_predictions': correct_predictions,
                'unknown_expected': unknown_expected,
                'accuracy': accuracy,
                'avg_confidence': avg_confidence,
                'avg_inference_time_ms': avg_inference_time,
                'meets_latency_target': avg_inference_time < 150,
                'meets_accuracy_target': accuracy >= 0.90
            },
            'phrase_accuracy': phrase_accuracy,
            'detailed_results': validation_results,
            'system_info': {
                'model_parameters': self.model.get_num_parameters() if self.model else None,
                'device': str(self.device),
                'config': self.config
            }
        }
        
        # Save report
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print(f"📊 Validation Summary:")
        print(f"   Total videos: {total_videos}")
        print(f"   Accuracy: {accuracy:.1%} ({correct_predictions}/{total_videos - unknown_expected})")
        print(f"   Avg confidence: {avg_confidence:.3f}")
        print(f"   Avg inference time: {avg_inference_time:.1f}ms")
        print(f"   Meets targets: Accuracy {'✅' if accuracy >= 0.90 else '❌'}, Latency {'✅' if avg_inference_time < 150 else '❌'}")
        
        if phrase_accuracy:
            print(f"\n📈 Per-phrase accuracy:")
            for phrase, acc in sorted(phrase_accuracy.items(), key=lambda x: x[1]):
                print(f"   {phrase}: {acc:.1%}")
        
        print(f"\n📄 Full report saved to: {output_path}")
        
        return report


def main():
    """Run comprehensive VSR validation"""
    
    print("🧪 LIGHTWEIGHT VSR SYSTEM VALIDATION")
    print("="*60)
    
    validator = VSRValidator()
    
    # Test 1: Video Processing
    print("\n🎬 Testing video processing...")
    video_processing_ok = validator.test_video_processing()
    
    # Test 2: Model Creation
    print("\n🤖 Testing model creation...")
    model_creation_ok = validator.test_model_creation()
    
    # Test 3: Inference Pipeline
    print("\n⚡ Testing inference pipeline...")
    inference_results = validator.test_inference_pipeline() if model_creation_ok else None
    
    # Test 4: Reference Video Validation
    print("\n📹 Validating against reference videos...")
    reference_results = validator.validate_reference_videos("icu-videos-today") if model_creation_ok else None
    
    # Generate Report
    if reference_results:
        print("\n📊 Generating validation report...")
        report = validator.generate_validation_report(reference_results)
    
    # Final Assessment
    print("\n" + "="*60)
    print("FINAL SYSTEM ASSESSMENT")
    print("="*60)
    
    all_tests_passed = video_processing_ok and model_creation_ok and inference_results
    
    print(f"✅ Video Processing: {'PASS' if video_processing_ok else 'FAIL'}")
    print(f"✅ Model Creation: {'PASS' if model_creation_ok else 'FAIL'}")
    print(f"✅ Inference Pipeline: {'PASS' if inference_results else 'FAIL'}")
    print(f"✅ Reference Validation: {'PASS' if reference_results else 'FAIL'}")
    
    if all_tests_passed:
        print(f"\n🎉 SYSTEM READY FOR TRAINING AND DEPLOYMENT!")
    else:
        print(f"\n⚠️  SYSTEM NEEDS FIXES BEFORE DEPLOYMENT")
    
    return all_tests_passed


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
