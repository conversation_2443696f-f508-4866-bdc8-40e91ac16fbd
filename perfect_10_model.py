#!/usr/bin/env python3
"""
Perfect 10 Mobile3DTiny model for 10-class ICU phrase classification
Ultra-focused on phrases with 100% baseline accuracy
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from pathlib import Path
import sys
import json

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.model import Mobile3DTiny

class Perfect10Mobile3DTiny(nn.Module):
    """
    Ultra-focused version of Mobile3DTiny for 10 perfect-performance ICU phrases
    Supports transfer learning from 26-class baseline model
    """
    
    def __init__(self, num_classes=10, pretrained_26_class_path=None):
        super(Perfect10Mobile3DTiny, self).__init__()
        
        self.num_classes = num_classes
        
        # Create base 26-class model first
        self.base_model = Mobile3DTiny(num_classes=26)
        
        # Load pretrained weights if provided
        if pretrained_26_class_path and Path(pretrained_26_class_path).exists():
            self.load_pretrained_weights(pretrained_26_class_path)
            print(f"✅ Loaded pretrained weights from: {pretrained_26_class_path}")
        
        # Replace final classifier for 10 classes
        # Get the feature dimension from the original classifier
        if hasattr(self.base_model, 'classifier'):
            in_features = self.base_model.classifier.in_features
        elif hasattr(self.base_model, 'fc'):
            in_features = self.base_model.fc.in_features
        else:
            # Default feature dimension for Mobile3DTiny
            in_features = 512
        
        # Create new classifier for 10 classes with enhanced regularization
        self.perfect_classifier = nn.Sequential(
            nn.Dropout(0.3),  # Enhanced: Increased dropout to 0.3 for better generalization
            nn.Linear(in_features, num_classes)
        )
        
        # Replace the original classifier
        if hasattr(self.base_model, 'classifier'):
            self.base_model.classifier = self.perfect_classifier
        elif hasattr(self.base_model, 'fc'):
            self.base_model.fc = self.perfect_classifier
        
        print(f"🎯 Perfect10Mobile3DTiny initialized for {num_classes} classes")
    
    def load_pretrained_weights(self, checkpoint_path: str):
        """Load pretrained weights from 26-class baseline model"""
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            
            # Extract model state dict
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint
            
            # Load weights into base model (excluding final classifier)
            model_dict = self.base_model.state_dict()
            
            # Filter out classifier weights (we'll replace this layer)
            pretrained_dict = {}
            for k, v in state_dict.items():
                if 'classifier' not in k and 'fc' not in k:
                    if k in model_dict and model_dict[k].shape == v.shape:
                        pretrained_dict[k] = v
                        print(f"   Loaded: {k} {v.shape}")
                    else:
                        print(f"   Skipped: {k} (shape mismatch or not found)")
            
            # Update model dict
            model_dict.update(pretrained_dict)
            self.base_model.load_state_dict(model_dict, strict=False)
            
            print(f"✅ Transfer learning: Loaded {len(pretrained_dict)} layers")
            
        except Exception as e:
            print(f"⚠️  Failed to load pretrained weights: {e}")
    
    def forward(self, x):
        """Forward pass through the perfect 10 model"""
        return self.base_model(x)
    
    def get_num_parameters(self):
        """Get total number of parameters"""
        return sum(p.numel() for p in self.parameters())
    
    def freeze_backbone(self, freeze=True):
        """Freeze/unfreeze backbone layers for fine-tuning"""
        
        for name, param in self.base_model.named_parameters():
            if 'classifier' not in name and 'fc' not in name:
                param.requires_grad = not freeze
        
        status = "frozen" if freeze else "unfrozen"
        print(f"🔒 Backbone layers {status}")

class Perfect10ModelManager:
    """Manager for perfect 10 model operations"""
    
    def __init__(self, baseline_model_path: str, perfect_phrases_path: str):
        """Initialize model manager"""
        
        self.baseline_model_path = baseline_model_path
        self.perfect_phrases_path = perfect_phrases_path
        
        # Load perfect phrases info
        with open(perfect_phrases_path, 'r') as f:
            self.perfect_info = json.load(f)
        
        self.perfect_phrases = self.perfect_info['perfect_phrases']
        self.phrase_to_idx = self.perfect_info['phrase_to_idx']
        self.num_classes = len(self.perfect_phrases)
        
        print(f"🎯 Perfect 10 Model Manager Initialized")
        print(f"   Perfect phrases: {self.num_classes}")
        print(f"   Baseline model: {baseline_model_path}")
    
    def create_perfect_model(self) -> Perfect10Mobile3DTiny:
        """Create perfect 10 model with transfer learning"""
        
        print(f"\n🤖 Creating Perfect 10 Model")
        print("=" * 30)
        
        # Create model with transfer learning
        model = Perfect10Mobile3DTiny(
            num_classes=self.num_classes,
            pretrained_26_class_path=self.baseline_model_path
        )
        
        print(f"✅ Model created with {model.get_num_parameters():,} parameters")
        
        return model
    
    def save_perfect_model(self, model: Perfect10Mobile3DTiny, 
                          save_path: str, epoch: int = 0, 
                          best_accuracy: float = 0.0):
        """Save perfect 10 model checkpoint"""
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'num_classes': self.num_classes,
            'perfect_phrases': self.perfect_phrases,
            'phrase_to_idx': self.phrase_to_idx,
            'best_accuracy': best_accuracy,
            'model_info': {
                'name': 'Perfect10Mobile3DTiny',
                'parameters': model.get_num_parameters(),
                'baseline_model': self.baseline_model_path,
                'transfer_learning': True,
                'focus': '100% baseline accuracy phrases'
            }
        }
        
        torch.save(checkpoint, save_path)
        print(f"💾 Perfect 10 model saved: {save_path}")

def test_perfect_10_model():
    """Test the perfect 10 model creation and transfer learning"""
    
    print("🧪 Testing Perfect 10 Model Creation")
    print("=" * 40)
    
    # Paths
    baseline_model_path = "checkpoints/reference_training/best_model.pth"
    perfect_summary_path = "perfect_10_phrases_summary.json"
    
    # Check if files exist
    if not Path(baseline_model_path).exists():
        print(f"⚠️  Baseline model not found: {baseline_model_path}")
        return False
    
    if not Path(perfect_summary_path).exists():
        print(f"⚠️  Perfect summary not found: {perfect_summary_path}")
        return False
    
    try:
        # Create model manager
        manager = Perfect10ModelManager(baseline_model_path, perfect_summary_path)
        
        # Create perfect model
        model = manager.create_perfect_model()
        
        # Test forward pass with enhanced dimensions
        batch_size = 2
        frames = 64  # Enhanced: 64 frames instead of 32
        height = 112  # Enhanced: 112×112 instead of 96×96
        width = 112

        dummy_input = torch.randn(batch_size, 1, frames, height, width)
        
        model.eval()
        with torch.no_grad():
            output = model(dummy_input)
        
        print(f"✅ Forward pass test:")
        print(f"   Input shape: {dummy_input.shape}")
        print(f"   Output shape: {output.shape}")
        print(f"   Expected classes: {manager.num_classes}")
        
        # Test model saving
        test_save_path = "test_perfect_10_model.pth"
        manager.save_perfect_model(model, test_save_path, epoch=0, best_accuracy=0.0)
        
        # Clean up test file
        Path(test_save_path).unlink()
        
        print(f"\n🎉 Perfect 10 Model Test Successful!")
        print(f"🏆 Ready for ultra-focused training on 100% accuracy phrases!")
        return True
        
    except Exception as e:
        print(f"❌ Perfect 10 model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    
    # Test perfect 10 model
    success = test_perfect_10_model()
    
    if success:
        print(f"\n🚀 Ready for Perfect 10 Training!")
        print("=" * 35)
        print("✅ Perfect 10 model architecture ready")
        print("✅ Transfer learning implemented")
        print("✅ 10-class classification configured")
        print("✅ Ultra-focused on 100% accuracy phrases")
        
        print(f"\n🏆 Perfect 10 Advantages:")
        print("✅ Maximum accuracy potential")
        print("✅ Fastest training convergence")
        print("✅ Highest confidence predictions")
        print("✅ Most reliable deployment")
    else:
        print(f"\n❌ Perfect 10 model setup failed")

if __name__ == '__main__':
    main()
