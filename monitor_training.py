#!/usr/bin/env python3
"""
Monitor the training progress
"""

import time
import json
from pathlib import Path
import matplotlib.pyplot as plt

def monitor_training_progress():
    """Monitor training progress and show updates"""
    
    print("📊 Monitoring ICU Lipreading Training Progress")
    print("=" * 50)
    
    log_dir = Path("logs/reference_training")
    checkpoint_dir = Path("checkpoints/reference_training")
    
    last_epoch = 0
    
    while True:
        try:
            # Check for training report
            report_path = log_dir / "training_report.json"
            
            if report_path.exists():
                with open(report_path, 'r') as f:
                    report = json.load(f)
                
                print(f"\n🎉 Training Complete!")
                print("=" * 25)
                
                training_info = report.get('training', {})
                test_results = report.get('test_results', {})
                
                print(f"✅ Epochs completed: {training_info.get('epochs_completed', 'Unknown')}")
                print(f"✅ Best validation accuracy: {training_info.get('best_val_accuracy', 0):.3f}")
                print(f"✅ Final training accuracy: {training_info.get('final_train_accuracy', 0):.3f}")
                print(f"✅ Test accuracy: {test_results.get('accuracy', 0):.3f}")
                print(f"✅ Test F1-score: {test_results.get('f1_score', 0):.3f}")
                
                # Check for generated files
                if (log_dir / "training_curves.png").exists():
                    print(f"📊 Training curves: {log_dir / 'training_curves.png'}")
                
                if (log_dir / "confusion_matrix.png").exists():
                    print(f"📊 Confusion matrix: {log_dir / 'confusion_matrix.png'}")
                
                if (checkpoint_dir / "best_model.pth").exists():
                    print(f"💾 Best model: {checkpoint_dir / 'best_model.pth'}")
                
                break
            
            # Check for checkpoints to estimate progress
            checkpoints = list(checkpoint_dir.glob("checkpoint_epoch_*.pth"))
            
            if checkpoints:
                # Get latest checkpoint
                latest_checkpoint = max(checkpoints, key=lambda x: int(x.stem.split('_')[-1]))
                current_epoch = int(latest_checkpoint.stem.split('_')[-1])
                
                if current_epoch > last_epoch:
                    print(f"📈 Progress: Epoch {current_epoch} completed")
                    last_epoch = current_epoch
            
            # Check if training curves exist (indicates training is progressing)
            curves_path = log_dir / "training_curves.png"
            if curves_path.exists():
                mod_time = curves_path.stat().st_mtime
                current_time = time.time()
                
                if current_time - mod_time < 300:  # Updated in last 5 minutes
                    print(f"📊 Training curves updated recently")
            
            time.sleep(30)  # Check every 30 seconds
            
        except KeyboardInterrupt:
            print(f"\n⏹️  Monitoring stopped by user")
            break
        except Exception as e:
            print(f"⚠️  Monitoring error: {e}")
            time.sleep(60)

def show_training_status():
    """Show current training status"""
    
    print(f"\n📋 Current Training Status")
    print("=" * 30)
    
    log_dir = Path("logs/reference_training")
    checkpoint_dir = Path("checkpoints/reference_training")
    
    # Check directories exist
    if not log_dir.exists():
        print(f"⚠️  Log directory not found: {log_dir}")
        return
    
    if not checkpoint_dir.exists():
        print(f"⚠️  Checkpoint directory not found: {checkpoint_dir}")
        return
    
    # Check for checkpoints
    checkpoints = list(checkpoint_dir.glob("checkpoint_epoch_*.pth"))
    
    if checkpoints:
        latest_checkpoint = max(checkpoints, key=lambda x: int(x.stem.split('_')[-1]))
        current_epoch = int(latest_checkpoint.stem.split('_')[-1])
        print(f"📊 Latest checkpoint: Epoch {current_epoch}")
    else:
        print(f"📊 No checkpoints found yet")
    
    # Check for best model
    best_model_path = checkpoint_dir / "best_model.pth"
    if best_model_path.exists():
        print(f"💾 Best model saved: {best_model_path}")
    
    # Check for training curves
    curves_path = log_dir / "training_curves.png"
    if curves_path.exists():
        print(f"📊 Training curves: {curves_path}")
    
    # Check for confusion matrix
    cm_path = log_dir / "confusion_matrix.png"
    if cm_path.exists():
        print(f"📊 Confusion matrix: {cm_path}")
    
    # Check for training report
    report_path = log_dir / "training_report.json"
    if report_path.exists():
        print(f"📋 Training report: {report_path}")
        
        try:
            with open(report_path, 'r') as f:
                report = json.load(f)
            
            training_info = report.get('training', {})
            print(f"✅ Training completed!")
            print(f"   Best val accuracy: {training_info.get('best_val_accuracy', 0):.3f}")
            
        except Exception as e:
            print(f"⚠️  Could not read report: {e}")

def main():
    """Main monitoring function"""
    
    # Show current status
    show_training_status()
    
    # Start monitoring
    try:
        monitor_training_progress()
    except KeyboardInterrupt:
        print(f"\n👋 Monitoring stopped")

if __name__ == '__main__':
    main()
