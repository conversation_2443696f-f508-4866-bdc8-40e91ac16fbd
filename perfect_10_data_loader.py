#!/usr/bin/env python3
"""
Custom data loader for Perfect 10 ICU lipreading training
Ultra-focused on 10 phrases with 100% baseline accuracy
"""

import pandas as pd
import torch
from torch.utils.data import DataLoader, Dataset
from sklearn.model_selection import train_test_split
import numpy as np
from pathlib import Path
import sys
from typing import Dict, List, Tuple
from collections import defaultdict

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

class Perfect10VideoDataset(Dataset):
    """Dataset for Perfect 10 training"""
    
    def __init__(self, video_paths: List[str], labels: List[int], 
                 video_processor: VideoProcessor, phrases: List[str]):
        self.video_paths = video_paths
        self.labels = labels
        self.video_processor = video_processor
        self.phrases = phrases
        
    def __len__(self):
        return len(self.video_paths)
    
    def __getitem__(self, idx):
        video_path = self.video_paths[idx]
        label = self.labels[idx]
        phrase = self.phrases[idx]
        
        # Process video
        try:
            video_tensor = self.video_processor.process_video(video_path)
        except Exception as e:
            print(f"Error processing {video_path}: {e}")
            # Return zeros if processing fails
            video_tensor = torch.zeros(1, 32, 96, 96)
        
        metadata = {
            'video_path': video_path,
            'phrase': phrase,
            'label': label
        }
        
        return video_tensor, torch.tensor(label, dtype=torch.long), metadata

def create_perfect_10_dataloaders(config: Dict, manifest_path: str):
    """Create data loaders for Perfect 10 training with careful splitting"""
    
    print(f"📊 Creating Perfect 10 Data Loaders")
    print("=" * 35)
    
    # Load manifest
    manifest_df = pd.read_csv(manifest_path)
    print(f"📋 Loaded manifest: {len(manifest_df)} videos")
    
    # Extract data
    video_paths = manifest_df['video_path'].tolist()
    phrases = manifest_df['phrase'].tolist()
    
    # Verify video files exist
    existing_paths = []
    existing_phrases = []
    
    for path, phrase in zip(video_paths, phrases):
        if Path(path).exists():
            existing_paths.append(path)
            existing_phrases.append(phrase)
        else:
            print(f"⚠️  Video not found: {path}")
    
    print(f"✅ Found {len(existing_paths)} existing videos")
    
    # Create phrase mapping for 10 classes
    unique_phrases = sorted(list(set(existing_phrases)))
    phrase_to_idx = {phrase: idx for idx, phrase in enumerate(unique_phrases)}
    labels = [phrase_to_idx[phrase] for phrase in existing_phrases]
    
    print(f"📊 Perfect 10 phrase distribution:")
    phrase_counts = {}
    for phrase in existing_phrases:
        phrase_counts[phrase] = phrase_counts.get(phrase, 0) + 1
    
    for phrase, count in sorted(phrase_counts.items()):
        print(f"   {phrase.title()}: {count} videos")
    
    # Careful splitting for very small dataset (30 videos total)
    train_ratio = config['data']['train_ratio']
    val_ratio = config['data']['val_ratio']
    test_ratio = config['data']['test_ratio']
    
    # Ensure ratios sum to 1
    total_ratio = train_ratio + val_ratio + test_ratio
    train_ratio /= total_ratio
    val_ratio /= total_ratio
    test_ratio /= total_ratio
    
    print(f"📊 Data splits: Train {train_ratio:.2f}, Val {val_ratio:.2f}, Test {test_ratio:.2f}")
    
    # Manual stratified splitting for 3 videos per phrase
    print(f"🎯 Using manual stratified splitting (3 videos per phrase)")
    
    # Group videos by phrase
    phrase_groups = defaultdict(list)
    for i, phrase in enumerate(existing_phrases):
        phrase_groups[phrase].append(i)
    
    train_indices = []
    val_indices = []
    test_indices = []
    
    # Split each phrase group (3 videos each)
    for phrase, indices in phrase_groups.items():
        n_videos = len(indices)
        
        if n_videos == 3:
            # Perfect case: 2 train, 1 val, 0 test OR 2 train, 0 val, 1 test
            # We'll do 2 train, 1 val for better validation
            train_indices.extend(indices[:2])
            val_indices.extend(indices[2:3])
            # No test videos for this phrase
        elif n_videos == 2:
            # 1 train, 1 val
            train_indices.extend(indices[:1])
            val_indices.extend(indices[1:2])
        elif n_videos == 1:
            # 1 train
            train_indices.extend(indices)
        else:
            # More than 3 videos: 2 train, 1 val, rest test
            train_indices.extend(indices[:2])
            val_indices.extend(indices[2:3])
            test_indices.extend(indices[3:])
        
        print(f"   {phrase.title()}: {len([i for i in indices if i in train_indices])} train, "
              f"{len([i for i in indices if i in val_indices])} val, "
              f"{len([i for i in indices if i in test_indices])} test")
    
    # Extract data for each split
    train_paths = [existing_paths[i] for i in train_indices]
    train_phrases = [existing_phrases[i] for i in train_indices]
    train_labels = [labels[i] for i in train_indices]
    
    val_paths = [existing_paths[i] for i in val_indices]
    val_phrases = [existing_phrases[i] for i in val_indices]
    val_labels = [labels[i] for i in val_indices]
    
    test_paths = [existing_paths[i] for i in test_indices]
    test_phrases = [existing_phrases[i] for i in test_indices]
    test_labels = [labels[i] for i in test_indices]
    
    print(f"📊 Final splits:")
    print(f"   Train: {len(train_paths)} videos")
    print(f"   Val: {len(val_paths)} videos")
    print(f"   Test: {len(test_paths)} videos")
    
    # Create video processor
    video_processor = VideoProcessor(
        target_frames=config['data']['frames'],
        target_size=(config['data']['height'], config['data']['width']),
        grayscale=config['data']['grayscale']
    )
    
    # Create datasets
    train_dataset = Perfect10VideoDataset(train_paths, train_labels, video_processor, train_phrases)
    val_dataset = Perfect10VideoDataset(val_paths, val_labels, video_processor, val_phrases)
    test_dataset = Perfect10VideoDataset(test_paths, test_labels, video_processor, test_phrases)
    
    # Create data loaders
    batch_size = config['training']['batch_size']
    num_workers = config['hardware']['num_workers']
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=config['hardware'].get('pin_memory', True)
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=config['hardware'].get('pin_memory', True)
    ) if len(val_dataset) > 0 else None
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=config['hardware'].get('pin_memory', True)
    ) if len(test_dataset) > 0 else None
    
    # Create info dictionary
    data_info = {
        'train_size': len(train_dataset),
        'val_size': len(val_dataset),
        'test_size': len(test_dataset),
        'num_classes': len(unique_phrases),
        'phrase_to_idx': phrase_to_idx,
        'phrases': unique_phrases
    }
    
    print(f"✅ Perfect 10 data loaders created successfully")
    
    return train_loader, val_loader, test_loader, data_info

def test_perfect_10_dataloaders():
    """Test the Perfect 10 data loaders"""
    
    import yaml
    
    print("🧪 Testing Perfect 10 Data Loaders")
    print("=" * 40)
    
    # Load config
    with open('configs/perfect_10_training.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Create data loaders
    try:
        train_loader, val_loader, test_loader, data_info = create_perfect_10_dataloaders(
            config, config['data']['manifest_path']
        )
        
        print(f"✅ Data loaders created successfully")
        print(f"📊 Train: {data_info['train_size']} videos")
        print(f"📊 Val: {data_info['val_size']} videos")
        print(f"📊 Test: {data_info['test_size']} videos")
        print(f"📊 Classes: {data_info['num_classes']}")
        
        # Test first batch
        if train_loader:
            for videos, labels, metadata in train_loader:
                print(f"📊 Batch shape: {videos.shape}")
                print(f"📊 Label shape: {labels.shape}")
                print(f"📊 Video range: [{videos.min():.3f}, {videos.max():.3f}]")
                print(f"📊 Label range: [{labels.min()}, {labels.max()}]")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ Data loader test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_perfect_10_dataloaders()
