#!/usr/bin/env python3
"""
Enhanced video analyzer with Test-Time Augmentation (TTA) and 64-frame, 112×112 support
Supports both baseline and enhanced preprocessing for A/B comparison
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
from pathlib import Path
import sys
import json
from typing import List, Dict, Optional, Tuple

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class EnhancedVideoAnalyzer:
    """Enhanced analyzer with TTA and enhanced preprocessing support"""
    
    def __init__(self, model_path: str, use_enhanced: bool = True):
        """Initialize the enhanced analyzer"""
        
        self.model_path = Path(model_path)
        self.device = torch.device('cpu')  # Use CPU for consistency
        self.use_enhanced = use_enhanced
        
        # Perfect 10 phrases (in order)
        self.perfect_phrases = [
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today"
        ]
        
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        self.model = None
        
        # Initialize video processors
        if use_enhanced:
            # Enhanced preprocessing: 64 frames, 112×112, z-score normalization
            self.video_processor = VideoProcessor(
                target_frames=64,
                target_size=(112, 112),
                grayscale=True,
                fps=25.0,
                mouth_crop=None,  # No cropping for pre-cropped videos
                use_dataset_normalization=True
            )
            print(f"🎯 Enhanced Video Analyzer (64 frames, 112×112, z-score)")
        else:
            # Baseline preprocessing: 32 frames, 96×96, [0,1] normalization
            self.video_processor = VideoProcessor(
                target_frames=32,
                target_size=(96, 96),
                grayscale=True,
                mouth_crop=None,
                use_dataset_normalization=False
            )
            print(f"🎯 Baseline Video Analyzer (32 frames, 96×96, [0,1])")
        
        print(f"   Model: {self.model_path}")
        print(f"   Device: {self.device}")
        print(f"   Perfect phrases: {len(self.perfect_phrases)}")
    
    def load_model(self) -> bool:
        """Load the trained Perfect 10 model"""
        
        print(f"\n🤖 Loading Perfect 10 Model")
        print("=" * 30)
        
        if not self.model_path.exists():
            print(f"❌ Model not found: {self.model_path}")
            return False
        
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device)
            self.model = Perfect10Mobile3DTiny(num_classes=10)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            best_val_accuracy = checkpoint.get('best_val_accuracy', 0.0)
            epoch = checkpoint.get('epoch', 0)
            
            print(f"✅ Perfect 10 model loaded successfully")
            print(f"   Parameters: {self.model.get_num_parameters():,}")
            print(f"   Training epoch: {epoch}")
            print(f"   Best validation accuracy: {best_val_accuracy:.1%}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_temporal_crops(self, video_tensor: torch.Tensor) -> List[torch.Tensor]:
        """Create 3 temporal crops for Test-Time Augmentation"""
        
        C, T, H, W = video_tensor.shape
        target_frames = 64 if self.use_enhanced else 32
        
        if T <= target_frames:
            # Video is too short, return single crop
            return [video_tensor]
        
        # Create 3 temporal crops
        crops = []
        
        # Crop 1: Beginning frames [0:target_frames]
        crop1 = video_tensor[:, :target_frames, :, :]
        crops.append(crop1)
        
        # Crop 2: Middle frames [middle-target_frames//2:middle+target_frames//2]
        middle = T // 2
        start_middle = max(0, middle - target_frames // 2)
        end_middle = min(T, start_middle + target_frames)
        crop2 = video_tensor[:, start_middle:end_middle, :, :]
        
        # Pad if necessary
        if crop2.shape[1] < target_frames:
            padding_needed = target_frames - crop2.shape[1]
            last_frame = crop2[:, -1:, :, :].repeat(1, padding_needed, 1, 1)
            crop2 = torch.cat([crop2, last_frame], dim=1)
        
        crops.append(crop2)
        
        # Crop 3: End frames [end-target_frames:end]
        crop3 = video_tensor[:, -target_frames:, :, :]
        crops.append(crop3)
        
        return crops
    
    def analyze_video_with_tta(self, video_path: str, use_tta: bool = True) -> Dict:
        """Analyze video with optional Test-Time Augmentation"""
        
        video_name = Path(video_path).name
        print(f"\n🎬 Analyzing: {video_name}")
        print(f"   TTA: {'Enabled' if use_tta else 'Disabled'}")
        print(f"   Mode: {'Enhanced' if self.use_enhanced else 'Baseline'}")
        
        if not Path(video_path).exists():
            return {
                'success': False,
                'error': f'Video file not found: {video_path}'
            }
        
        try:
            # Process video through pipeline
            video_tensor = self.video_processor.process_video(video_path)
            
            print(f"   📊 Processed tensor: {video_tensor.shape}")
            print(f"   📊 Value range: [{video_tensor.min():.3f}, {video_tensor.max():.3f}]")
            
            if use_tta and video_tensor.shape[1] > (64 if self.use_enhanced else 32):
                # Apply Test-Time Augmentation
                temporal_crops = self.create_temporal_crops(video_tensor)
                print(f"   🔄 TTA: {len(temporal_crops)} temporal crops")
                
                # Get predictions for each crop
                all_logits = []
                
                for i, crop in enumerate(temporal_crops):
                    crop_batch = crop.unsqueeze(0).to(self.device)  # Add batch dimension
                    
                    with torch.no_grad():
                        logits = self.model(crop_batch)
                        all_logits.append(logits[0])  # Remove batch dimension
                
                # Average logits across crops
                final_logits = torch.stack(all_logits).mean(dim=0)
                probabilities = F.softmax(final_logits, dim=0)
                
                print(f"   ✅ TTA prediction complete")
                
            else:
                # Single prediction (no TTA)
                video_batch = video_tensor.unsqueeze(0).to(self.device)
                
                with torch.no_grad():
                    logits = self.model(video_batch)
                    final_logits = logits[0]  # Remove batch dimension
                    probabilities = F.softmax(final_logits, dim=0)
                
                print(f"   ✅ Single prediction complete")
            
            # Get top-3 predictions
            top3_probs, top3_indices = torch.topk(probabilities, 3)
            
            top3_probs = top3_probs.cpu().numpy()
            top3_indices = top3_indices.cpu().numpy()
            
            # Convert to phrases
            top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
            
            # Determine confidence level
            top_confidence = float(top3_probs[0])
            if top_confidence >= 0.7:
                confidence_level = "Very High"
                confidence_emoji = "🟢"
            elif top_confidence >= 0.5:
                confidence_level = "Moderate"
                confidence_emoji = "🟡"
            elif top_confidence >= 0.3:
                confidence_level = "Low"
                confidence_emoji = "🟠"
            else:
                confidence_level = "Very Low"
                confidence_emoji = "🔴"
            
            # Get all predictions for analysis
            all_probs = probabilities.cpu().numpy()
            
            return {
                'success': True,
                'video_path': video_path,
                'video_name': video_name,
                'tensor_shape': list(video_tensor.shape),
                'preprocessing_mode': 'enhanced' if self.use_enhanced else 'baseline',
                'tta_used': use_tta and video_tensor.shape[1] > (64 if self.use_enhanced else 32),
                'top_prediction': top3_phrases[0],
                'top_confidence': top_confidence,
                'confidence_level': confidence_level,
                'confidence_emoji': confidence_emoji,
                'top3_phrases': top3_phrases,
                'top3_probabilities': top3_probs.tolist(),
                'all_probabilities': all_probs.tolist(),
                'raw_logits': final_logits.cpu().numpy().tolist()
            }
                
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'video_path': video_path
            }
    
    def display_analysis_results(self, results: Dict):
        """Display comprehensive analysis results"""
        
        if not results['success']:
            print(f"❌ Analysis failed: {results['error']}")
            return
        
        print(f"\n🎯 ENHANCED PREDICTION RESULTS")
        print("=" * 40)
        print(f"📹 Video: {results['video_name']}")
        print(f"🔧 Tensor: {results['tensor_shape']}")
        print(f"⚙️  Mode: {results['preprocessing_mode'].title()}")
        print(f"🔄 TTA: {'Used' if results['tta_used'] else 'Not used'}")
        
        # Top prediction
        top_phrase = results['top_prediction']
        top_confidence = results['top_confidence']
        confidence_emoji = results['confidence_emoji']
        confidence_level = results['confidence_level']
        
        print(f"\n🏆 **TOP PREDICTION**")
        print(f"   Phrase: \"{top_phrase.title()}\"")
        print(f"   Confidence: {top_confidence:.1%}")
        print(f"   Level: {confidence_emoji} {confidence_level}")
        
        # Top-3 predictions
        print(f"\n📊 **TOP-3 PREDICTIONS**")
        for i, (phrase, prob) in enumerate(zip(results['top3_phrases'], results['top3_probabilities'])):
            rank_emoji = ["🥇", "🥈", "🥉"][i]
            print(f"   {rank_emoji} {phrase.title()}: {prob:.1%}")
        
        # All significant predictions (>5%)
        print(f"\n📋 **ALL SIGNIFICANT PREDICTIONS (>5%)**")
        all_probs = results['all_probabilities']
        for i, (phrase, prob) in enumerate(zip(self.perfect_phrases, all_probs)):
            if prob >= 0.05:  # Only show predictions above 5%
                print(f"   {phrase.title()}: {prob:.1%}")

def compare_baseline_vs_enhanced(video_path: str, model_path: str):
    """Compare baseline vs enhanced preprocessing on the same video"""
    
    print(f"🔍 Baseline vs Enhanced Comparison")
    print("=" * 40)
    print(f"📹 Video: {Path(video_path).name}")
    
    # Test baseline
    print(f"\n📊 BASELINE ANALYSIS (32 frames, 96×96)")
    baseline_analyzer = EnhancedVideoAnalyzer(model_path, use_enhanced=False)
    if baseline_analyzer.load_model():
        baseline_results = baseline_analyzer.analyze_video_with_tta(video_path, use_tta=False)
        if baseline_results['success']:
            print(f"   Top prediction: {baseline_results['top_prediction'].title()}")
            print(f"   Confidence: {baseline_results['top_confidence']:.1%}")
    
    # Test enhanced
    print(f"\n📊 ENHANCED ANALYSIS (64 frames, 112×112)")
    enhanced_analyzer = EnhancedVideoAnalyzer(model_path, use_enhanced=True)
    if enhanced_analyzer.load_model():
        enhanced_results = enhanced_analyzer.analyze_video_with_tta(video_path, use_tta=True)
        if enhanced_results['success']:
            print(f"   Top prediction: {enhanced_results['top_prediction'].title()}")
            print(f"   Confidence: {enhanced_results['top_confidence']:.1%}")
            print(f"   TTA used: {enhanced_results['tta_used']}")
    
    # Compare results
    if baseline_results['success'] and enhanced_results['success']:
        print(f"\n🔍 COMPARISON SUMMARY")
        print(f"   Baseline: {baseline_results['top_prediction'].title()} ({baseline_results['top_confidence']:.1%})")
        print(f"   Enhanced: {enhanced_results['top_prediction'].title()} ({enhanced_results['top_confidence']:.1%})")
        
        if baseline_results['top_prediction'] == enhanced_results['top_prediction']:
            print(f"   ✅ CONSISTENT: Same prediction")
        else:
            print(f"   ⚠️  DIFFERENT: Different predictions")
        
        confidence_improvement = enhanced_results['top_confidence'] - baseline_results['top_confidence']
        print(f"   Confidence change: {confidence_improvement:+.1%}")

def main():
    """Main function for enhanced video analysis"""
    
    print("🎬 Enhanced Video Analysis with TTA")
    print("=" * 40)
    
    # Configuration
    model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
    test_video = "/Users/<USER>/Desktop/processed_perfect_10_videos/processed_4.mp4"
    
    # Test enhanced analyzer
    analyzer = EnhancedVideoAnalyzer(model_path, use_enhanced=True)
    
    if not analyzer.load_model():
        print("❌ Failed to load model")
        return
    
    if not Path(test_video).exists():
        print(f"❌ Test video not found: {test_video}")
        return
    
    # Analyze with TTA
    results = analyzer.analyze_video_with_tta(test_video, use_tta=True)
    analyzer.display_analysis_results(results)
    
    # Compare baseline vs enhanced
    compare_baseline_vs_enhanced(test_video, model_path)
    
    print(f"\n🎉 Enhanced Analysis Complete!")

if __name__ == '__main__':
    main()
