#!/usr/bin/env python3
"""
Corrected Video Processor for Perfect 10 ICU Lipreading
Uses exact mouth-cropping coordinates (133, 0, 133, 100) from reference training
"""

import cv2
import numpy as np
import torch
from pathlib import Path
import sys
import os

# Add current directory to path
sys.path.append('.')

class CorrectedICUVideoProcessor:
    """Video processor with exact ICU reference training mouth-cropping parameters"""
    
    def __init__(self, input_folder: str, output_folder: str):
        """Initialize with correct ICU mouth-cropping specifications"""
        
        self.input_folder = Path(input_folder)
        self.output_folder = Path(output_folder)
        
        # Create output folder
        self.output_folder.mkdir(parents=True, exist_ok=True)
        
        # ICU Reference Training Mouth-Cropping Parameters
        # From 400×200 source: crop (133, 0, 133, 100) -> 132-133×100 mouth region
        self.mouth_crop_params = {
            'x': 133,           # Start x coordinate
            'y': 0,             # Start y coordinate  
            'width': 133,       # Crop width
            'height': 100,      # Crop height
            'source_width': 400,
            'source_height': 200,
            'target_width': 96,
            'target_height': 96,
            'target_frames': 32
        }
        
        print(f"🎯 Corrected ICU Video Processor Initialized")
        print(f"   Input folder: {self.input_folder}")
        print(f"   Output folder: {self.output_folder}")
        print(f"   ICU Mouth-cropping: ({self.mouth_crop_params['x']}, {self.mouth_crop_params['y']}, {self.mouth_crop_params['width']}, {self.mouth_crop_params['height']})")
        print(f"   Source: {self.mouth_crop_params['source_width']}×{self.mouth_crop_params['source_height']} → Crop: ~133×100 → Target: 96×96")
    
    def apply_icu_mouth_cropping(self, frame: np.ndarray) -> np.ndarray:
        """Apply exact ICU reference training mouth-cropping"""
        
        # Verify source frame dimensions
        h, w = frame.shape[:2]
        if w != self.mouth_crop_params['source_width'] or h != self.mouth_crop_params['source_height']:
            print(f"   ⚠️  Frame size {w}×{h} doesn't match expected {self.mouth_crop_params['source_width']}×{self.mouth_crop_params['source_height']}")
        
        # Apply exact ICU mouth-cropping coordinates
        x = self.mouth_crop_params['x']
        y = self.mouth_crop_params['y']
        w_crop = self.mouth_crop_params['width']
        h_crop = self.mouth_crop_params['height']
        
        # Ensure coordinates are within frame bounds
        x = max(0, min(x, w - 1))
        y = max(0, min(y, h - 1))
        x_end = min(x + w_crop, w)
        y_end = min(y + h_crop, h)
        
        # Extract mouth region
        mouth_region = frame[y:y_end, x:x_end]
        
        return mouth_region
    
    def process_video_with_icu_cropping(self, video_path: str) -> torch.Tensor:
        """Process video with exact ICU reference training pipeline"""
        
        cap = cv2.VideoCapture(video_path)
        
        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        # Calculate frame indices for exactly 32 frames
        if total_frames <= self.mouth_crop_params['target_frames']:
            # If video has 32 or fewer frames, use all frames and pad if necessary
            frame_indices = list(range(total_frames))
        else:
            # Sample 32 frames evenly across the video
            frame_indices = np.linspace(0, total_frames - 1, self.mouth_crop_params['target_frames'], dtype=int)
        
        processed_frames = []
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Apply ICU mouth-cropping
                mouth_region = self.apply_icu_mouth_cropping(frame)
                
                # Convert to grayscale
                if len(mouth_region.shape) == 3:
                    mouth_gray = cv2.cvtColor(mouth_region, cv2.COLOR_BGR2GRAY)
                else:
                    mouth_gray = mouth_region
                
                # Resize to target size (96×96)
                mouth_resized = cv2.resize(mouth_gray, 
                                         (self.mouth_crop_params['target_width'], 
                                          self.mouth_crop_params['target_height']))
                
                # Normalize to [0,1] range
                mouth_normalized = mouth_resized.astype(np.float32) / 255.0
                
                processed_frames.append(mouth_normalized)
            else:
                print(f"   ⚠️  Could not read frame {frame_idx}")
        
        cap.release()
        
        # Ensure we have exactly 32 frames
        while len(processed_frames) < self.mouth_crop_params['target_frames']:
            # Pad with last frame if needed
            if processed_frames:
                processed_frames.append(processed_frames[-1])
            else:
                # Create black frame if no frames were read
                black_frame = np.zeros((self.mouth_crop_params['target_height'], 
                                      self.mouth_crop_params['target_width']), dtype=np.float32)
                processed_frames.append(black_frame)
        
        # Convert to tensor: [32, 96, 96] -> [1, 32, 96, 96]
        frames_array = np.stack(processed_frames[:self.mouth_crop_params['target_frames']])
        tensor = torch.from_numpy(frames_array).unsqueeze(0)  # Add channel dimension
        
        return tensor
    
    def process_single_video(self, input_path: str, output_path: str) -> dict:
        """Process single video with corrected ICU mouth-cropping"""
        
        video_name = Path(input_path).name
        print(f"\n🎬 Processing with ICU mouth-cropping: {video_name}")
        
        try:
            # Get original video info
            cap = cv2.VideoCapture(input_path)
            orig_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            orig_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            orig_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            orig_fps = cap.get(cv2.CAP_PROP_FPS)
            cap.release()
            
            print(f"   📊 Original: {orig_width}×{orig_height}, {orig_frames} frames, {orig_fps:.1f} FPS")
            
            # Process with ICU mouth-cropping
            processed_tensor = self.process_video_with_icu_cropping(input_path)
            
            print(f"   ✅ ICU processing successful")
            print(f"   📊 Processed tensor: {processed_tensor.shape}")
            print(f"   📊 Value range: [{processed_tensor.min():.3f}, {processed_tensor.max():.3f}]")
            print(f"   🎯 Mouth-cropping: ({self.mouth_crop_params['x']}, {self.mouth_crop_params['y']}) → {self.mouth_crop_params['width']}×{self.mouth_crop_params['height']} → 96×96")
            
            # Convert tensor to video file for inspection
            frames = processed_tensor.squeeze(0).numpy()  # [32, 96, 96]
            frames_uint8 = (frames * 255).astype(np.uint8)
            
            # Create video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = 10.0  # Slower FPS for inspection
            out = cv2.VideoWriter(output_path, fourcc, fps, (96, 96), isColor=False)
            
            # Write frames
            for frame in frames_uint8:
                out.write(frame)
            
            out.release()
            
            # Verify output
            cap_out = cv2.VideoCapture(output_path)
            out_width = int(cap_out.get(cv2.CAP_PROP_FRAME_WIDTH))
            out_height = int(cap_out.get(cv2.CAP_PROP_FRAME_HEIGHT))
            out_frames = int(cap_out.get(cv2.CAP_PROP_FRAME_COUNT))
            cap_out.release()
            
            print(f"   ✅ Saved: {Path(output_path).name} ({out_width}×{out_height}, {out_frames} frames)")
            
            return {
                'success': True,
                'input_path': input_path,
                'output_path': output_path,
                'original_specs': f"{orig_width}×{orig_height}, {orig_frames} frames",
                'processed_specs': f"{out_width}×{out_height}, {out_frames} frames",
                'tensor_shape': list(processed_tensor.shape),
                'value_range': [float(processed_tensor.min()), float(processed_tensor.max())],
                'mouth_crop_applied': f"({self.mouth_crop_params['x']}, {self.mouth_crop_params['y']}, {self.mouth_crop_params['width']}, {self.mouth_crop_params['height']})"
            }
            
        except Exception as e:
            print(f"   ❌ Processing failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'input_path': input_path,
                'output_path': output_path,
                'error': str(e)
            }
    
    def reprocess_all_videos(self) -> list:
        """Reprocess all videos with correct ICU mouth-cropping"""
        
        print(f"\n🔄 REPROCESSING WITH CORRECT ICU MOUTH-CROPPING")
        print("=" * 55)
        print(f"Using ICU reference training parameters:")
        print(f"   Source: 400×200 → Crop: (133, 0, 133, 100) → Resize: 96×96")
        print(f"   Target: 32 frames, grayscale, [0,1] normalization")
        
        results = []
        
        for i in range(1, 6):
            input_path = self.input_folder / f"{i}.webm"
            output_path = self.output_folder / f"processed_{i}.mp4"
            
            if input_path.exists():
                result = self.process_single_video(str(input_path), str(output_path))
                results.append(result)
            else:
                print(f"\n❌ Video not found: {i}.webm")
                results.append({
                    'success': False,
                    'input_path': str(input_path),
                    'output_path': str(output_path),
                    'error': 'Input file not found'
                })
        
        return results
    
    def create_corrected_verification_report(self, results: list):
        """Create verification report for corrected processing"""
        
        print(f"\n📊 CORRECTED ICU MOUTH-CROPPING VERIFICATION")
        print("=" * 50)
        
        successful_results = [r for r in results if r['success']]
        
        print(f"📈 Corrected Processing Summary:")
        print(f"   Total videos: {len(results)}")
        print(f"   Successfully reprocessed: {len(successful_results)}")
        print(f"   Success rate: {len(successful_results)/len(results)*100:.1f}%")
        
        if successful_results:
            print(f"\n📋 ICU Mouth-Cropping Verification:")
            print(f"{'Video':<12} {'Original':<20} {'Processed':<15} {'Mouth Crop':<20} {'Status'}")
            print(f"{'-'*12} {'-'*20} {'-'*15} {'-'*20} {'-'*10}")
            
            for result in successful_results:
                print(f"{Path(result['input_path']).name:<12} "
                      f"{result['original_specs']:<20} "
                      f"{result['processed_specs']:<15} "
                      f"{result['mouth_crop_applied']:<20} "
                      f"✅ SUCCESS")
        
        print(f"\n🎯 ICU Reference Training Compliance:")
        if successful_results:
            all_correct_shape = all(r['tensor_shape'] == [1, 32, 96, 96] for r in successful_results)
            all_correct_range = all(0 <= r['value_range'][0] and r['value_range'][1] <= 1 for r in successful_results)
            all_correct_crop = all('(133, 0, 133, 100)' in r['mouth_crop_applied'] for r in successful_results)
            
            print(f"   ✅ ICU mouth-cropping (133, 0, 133, 100): {'APPLIED' if all_correct_crop else 'FAILED'}")
            print(f"   ✅ Tensor shape [1, 32, 96, 96]: {'PASSED' if all_correct_shape else 'FAILED'}")
            print(f"   ✅ Value range [0, 1]: {'PASSED' if all_correct_range else 'FAILED'}")
            print(f"   ✅ Frame extraction (32 frames): APPLIED")
            print(f"   ✅ Grayscale conversion: APPLIED")
            print(f"   ✅ 96×96 resize: APPLIED")
        
        print(f"\n📁 Corrected Output Files:")
        for result in successful_results:
            print(f"   📄 {result['output_path']}")
        
        print(f"\n🔍 Visual Inspection - Corrected Mouth-Cropping:")
        print("=" * 50)
        print(f"1. Videos now use exact ICU reference training coordinates")
        print(f"2. Mouth region should be properly centered and focused")
        print(f"3. Compare with reference training videos for consistency")
        print(f"4. Verify different lip movements are now clearly visible")
        print(f"5. Check if 'I Feel Anxious' predictions are still consistent")

def main():
    """Main function to reprocess with correct ICU mouth-cropping"""
    
    print("🔄 ICU Reference Training Mouth-Cropping Correction")
    print("=" * 60)
    print("Reprocessing with exact coordinates: (133, 0, 133, 100)")
    
    # Folder paths
    input_folder = "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on"
    output_folder = "/Users/<USER>/Desktop/processed_perfect_10_videos"
    
    # Initialize corrected processor
    processor = CorrectedICUVideoProcessor(input_folder, output_folder)
    
    # Reprocess all videos with correct mouth-cropping
    results = processor.reprocess_all_videos()
    
    # Create verification report
    processor.create_corrected_verification_report(results)
    
    # Open output folder
    try:
        os.system(f'open "{output_folder}"')
        print(f"\n✅ Corrected videos folder opened for inspection")
    except Exception as e:
        print(f"\n⚠️  Could not auto-open folder: {e}")
    
    print(f"\n🎉 ICU Mouth-Cropping Correction Complete!")
    print("=" * 50)
    print("✅ Videos reprocessed with exact ICU reference coordinates")
    print("✅ Mouth-cropping now matches Perfect 10 training pipeline")
    print("✅ Ready to investigate corrected preprocessing results")

if __name__ == '__main__':
    main()
