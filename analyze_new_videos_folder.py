#!/usr/bin/env python3
"""
Analyzer for new test videos in 'new videos 14.8.25 to test the model on' folder
Tests Perfect 10 classifier on unseen data with potentially different speakers
"""

import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import sys
import json
import os

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class NewVideosAnalyzer:
    """Analyzer for new test videos folder"""
    
    def __init__(self, model_path: str, videos_folder: str):
        """Initialize the analyzer"""
        
        self.model_path = Path(model_path)
        self.videos_folder = Path(videos_folder)
        self.device = torch.device('cpu')
        
        # Perfect 10 phrases (in order)
        self.perfect_phrases = [
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today"
        ]
        
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        self.model = None
        self.video_processor = None
        
        print(f"🎯 New Videos Analyzer for Perfect 10 Classifier")
        print(f"   Model: {self.model_path}")
        print(f"   Videos folder: {self.videos_folder}")
    
    def check_videos_folder(self) -> list:
        """Check for videos in the specified folder"""
        
        print(f"\n📁 Checking Videos Folder")
        print("=" * 30)
        
        if not self.videos_folder.exists():
            print(f"❌ Folder not found: {self.videos_folder}")
            return []
        
        # Look for numbered video files
        video_files = []
        for i in range(1, 6):  # 1.webm through 5.webm
            video_path = self.videos_folder / f"{i}.webm"
            if video_path.exists():
                video_files.append(str(video_path))
                print(f"   ✅ Found: {i}.webm")
            else:
                print(f"   ❌ Missing: {i}.webm")
        
        # Also check for any other .webm files
        all_webm = list(self.videos_folder.glob("*.webm"))
        other_webm = [f for f in all_webm if f.name not in [f"{i}.webm" for i in range(1, 6)]]
        
        if other_webm:
            print(f"\n📹 Other .webm files found:")
            for video in other_webm:
                print(f"   📄 {video.name}")
        
        print(f"\n✅ Found {len(video_files)} target videos (1.webm - 5.webm)")
        return video_files
    
    def load_model(self) -> bool:
        """Load the Perfect 10 model"""
        
        print(f"\n🤖 Loading Perfect 10 Model")
        print("=" * 30)
        
        if not self.model_path.exists():
            print(f"❌ Model not found: {self.model_path}")
            return False
        
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device)
            self.model = Perfect10Mobile3DTiny(num_classes=10)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            best_val_accuracy = checkpoint.get('best_val_accuracy', 0.0)
            epoch = checkpoint.get('epoch', 0)
            
            print(f"✅ Perfect 10 model loaded successfully")
            print(f"   Parameters: {self.model.get_num_parameters():,}")
            print(f"   Training epoch: {epoch}")
            print(f"   Best validation accuracy: {best_val_accuracy:.1%}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def initialize_video_processor(self):
        """Initialize video processor with training settings"""
        
        self.video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True
        )
        
        print(f"✅ Video processor initialized")
        print(f"   Target frames: 32")
        print(f"   Target size: 96×96")
        print(f"   Grayscale: True")
        print(f"   Normalization: [-1, 1] range")
    
    def analyze_single_video(self, video_path: str) -> dict:
        """Analyze a single video"""
        
        video_name = Path(video_path).name
        print(f"\n🎬 Analyzing: {video_name}")
        
        try:
            # Process video through identical training pipeline
            video_tensor = self.video_processor.process_video(video_path)
            
            print(f"   ✅ Video processed successfully")
            print(f"   📊 Shape: {video_tensor.shape}")
            print(f"   📊 Value range: [{video_tensor.min():.3f}, {video_tensor.max():.3f}]")
            
            # Get prediction
            video_batch = video_tensor.unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                outputs = self.model(video_batch)
                probabilities = F.softmax(outputs, dim=1)
                
                # Get top-3 predictions
                top3_probs, top3_indices = torch.topk(probabilities, 3, dim=1)
                
                top3_probs = top3_probs[0].cpu().numpy()
                top3_indices = top3_indices[0].cpu().numpy()
                top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
                
                # Determine confidence level
                top_confidence = float(top3_probs[0])
                if top_confidence >= 0.7:
                    confidence_level = "Very High"
                    confidence_emoji = "🟢"
                elif top_confidence >= 0.5:
                    confidence_level = "Moderate"
                    confidence_emoji = "🟡"
                elif top_confidence >= 0.3:
                    confidence_level = "Low"
                    confidence_emoji = "🟠"
                else:
                    confidence_level = "Very Low"
                    confidence_emoji = "🔴"
                
                result = {
                    'success': True,
                    'video_name': video_name,
                    'video_path': video_path,
                    'video_shape': list(video_tensor.shape),
                    'value_range': [float(video_tensor.min()), float(video_tensor.max())],
                    'top_prediction': top3_phrases[0],
                    'top_confidence': top_confidence,
                    'confidence_level': confidence_level,
                    'confidence_emoji': confidence_emoji,
                    'top3_phrases': top3_phrases,
                    'top3_probabilities': top3_probs.tolist(),
                    'all_probabilities': probabilities[0].cpu().numpy().tolist(),
                    'raw_outputs': outputs[0].cpu().numpy().tolist()
                }
                
                print(f"   🏆 Top Prediction: {top3_phrases[0].title()}")
                print(f"   📊 Confidence: {top_confidence:.1%} ({confidence_emoji} {confidence_level})")
                print(f"   🥈 2nd Choice: {top3_phrases[1].title()} ({top3_probs[1]:.1%})")
                print(f"   🥉 3rd Choice: {top3_phrases[2].title()} ({top3_probs[2]:.1%})")
                
                return result
                
        except Exception as e:
            print(f"   ❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'video_name': video_name,
                'video_path': video_path,
                'error': str(e)
            }
    
    def analyze_all_videos(self, video_paths: list) -> list:
        """Analyze all videos in the folder"""
        
        print(f"\n🎬 Analyzing {len(video_paths)} New Test Videos")
        print("=" * 45)
        
        results = []
        for i, video_path in enumerate(video_paths, 1):
            print(f"\n📹 Video {i}/{len(video_paths)}")
            result = self.analyze_single_video(video_path)
            results.append(result)
        
        return results
    
    def create_summary_table(self, results: list):
        """Create and display comprehensive summary table"""
        
        print(f"\n📊 NEW VIDEOS ANALYSIS SUMMARY TABLE")
        print("=" * 55)
        
        # Header
        print(f"{'Video':<10} {'Top Prediction':<25} {'Confidence':<12} {'Level':<15} {'2nd Choice':<20}")
        print(f"{'-'*10} {'-'*25} {'-'*12} {'-'*15} {'-'*20}")
        
        # Results
        successful_results = [r for r in results if r['success']]
        for result in successful_results:
            emoji = result['confidence_emoji']
            second_choice = result['top3_phrases'][1] if len(result['top3_phrases']) > 1 else 'N/A'
            
            print(f"{result['video_name']:<10} {result['top_prediction'].title():<25} "
                  f"{result['top_confidence']:.1%}{'':>7} {emoji} {result['confidence_level']:<12} "
                  f"{second_choice.title():<20}")
        
        # Failed analyses
        failed_results = [r for r in results if not r['success']]
        for result in failed_results:
            print(f"{result['video_name']:<10} {'FAILED':<25} {'N/A':<12} {'N/A':<15} {'N/A':<20}")
    
    def display_detailed_results(self, results: list):
        """Display detailed results for each video"""
        
        print(f"\n📋 DETAILED ANALYSIS RESULTS")
        print("=" * 35)
        
        successful_results = [r for r in results if r['success']]
        
        for result in successful_results:
            print(f"\n🎬 **{result['video_name'].upper()}**")
            print(f"   🏆 Top: {result['top_prediction'].title()} ({result['top_confidence']:.1%}) - {result['confidence_emoji']} {result['confidence_level']}")
            print(f"   🥈 2nd: {result['top3_phrases'][1].title()} ({result['top3_probabilities'][1]:.1%})")
            print(f"   🥉 3rd: {result['top3_phrases'][2].title()} ({result['top3_probabilities'][2]:.1%})")
            print(f"   🔧 Technical: Shape {result['video_shape']}, Range [{result['value_range'][0]:.3f}, {result['value_range'][1]:.3f}]")
    
    def calculate_statistics(self, results: list):
        """Calculate and display analysis statistics"""
        
        successful_results = [r for r in results if r['success']]
        
        if not successful_results:
            print(f"\n❌ No successful analyses to calculate statistics")
            return
        
        print(f"\n📈 ANALYSIS STATISTICS")
        print("=" * 25)
        
        # Basic stats
        confidences = [r['top_confidence'] for r in successful_results]
        print(f"   Total videos: {len(results)}")
        print(f"   Successful analyses: {len(successful_results)}")
        print(f"   Success rate: {len(successful_results)/len(results):.1%}")
        
        # Confidence stats
        print(f"   Average confidence: {np.mean(confidences):.1%}")
        print(f"   Highest confidence: {np.max(confidences):.1%}")
        print(f"   Lowest confidence: {np.min(confidences):.1%}")
        print(f"   Confidence std dev: {np.std(confidences):.1%}")
        
        # Confidence distribution
        levels = [r['confidence_level'] for r in successful_results]
        level_counts = {level: levels.count(level) for level in set(levels)}
        print(f"   Confidence distribution: {level_counts}")
        
        # Phrase categories
        predicted_phrases = [r['top_prediction'] for r in successful_results]
        unique_phrases = list(set(predicted_phrases))
        print(f"   Unique phrases predicted: {len(unique_phrases)}")
        print(f"   Phrases: {[p.title() for p in unique_phrases]}")

def main():
    """Main function to analyze new videos folder"""
    
    print("🎬 Perfect 10 New Videos Folder Analyzer")
    print("=" * 50)
    print("Testing classifier on unseen data with new speakers")
    
    # Configuration
    model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
    videos_folder = "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on"
    
    # Initialize analyzer
    analyzer = NewVideosAnalyzer(model_path, videos_folder)
    
    # Check for videos
    video_paths = analyzer.check_videos_folder()
    if not video_paths:
        print(f"\n❌ No videos found in folder")
        return
    
    # Load model
    if not analyzer.load_model():
        print(f"❌ Failed to load Perfect 10 model")
        return
    
    # Initialize video processor
    analyzer.initialize_video_processor()
    
    # Analyze all videos
    results = analyzer.analyze_all_videos(video_paths)
    
    # Display results
    analyzer.create_summary_table(results)
    analyzer.display_detailed_results(results)
    analyzer.calculate_statistics(results)
    
    # Save results
    output_file = "new_videos_analysis_results.json"
    analysis_data = {
        'folder_path': videos_folder,
        'model_path': model_path,
        'analysis_timestamp': str(Path().cwd()),
        'perfect_phrases': analyzer.perfect_phrases,
        'results': results
    }
    
    with open(output_file, 'w') as f:
        json.dump(analysis_data, f, indent=2)
    
    print(f"\n💾 Results saved: {output_file}")
    print(f"\n🎉 New Videos Analysis Complete!")
    print("=" * 35)
    print(f"✅ {len([r for r in results if r['success']])}/{len(results)} videos analyzed successfully")
    print(f"✅ Perfect 10 classifier tested on unseen data")

if __name__ == '__main__':
    main()
