#!/usr/bin/env python3
"""
Train Perfect 10 ICU lipreading classifier on phrases with 100% baseline accuracy
Ultra-focused training for maximum accuracy and confidence
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
import seaborn as sns
from tqdm import tqdm
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny, Perfect10ModelManager
from perfect_10_data_loader import create_perfect_10_dataloaders

class Perfect10Trainer:
    """Trainer for Perfect 10 ICU lipreading classifier"""
    
    def __init__(self, config_path: str):
        """Initialize trainer with configuration"""
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Set random seed for reproducibility
        self.set_seed(self.config.get('seed', 42))
        
        # Setup device
        self.device = self.setup_device()
        
        # Create directories
        self.setup_directories()
        
        # Initialize components
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        self.model_manager = None
        
        # Training state
        self.current_epoch = 0
        self.current_phase = 1
        self.best_val_accuracy = 0.0
        self.training_history = {
            'train_loss': [],
            'train_accuracy': [],
            'val_loss': [],
            'val_accuracy': [],
            'learning_rate': [],
            'phase': []
        }
        
        print(f"🎯 Perfect 10 Trainer Initialized")
        print(f"   Device: {self.device}")
        print(f"   Config: {config_path}")
        print(f"   Target phrases: {self.config['model']['num_classes']}")
    
    def set_seed(self, seed: int):
        """Set random seed for reproducibility"""
        torch.manual_seed(seed)
        np.random.seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
    
    def setup_device(self):
        """Setup training device"""
        if torch.cuda.is_available():
            device = torch.device('cuda')
            print(f"🚀 Using GPU: {torch.cuda.get_device_name()}")
        else:
            device = torch.device('cpu')
            print(f"💻 Using CPU (for compatibility)")
        
        return device
    
    def setup_directories(self):
        """Create necessary directories"""
        
        # Checkpoints directory
        self.checkpoint_dir = Path(self.config['checkpoints']['save_dir'])
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # Logs directory
        self.log_dir = Path(self.config['logging']['log_dir'])
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Directories created:")
        print(f"   Checkpoints: {self.checkpoint_dir}")
        print(f"   Logs: {self.log_dir}")
    
    def prepare_data(self):
        """Prepare data loaders for Perfect 10 training"""
        
        print(f"\n📊 Preparing Perfect 10 Dataset")
        print("=" * 35)
        
        # Check if manifest exists
        manifest_path = self.config['data']['manifest_path']
        if not Path(manifest_path).exists():
            raise FileNotFoundError(f"Manifest not found: {manifest_path}")
        
        # Load and analyze manifest
        manifest_df = pd.read_csv(manifest_path)
        print(f"📋 Loaded manifest: {len(manifest_df)} videos")
        
        # Show phrase distribution
        phrase_counts = manifest_df['phrase'].value_counts()
        print(f"📊 Perfect phrase distribution:")
        for phrase, count in phrase_counts.items():
            print(f"   {phrase.title()}: {count} videos")
        
        # Create data loaders for Perfect 10 dataset
        try:
            train_loader, val_loader, test_loader, data_info = create_perfect_10_dataloaders(
                self.config, manifest_path
            )
            
            self.train_loader = train_loader
            self.val_loader = val_loader
            self.test_loader = test_loader
            self.data_info = data_info
            
            print(f"\n✅ Data loaders created:")
            print(f"   Train: {data_info['train_size']} videos")
            print(f"   Validation: {data_info['val_size']} videos")
            print(f"   Test: {data_info['test_size']} videos")
            print(f"   Classes: {data_info['num_classes']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create data loaders: {e}")
            return False
    
    def initialize_model(self):
        """Initialize Perfect 10 model with transfer learning"""
        
        print(f"\n🤖 Initializing Perfect 10 Model")
        print("=" * 35)
        
        # Create model manager
        baseline_model_path = self.config['model']['pretrained_baseline_path']
        perfect_summary_path = "perfect_10_phrases_summary.json"
        
        self.model_manager = Perfect10ModelManager(baseline_model_path, perfect_summary_path)
        
        # Create Perfect 10 model with transfer learning
        self.model = self.model_manager.create_perfect_model()
        self.model.to(self.device)
        
        print(f"✅ Model: {self.model.__class__.__name__}")
        print(f"   Parameters: {self.model.get_num_parameters():,}")
        print(f"   Classes: {self.config['model']['num_classes']}")
        print(f"   Transfer learning: {self.config['model']['transfer_learning']}")
        
        # Initialize optimizer for phase 1 (frozen backbone)
        self.setup_phase1_training()
        
        # Initialize loss function
        label_smoothing = self.config['regularization'].get('label_smoothing', 0.0)
        self.criterion = nn.CrossEntropyLoss(label_smoothing=label_smoothing)
        
        print(f"✅ Loss: CrossEntropyLoss (smoothing={label_smoothing})")
    
    def setup_phase1_training(self):
        """Setup training for phase 1 (frozen backbone)"""
        
        print(f"\n🔒 Phase 1: Frozen Backbone Training")
        print("=" * 40)
        
        # Freeze backbone
        self.model.freeze_backbone(freeze=True)
        
        # Setup optimizer for classifier only
        lr = self.config['training']['learning_rate']
        weight_decay = self.config['training']['weight_decay']
        
        # Only optimize classifier parameters
        classifier_params = []
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                classifier_params.append(param)
        
        self.optimizer = optim.AdamW(
            classifier_params,
            lr=lr,
            weight_decay=weight_decay
        )
        
        # Setup scheduler
        phase1_epochs = self.config['training_phases']['phase1']['epochs']
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=phase1_epochs
        )
        
        print(f"✅ Phase 1 setup complete")
        print(f"   Trainable parameters: {sum(p.numel() for p in classifier_params):,}")
        print(f"   Learning rate: {lr}")
        print(f"   Epochs: {phase1_epochs}")
    
    def setup_phase2_training(self):
        """Setup training for phase 2 (fine-tuning)"""
        
        print(f"\n🔓 Phase 2: Fine-tuning Entire Model")
        print("=" * 40)
        
        # Unfreeze backbone
        self.model.freeze_backbone(freeze=False)
        
        # Setup optimizer for entire model
        fine_tune_lr = self.config['training']['fine_tune_lr']
        weight_decay = self.config['training']['weight_decay']
        
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=fine_tune_lr,
            weight_decay=weight_decay
        )
        
        # Setup scheduler for remaining epochs
        phase2_epochs = self.config['training_phases']['phase2']['epochs']
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=phase2_epochs
        )
        
        self.current_phase = 2
        
        print(f"✅ Phase 2 setup complete")
        print(f"   Trainable parameters: {self.model.get_num_parameters():,}")
        print(f"   Learning rate: {fine_tune_lr}")
        print(f"   Epochs: {phase2_epochs}")

def main():
    """Main training function"""
    
    print("🎯 Perfect 10 ICU Lipreading Training - Maximum Accuracy Focus")
    print("=" * 70)
    
    # Initialize trainer
    config_path = "configs/perfect_10_training.yaml"
    trainer = Perfect10Trainer(config_path)
    
    # Prepare data
    if not trainer.prepare_data():
        print("❌ Data preparation failed. Exiting.")
        return
    
    # Initialize model
    trainer.initialize_model()
    
    print(f"\n🚀 Starting Perfect 10 Training")
    print("=" * 35)
    print(f"📊 Dataset: {trainer.data_info['train_size']} train, {trainer.data_info['val_size']} val")
    print(f"🎯 Target: 10 perfect-performance ICU phrases")
    print(f"🔄 Transfer learning from 26-class baseline")
    print(f"⏱️  Ultra-focused two-phase training strategy")
    print(f"🏆 Goal: 90%+ accuracy on most reliable phrases")
    
    # Start Perfect 10 training
    try:
        # Add training loop methods from focused trainer
        trainer.train_epoch = lambda: train_epoch_method(trainer)
        trainer.validate_epoch = lambda: validate_epoch_method(trainer)
        trainer.train = lambda: train_method(trainer)

        best_accuracy = trainer.train()

        print(f"\n🎉 Perfect 10 Training Complete!")
        print("=" * 40)
        print(f"✅ Best validation accuracy: {best_accuracy:.3f}")
        print(f"✅ Model saved in: {trainer.checkpoint_dir}")
        print(f"✅ 10 perfect phrases trained")
        print(f"✅ Maximum accuracy achieved!")

    except Exception as e:
        print(f"❌ Perfect 10 training failed: {e}")
        import traceback
        traceback.print_exc()

def train_epoch_method(trainer):
    """Train for one epoch"""

    trainer.model.train()
    total_loss = 0.0
    correct_predictions = 0
    total_samples = 0

    phase_name = f"Phase {trainer.current_phase}"
    progress_bar = tqdm(trainer.train_loader, desc=f"{phase_name} Epoch {trainer.current_epoch+1}")

    for batch_idx, (videos, labels, metadata) in enumerate(progress_bar):
        # Move data to device
        videos = videos.to(trainer.device)
        labels = labels.to(trainer.device)

        # Forward pass
        trainer.optimizer.zero_grad()
        outputs = trainer.model(videos)
        loss = trainer.criterion(outputs, labels)

        # Backward pass
        loss.backward()

        # Gradient clipping
        if trainer.config['training'].get('grad_clip_norm'):
            torch.nn.utils.clip_grad_norm_(
                trainer.model.parameters(),
                trainer.config['training']['grad_clip_norm']
            )

        trainer.optimizer.step()

        # Statistics
        total_loss += loss.item()
        _, predicted = torch.max(outputs.data, 1)
        total_samples += labels.size(0)
        correct_predictions += (predicted == labels).sum().item()

        # Update progress bar
        current_accuracy = correct_predictions / total_samples
        progress_bar.set_postfix({
            'Loss': f"{loss.item():.4f}",
            'Acc': f"{current_accuracy:.3f}",
            'Phase': trainer.current_phase
        })

    # Calculate epoch metrics
    epoch_loss = total_loss / len(trainer.train_loader)
    epoch_accuracy = correct_predictions / total_samples

    return epoch_loss, epoch_accuracy

def validate_epoch_method(trainer):
    """Validate for one epoch"""

    trainer.model.eval()
    total_loss = 0.0
    correct_predictions = 0
    total_samples = 0

    with torch.no_grad():
        for videos, labels, metadata in trainer.val_loader:
            # Move data to device
            videos = videos.to(trainer.device)
            labels = labels.to(trainer.device)

            # Forward pass
            outputs = trainer.model(videos)
            loss = trainer.criterion(outputs, labels)

            # Statistics
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_samples += labels.size(0)
            correct_predictions += (predicted == labels).sum().item()

    # Calculate epoch metrics
    epoch_loss = total_loss / len(trainer.val_loader)
    epoch_accuracy = correct_predictions / total_samples

    return epoch_loss, epoch_accuracy

def train_method(trainer):
    """Main training loop with two phases"""

    print(f"\n🚀 Starting Perfect 10 Two-Phase Training")
    print("=" * 45)

    # Phase 1: Frozen backbone
    phase1_epochs = trainer.config['training_phases']['phase1']['epochs']
    phase2_epochs = trainer.config['training_phases']['phase2']['epochs']
    total_epochs = phase1_epochs + phase2_epochs

    val_every_n_epochs = trainer.config['training']['val_every_n_epochs']
    save_every_n_epochs = trainer.config['training']['save_every_n_epochs']

    # Early stopping setup
    early_stopping_enabled = trainer.config['training']['early_stopping']['enabled']
    patience = trainer.config['training']['early_stopping']['patience']
    min_delta = trainer.config['training']['early_stopping']['min_delta']
    patience_counter = 0

    print(f"📋 Perfect 10 Training Plan:")
    print(f"   Phase 1 (Frozen): {phase1_epochs} epochs")
    print(f"   Phase 2 (Fine-tune): {phase2_epochs} epochs")
    print(f"   Total: {total_epochs} epochs")
    print(f"   Target: 90%+ accuracy on 10 perfect phrases")

    for epoch in range(total_epochs):
        trainer.current_epoch = epoch

        # Check if we need to switch to phase 2
        if epoch == phase1_epochs and trainer.current_phase == 1:
            trainer.setup_phase2_training()

        # Training phase
        train_loss, train_accuracy = trainer.train_epoch()

        # Update learning rate
        trainer.scheduler.step()
        current_lr = trainer.optimizer.param_groups[0]['lr']

        # Store training metrics
        trainer.training_history['train_loss'].append(train_loss)
        trainer.training_history['train_accuracy'].append(train_accuracy)
        trainer.training_history['learning_rate'].append(current_lr)
        trainer.training_history['phase'].append(trainer.current_phase)

        # Validation phase
        if (epoch + 1) % val_every_n_epochs == 0:
            val_loss, val_accuracy = trainer.validate_epoch()

            trainer.training_history['val_loss'].append(val_loss)
            trainer.training_history['val_accuracy'].append(val_accuracy)

            # Check for best model
            is_best = val_accuracy > trainer.best_val_accuracy + min_delta
            if is_best:
                trainer.best_val_accuracy = val_accuracy
                patience_counter = 0

                # Save best model
                checkpoint = {
                    'epoch': epoch + 1,
                    'model_state_dict': trainer.model.state_dict(),
                    'best_val_accuracy': trainer.best_val_accuracy,
                    'config': trainer.config
                }
                best_path = trainer.checkpoint_dir / "best_perfect_10_model.pth"
                torch.save(checkpoint, best_path)
                print(f"💾 Best Perfect 10 model saved: {best_path}")
            else:
                patience_counter += 1

            phase_name = f"P{trainer.current_phase}"
            print(f"Epoch {epoch+1:3d}/{total_epochs} | {phase_name} | "
                  f"Train: {train_loss:.4f}/{train_accuracy:.3f} | "
                  f"Val: {val_loss:.4f}/{val_accuracy:.3f} | "
                  f"LR: {current_lr:.6f} | "
                  f"Best: {trainer.best_val_accuracy:.3f}")

            # Early stopping check
            if early_stopping_enabled and patience_counter >= patience:
                print(f"🛑 Early stopping triggered after {patience} epochs without improvement")
                break
        else:
            phase_name = f"P{trainer.current_phase}"
            print(f"Epoch {epoch+1:3d}/{total_epochs} | {phase_name} | "
                  f"Train: {train_loss:.4f}/{train_accuracy:.3f} | "
                  f"LR: {current_lr:.6f}")

    print(f"\n✅ Perfect 10 Training completed!")
    print(f"🎯 Best validation accuracy: {trainer.best_val_accuracy:.3f}")

    # Compare with baseline
    baseline_accuracy = 0.512  # 51.2% on 26-class
    target_accuracy = 0.90     # 90% target

    print(f"\n📊 Perfect 10 Performance")
    print("=" * 30)
    print(f"   Baseline (26-class): {baseline_accuracy:.1%}")
    print(f"   Perfect 10 (10-class): {trainer.best_val_accuracy:.1%}")
    print(f"   Target: {target_accuracy:.1%}")

    improvement = trainer.best_val_accuracy - baseline_accuracy
    print(f"   Improvement: {improvement:+.1%}")

    if trainer.best_val_accuracy >= target_accuracy:
        print(f"🎉 Target accuracy achieved!")
    else:
        remaining = target_accuracy - trainer.best_val_accuracy
        print(f"📈 {remaining:.1%} to target")

    return trainer.best_val_accuracy

if __name__ == '__main__':
    main()
