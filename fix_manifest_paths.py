#!/usr/bin/env python3
"""
Fix the manifest paths to work with the training pipeline
"""

import pandas as pd
from pathlib import Path

def fix_manifest_paths():
    """Fix the video paths in the manifest"""
    
    print("🔧 Fixing Manifest Paths")
    print("=" * 25)
    
    # Load the manifest
    manifest_path = "/Users/<USER>/Desktop/reference videos for training/reference_videos_manifest.csv"
    manifest_df = pd.read_csv(manifest_path)
    
    print(f"📊 Loaded manifest: {len(manifest_df)} videos")
    
    # Show current paths
    print(f"📋 Current path format:")
    print(f"   Example: {manifest_df.iloc[0]['video_path']}")
    
    # Fix the paths to be absolute
    fixed_paths = []
    for _, row in manifest_df.iterrows():
        current_path = row['video_path']
        
        # Extract just the filename
        filename = Path(current_path).name
        
        # Create absolute path
        absolute_path = f"/Users/<USER>/Desktop/reference videos for training/{filename}"
        fixed_paths.append(absolute_path)
    
    # Update the manifest
    manifest_df['video_path'] = fixed_paths
    
    # Save the fixed manifest
    fixed_manifest_path = "reference_videos_manifest_fixed.csv"
    manifest_df.to_csv(fixed_manifest_path, index=False)
    
    print(f"✅ Fixed manifest saved: {fixed_manifest_path}")
    print(f"📋 New path format:")
    print(f"   Example: {manifest_df.iloc[0]['video_path']}")
    
    # Verify files exist
    missing_files = []
    for path in fixed_paths:
        if not Path(path).exists():
            missing_files.append(path)
    
    if missing_files:
        print(f"⚠️  Missing files: {len(missing_files)}")
        for missing in missing_files[:5]:
            print(f"   {missing}")
    else:
        print(f"✅ All {len(fixed_paths)} video files found")
    
    return fixed_manifest_path

def update_config():
    """Update the training config to use the fixed manifest"""
    
    print(f"\n🔧 Updating Training Configuration")
    print("=" * 35)
    
    import yaml
    
    # Load config
    with open('configs/reference_training.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Update manifest path
    config['data']['manifest_path'] = "reference_videos_manifest_fixed.csv"
    config['data']['video_root'] = "/"  # Use absolute paths
    
    # Save updated config
    with open('configs/reference_training.yaml', 'w') as f:
        yaml.dump(config, f, default_flow_style=False, sort_keys=False)
    
    print(f"✅ Configuration updated")
    print(f"   Manifest: {config['data']['manifest_path']}")
    print(f"   Video root: {config['data']['video_root']}")

def main():
    """Main function"""
    
    # Fix manifest paths
    fixed_manifest = fix_manifest_paths()
    
    # Update config
    update_config()
    
    print(f"\n🎯 Ready for Training!")
    print("=" * 20)
    print("✅ Manifest paths fixed")
    print("✅ Configuration updated")
    print("✅ All video files verified")

if __name__ == '__main__':
    main()
