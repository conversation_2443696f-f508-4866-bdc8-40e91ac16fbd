#!/bin/bash

# Training script for lightweight VSR model
# Usage: ./scripts/run_train.sh [manifest_path] [output_dir]

set -e  # Exit on any error

# Default values
MANIFEST_PATH="${1:-data/manifest.csv}"
OUTPUT_DIR="${2:-artifacts/vsr_26p_v1}"
CONFIG_PATH="configs/phrases26.yaml"

# Check if virtual environment exists
if [ ! -d ".venv_vsr" ]; then
    echo "Creating virtual environment..."
    python -m venv .venv_vsr
fi

# Activate virtual environment
echo "Activating virtual environment..."
source .venv_vsr/bin/activate

# Install requirements
echo "Installing requirements..."
pip install -r backend/lightweight_vsr/requirements.txt

# Check if manifest exists
if [ ! -f "$MANIFEST_PATH" ]; then
    echo "Error: Manifest file not found: $MANIFEST_PATH"
    echo "Please create a manifest CSV with columns: video_path,speaker_id,phrase,age_group,gender,ethnicity,lighting"
    exit 1
fi

# Check if config exists
if [ ! -f "$CONFIG_PATH" ]; then
    echo "Error: Config file not found: $CONFIG_PATH"
    exit 1
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo "Starting training..."
echo "Manifest: $MANIFEST_PATH"
echo "Output: $OUTPUT_DIR"
echo "Config: $CONFIG_PATH"

# Run training
python backend/lightweight_vsr/train.py \
    --manifest "$MANIFEST_PATH" \
    --out_dir "$OUTPUT_DIR" \
    --config "$CONFIG_PATH" \
    --epochs 40 \
    --batch_size 16 \
    --val_split 0.1 \
    --test_split 0.1 \
    --num_workers 4 \
    --amp 1

echo "Training completed!"

# Export TorchScript model
echo "Exporting TorchScript model..."
./scripts/export_torchscript.sh "$OUTPUT_DIR"

echo "All done! Model saved to $OUTPUT_DIR"
