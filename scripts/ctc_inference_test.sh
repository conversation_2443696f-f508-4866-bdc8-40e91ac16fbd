#!/bin/bash

# End-to-end inference testing for CTC Visual Speech Recognition
# This script tests the complete inference pipeline with dummy data

set -e

echo "Running CTC VSR inference tests..."

# Check if models exist
PHONEME_MODEL="artifacts/models/phoneme_ctc_best.pt"
CHAR_MODEL="artifacts/models/char_ctc_best.pt"
LEXICON_FILE="artifacts/lexicon/icu_lexicon.json"

echo "Checking for required files..."

if [ ! -f "$PHONEME_MODEL" ]; then
    echo "⚠ Phoneme model not found: $PHONEME_MODEL"
    echo "  Run: bash scripts/ctc_train_phoneme.sh"
    PHONEME_AVAILABLE=false
else
    echo "✓ Phoneme model found: $PHONEME_MODEL"
    PHONEME_AVAILABLE=true
fi

if [ ! -f "$CHAR_MODEL" ]; then
    echo "⚠ Character model not found: $CHAR_MODEL"
    echo "  Run: bash scripts/ctc_train_char.sh"
    CHAR_AVAILABLE=false
else
    echo "✓ Character model found: $CHAR_MODEL"
    CHAR_AVAILABLE=true
fi

if [ ! -f "$LEXICON_FILE" ]; then
    echo "⚠ Lexicon not found: $LEXICON_FILE"
    echo "  Run: bash scripts/ctc_build_lexicon.sh"
    LEXICON_AVAILABLE=false
else
    echo "✓ Lexicon found: $LEXICON_FILE"
    LEXICON_AVAILABLE=true
fi

# Test 1: Basic inference initialization
echo ""
echo "Test 1: Testing inference initialization..."
python -c "
import sys
sys.path.append('.')
from backend.ctc_vsr.infer import CTCInference

# Initialize with no models (should not crash)
inference = CTCInference()
model_info = inference.get_model_info()
print(f'✓ Inference initialized: {model_info}')
"

# Test 2: Test with dummy video data
echo ""
echo "Test 2: Testing with dummy video data..."
python -c "
import sys
sys.path.append('.')
import numpy as np
from backend.ctc_vsr.infer import CTCInference

# Create dummy video data
dummy_video = np.random.randint(0, 255, (64, 112, 112, 1), dtype=np.uint8)

# Initialize inference
inference = CTCInference()

# Test preprocessing
try:
    video_tensor = inference._preprocess_video(dummy_video)
    expected_shape = (1, 1, 64, 112, 112)  # (batch, channels, frames, height, width)
    assert video_tensor.shape == expected_shape, f'Unexpected shape: {video_tensor.shape}'
    print(f'✓ Video preprocessing successful: {dummy_video.shape} -> {video_tensor.shape}')
except Exception as e:
    print(f'✗ Video preprocessing failed: {e}')
    sys.exit(1)
"

# Test 3: Test with models if available
if [ "$PHONEME_AVAILABLE" = true ] && [ "$LEXICON_AVAILABLE" = true ]; then
    echo ""
    echo "Test 3: Testing phoneme model inference..."
    python -c "
import sys
sys.path.append('.')
import numpy as np
from backend.ctc_vsr.infer import CTCInference

# Create dummy video
dummy_video = np.random.randint(0, 255, (64, 112, 112, 1), dtype=np.uint8)

# Initialize with phoneme model
inference = CTCInference(
    phoneme_model_path='$PHONEME_MODEL',
    lexicon_path='$LEXICON_FILE'
)

# Test ICU prediction
try:
    result = inference.predict_icu(dummy_video)
    print(f'✓ ICU prediction: {result}')
    assert 'success' in result, 'Missing success field'
    assert 'text' in result, 'Missing text field'
    assert 'confidence' in result, 'Missing confidence field'
    assert 'mode' in result, 'Missing mode field'
except Exception as e:
    print(f'✗ ICU prediction failed: {e}')
    sys.exit(1)
"
else
    echo ""
    echo "Test 3: Skipping phoneme model test (model or lexicon not available)"
fi

if [ "$CHAR_AVAILABLE" = true ]; then
    echo ""
    echo "Test 4: Testing character model inference..."
    python -c "
import sys
sys.path.append('.')
import numpy as np
from backend.ctc_vsr.infer import CTCInference

# Create dummy video
dummy_video = np.random.randint(0, 255, (64, 112, 112, 1), dtype=np.uint8)

# Initialize with character model
inference = CTCInference(char_model_path='$CHAR_MODEL')

# Test open prediction
try:
    result = inference.predict_open(dummy_video)
    print(f'✓ Open prediction: {result}')
    assert 'success' in result, 'Missing success field'
    assert 'text' in result, 'Missing text field'
    assert 'confidence' in result, 'Missing confidence field'
    assert 'mode' in result, 'Missing mode field'
except Exception as e:
    print(f'✗ Open prediction failed: {e}')
    sys.exit(1)
"
else
    echo ""
    echo "Test 4: Skipping character model test (model not available)"
fi

# Test 5: Test best mode selection
if [ "$PHONEME_AVAILABLE" = true ] && [ "$CHAR_AVAILABLE" = true ] && [ "$LEXICON_AVAILABLE" = true ]; then
    echo ""
    echo "Test 5: Testing best mode selection..."
    python -c "
import sys
sys.path.append('.')
import numpy as np
from backend.ctc_vsr.infer import CTCInference

# Create dummy video
dummy_video = np.random.randint(0, 255, (64, 112, 112, 1), dtype=np.uint8)

# Initialize with both models
inference = CTCInference(
    phoneme_model_path='$PHONEME_MODEL',
    char_model_path='$CHAR_MODEL',
    lexicon_path='$LEXICON_FILE'
)

# Test best prediction
try:
    result = inference.predict_best(dummy_video)
    print(f'✓ Best prediction: {result}')
    assert 'success' in result, 'Missing success field'
    assert 'text' in result, 'Missing text field'
    assert 'confidence' in result, 'Missing confidence field'
    assert 'selected_mode' in result, 'Missing selected_mode field'
except Exception as e:
    print(f'✗ Best prediction failed: {e}')
    sys.exit(1)
"
else
    echo ""
    echo "Test 5: Skipping best mode test (models not available)"
fi

# Test 6: Test API endpoints (if FastAPI available)
echo ""
echo "Test 6: Testing API endpoints..."
python -c "
import sys
sys.path.append('.')
try:
    from fastapi.testclient import TestClient
    from backend.ctc_vsr.api_router import router
    from fastapi import FastAPI
    
    # Create test app
    app = FastAPI()
    app.include_router(router)
    client = TestClient(app)
    
    # Test health endpoint
    response = client.get('/ctc/health')
    print(f'✓ Health endpoint: {response.status_code}')
    
    # Test info endpoint
    response = client.get('/ctc/info')
    print(f'✓ Info endpoint: {response.status_code}')
    
except ImportError:
    print('⚠ FastAPI not available, skipping API tests')
except Exception as e:
    print(f'⚠ API test failed: {e}')
"

# Test 7: Test with real video file (if available)
echo ""
echo "Test 7: Testing with real video file..."
TEST_VIDEO=""
for ext in mp4 avi mov; do
    if ls data/roi/*.$ext 1> /dev/null 2>&1; then
        TEST_VIDEO=$(ls data/roi/*.$ext | head -1)
        break
    fi
done

if [ -n "$TEST_VIDEO" ] && [ -f "$TEST_VIDEO" ]; then
    echo "Found test video: $TEST_VIDEO"
    
    if [ "$PHONEME_AVAILABLE" = true ] && [ "$LEXICON_AVAILABLE" = true ]; then
        python -c "
import sys
sys.path.append('.')
from backend.ctc_vsr.infer import CTCInference

# Initialize with phoneme model
inference = CTCInference(
    phoneme_model_path='$PHONEME_MODEL',
    lexicon_path='$LEXICON_FILE'
)

# Test with real video
try:
    result = inference.predict_icu('$TEST_VIDEO')
    print(f'✓ Real video ICU prediction: {result}')
except Exception as e:
    print(f'⚠ Real video test failed: {e}')
"
    else
        echo "⚠ Skipping real video test (models not available)"
    fi
else
    echo "⚠ No test video files found in data/roi/"
fi

echo ""
echo "🎉 Inference tests completed!"
echo ""

# Summary
echo "Summary:"
echo "- Phoneme model: $([ "$PHONEME_AVAILABLE" = true ] && echo "✓ Available" || echo "✗ Not available")"
echo "- Character model: $([ "$CHAR_AVAILABLE" = true ] && echo "✓ Available" || echo "✗ Not available")"
echo "- Lexicon: $([ "$LEXICON_AVAILABLE" = true ] && echo "✓ Available" || echo "✗ Not available")"
echo ""

if [ "$PHONEME_AVAILABLE" = true ] && [ "$CHAR_AVAILABLE" = true ] && [ "$LEXICON_AVAILABLE" = true ]; then
    echo "🚀 All components available! Ready for production use."
    echo ""
    echo "To start the API server:"
    echo "  export VSR_IMPL=ctc"
    echo "  uvicorn backend.api.app:app --host 0.0.0.0 --port 8000 --reload"
else
    echo "⚠ Some components missing. Complete setup:"
    [ "$LEXICON_AVAILABLE" = false ] && echo "  bash scripts/ctc_build_lexicon.sh"
    [ "$PHONEME_AVAILABLE" = false ] && echo "  bash scripts/ctc_train_phoneme.sh"
    [ "$CHAR_AVAILABLE" = false ] && echo "  bash scripts/ctc_train_char.sh"
fi
