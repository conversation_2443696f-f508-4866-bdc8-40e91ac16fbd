#!/bin/bash

# Smoke test for CTC Visual Speech Recognition pipeline
# This script performs basic functionality verification

set -e

echo "Running CTC VSR smoke tests..."

# Test 1: Check module imports
echo "Test 1: Checking module imports..."
python -c "
import sys
sys.path.append('.')
try:
    from backend.ctc_vsr import CTCInference
    from backend.ctc_vsr.tokens import get_phoneme_vocab, get_character_vocab
    from backend.ctc_vsr.visual_encoder import VisualEncoder
    print('✓ All imports successful')
except ImportError as e:
    print(f'✗ Import failed: {e}')
    sys.exit(1)
"

# Test 2: Check token vocabularies
echo "Test 2: Checking token vocabularies..."
python -c "
import sys
sys.path.append('.')
from backend.ctc_vsr.tokens import get_phoneme_vocab, get_character_vocab

# Test phoneme vocabulary
phoneme_tokens, phoneme_to_idx, idx_to_phoneme = get_phoneme_vocab()
assert len(phoneme_tokens) > 0, 'Phoneme vocabulary is empty'
assert phoneme_tokens[0] == '<blank>', 'Blank token not at index 0'
print(f'✓ Phoneme vocabulary: {len(phoneme_tokens)} tokens')

# Test character vocabulary
char_tokens, char_to_idx, idx_to_char = get_character_vocab()
assert len(char_tokens) > 0, 'Character vocabulary is empty'
assert char_tokens[0] == '<blank>', 'Blank token not at index 0'
print(f'✓ Character vocabulary: {len(char_tokens)} tokens')
"

# Test 3: Check visual encoder
echo "Test 3: Testing visual encoder..."
python -c "
import sys
sys.path.append('.')
import torch
from backend.ctc_vsr.visual_encoder import VisualEncoder

# Create encoder
encoder = VisualEncoder(feat_dim=128, pretrained=False)
print(f'✓ Visual encoder created with {sum(p.numel() for p in encoder.parameters()):,} parameters')

# Test forward pass
batch_size, channels, frames, height, width = 2, 1, 64, 112, 112
x = torch.randn(batch_size, channels, frames, height, width)

with torch.no_grad():
    features = encoder(x)
    expected_seq_len = encoder.get_sequence_length(frames)
    assert features.shape == (batch_size, expected_seq_len, 128), f'Unexpected output shape: {features.shape}'
    print(f'✓ Forward pass successful: {x.shape} -> {features.shape}')
"

# Test 4: Check configuration loading
echo "Test 4: Testing configuration loading..."
python -c "
import sys
sys.path.append('.')
import yaml
from pathlib import Path

config_path = Path('backend/ctc_vsr/config.yaml')
if config_path.exists():
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    required_sections = ['video', 'model', 'training', 'confidence', 'icu_vocabulary']
    for section in required_sections:
        assert section in config, f'Missing config section: {section}'
    
    print(f'✓ Configuration loaded with {len(config[\"icu_vocabulary\"])} ICU vocabulary words')
else:
    print('✗ Configuration file not found')
    sys.exit(1)
"

# Test 5: Check lexicon builder (if g2p-en available)
echo "Test 5: Testing lexicon builder..."
python -c "
import sys
sys.path.append('.')
try:
    from backend.ctc_vsr.build_lexicon import LexiconBuilder
    from backend.ctc_vsr.tokens import get_phoneme_vocab
    
    # Test with small vocabulary
    test_words = ['PAIN', 'HELP', 'NURSE']
    
    # Create temporary config
    import tempfile, yaml
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        config = {'icu_vocabulary': test_words}
        yaml.dump(config, f)
        temp_config = f.name
    
    try:
        builder = LexiconBuilder(temp_config)
        lexicon = builder.build_lexicon()
        assert len(lexicon) > 0, 'No lexicon entries created'
        print(f'✓ Lexicon builder created {len(lexicon)} entries')
    finally:
        import os
        os.unlink(temp_config)
        
except ImportError:
    print('⚠ g2p-en not available, skipping lexicon test')
"

# Test 6: Check dataset loader
echo "Test 6: Testing dataset loader..."
python -c "
import sys
sys.path.append('.')
import yaml
from pathlib import Path
from backend.ctc_vsr.dataset import VideoDataset

# Load config
config_path = Path('backend/ctc_vsr/config.yaml')
with open(config_path, 'r') as f:
    config = yaml.safe_load(f)

# Test with empty manifest (should not crash)
import tempfile
with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
    temp_manifest = f.name

try:
    dataset = VideoDataset(temp_manifest, config, mode='train', target_type='phoneme')
    assert len(dataset) == 0, 'Dataset should be empty with empty manifest'
    print('✓ Dataset loader handles empty manifest correctly')
finally:
    import os
    os.unlink(temp_manifest)
"

# Test 7: Check metrics
echo "Test 7: Testing evaluation metrics..."
python -c "
import sys
sys.path.append('.')
from backend.ctc_vsr.metrics import calculate_cer, calculate_wer, evaluate_predictions

# Test CER calculation
cer = calculate_cer('HELLO', 'HELO')
assert 0 <= cer <= 1, f'Invalid CER: {cer}'
print(f'✓ CER calculation: {cer:.3f}')

# Test WER calculation
wer = calculate_wer('HELLO WORLD', 'HELLO WORD')
assert 0 <= wer <= 1, f'Invalid WER: {wer}'
print(f'✓ WER calculation: {wer:.3f}')

# Test comprehensive evaluation
results = evaluate_predictions(['HELLO'], ['HELLO'], [0.9])
assert 'mean_cer' in results, 'Missing CER in results'
assert 'mean_wer' in results, 'Missing WER in results'
print('✓ Comprehensive evaluation successful')
"

# Test 8: Check API router (basic import)
echo "Test 8: Testing API router..."
python -c "
import sys
sys.path.append('.')
try:
    from backend.ctc_vsr.api_router import router
    print('✓ API router imported successfully')
except ImportError as e:
    print(f'⚠ API router import failed (may need FastAPI): {e}')
"

echo ""
echo "🎉 All smoke tests completed successfully!"
echo ""
echo "Next steps:"
echo "1. Build lexicon: bash scripts/ctc_build_lexicon.sh"
echo "2. Train models: bash scripts/ctc_train_phoneme.sh && bash scripts/ctc_train_char.sh"
echo "3. Run inference tests: bash scripts/ctc_inference_test.sh"
