#!/bin/bash

# Train character CTC model for open vocabulary Visual Speech Recognition
# This script trains the character-level CTC model with open vocabulary decoding

set -e

echo "Training character CTC model..."

# Set paths and parameters
TRAIN_MANIFEST="data/train_manifest.jsonl"
VAL_MANIFEST="data/val_manifest.jsonl"
OUTPUT_DIR="artifacts/training/character"
CONFIG_PATH="backend/ctc_vsr/config.yaml"

# Check if manifest files exist
if [ ! -f "$TRAIN_MANIFEST" ]; then
    echo "Error: Training manifest not found: $TRAIN_MANIFEST"
    echo "Please create training data manifest first"
    exit 1
fi

if [ ! -f "$VAL_MANIFEST" ]; then
    echo "Error: Validation manifest not found: $VAL_MANIFEST"
    echo "Please create validation data manifest first"
    exit 1
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Set device (use GPU if available)
if command -v nvidia-smi &> /dev/null && nvidia-smi &> /dev/null; then
    DEVICE="cuda"
    echo "Using GPU for training"
else
    DEVICE="cpu"
    echo "Using CPU for training"
fi

# Train character CTC model
python -m backend.ctc_vsr.train_ctc \
    --config "$CONFIG_PATH" \
    --mode character \
    --train-manifest "$TRAIN_MANIFEST" \
    --val-manifest "$VAL_MANIFEST" \
    --output-dir "$OUTPUT_DIR" \
    --device "$DEVICE" \
    --verbose

# Check if training completed successfully
BEST_MODEL="$OUTPUT_DIR/checkpoints/best_model.pt"
if [ -f "$BEST_MODEL" ]; then
    echo "Training completed successfully!"
    echo "Best model saved: $BEST_MODEL"
    
    # Copy best model to standard location
    mkdir -p "artifacts/models"
    cp "$BEST_MODEL" "artifacts/models/char_ctc_best.pt"
    echo "Model copied to: artifacts/models/char_ctc_best.pt"
    
    # Display model size
    echo "Model file size: $(du -h "$BEST_MODEL" | cut -f1)"
else
    echo "Error: Training failed - best model not found"
    exit 1
fi

echo "Character CTC training completed!"
