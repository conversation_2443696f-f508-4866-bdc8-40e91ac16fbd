#!/bin/bash

# Export trained model to TorchScript
# Usage: ./scripts/export_torchscript.sh [model_dir]

set -e  # Exit on any error

MODEL_DIR="${1:-artifacts/vsr_26p_v1}"
CONFIG_PATH="configs/phrases26.yaml"
CHECKPOINT_PATH="$MODEL_DIR/best.ckpt"
OUTPUT_PATH="$MODEL_DIR/model.ts"

# Check if checkpoint exists
if [ ! -f "$CHECKPOINT_PATH" ]; then
    echo "Error: Checkpoint not found: $CHECKPOINT_PATH"
    echo "Please train the model first using scripts/run_train.sh"
    exit 1
fi

# Check if config exists
if [ ! -f "$CONFIG_PATH" ]; then
    echo "Error: Config file not found: $CONFIG_PATH"
    exit 1
fi

# Activate virtual environment if it exists
if [ -d ".venv_vsr" ]; then
    echo "Activating virtual environment..."
    source .venv_vsr/bin/activate
fi

echo "Exporting TorchScript model..."
echo "Checkpoint: $CHECKPOINT_PATH"
echo "Config: $CONFIG_PATH"
echo "Output: $OUTPUT_PATH"

# Export using Python
python - << EOF
import torch
import yaml
import os
from pathlib import Path
from backend.lightweight_vsr.model import Mobile3DTiny

# Load config
with open('$CONFIG_PATH', 'r') as f:
    config = yaml.safe_load(f)

# Load checkpoint
checkpoint = torch.load('$CHECKPOINT_PATH', map_location='cpu')

# Create model
model = Mobile3DTiny(
    num_classes=len(config['phrases']),
    hidden_dim=config.get('model', {}).get('hidden_dim', 256),
    num_gru_layers=config.get('model', {}).get('num_gru_layers', 2),
    dropout=config.get('model', {}).get('dropout', 0.2)
)

# Load weights
model.load_state_dict(checkpoint['model_state_dict'])
model.eval()

# Create example input
channels = 1 if config.get('grayscale', True) else 3
example_input = torch.randn(
    1, channels,
    config.get('frames', 32),
    config.get('height', 96),
    config.get('width', 96)
)

print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
print(f"Example input shape: {example_input.shape}")

# Trace model
try:
    traced_model = torch.jit.trace(model, example_input)
    
    # Test traced model
    with torch.no_grad():
        original_output = model(example_input)
        traced_output = traced_model(example_input)
        max_diff = torch.max(torch.abs(original_output - traced_output)).item()
        print(f"Max difference between original and traced: {max_diff:.6f}")
    
    # Save TorchScript model
    os.makedirs(os.path.dirname('$OUTPUT_PATH'), exist_ok=True)
    traced_model.save('$OUTPUT_PATH')
    
    # Check file size
    file_size_mb = os.path.getsize('$OUTPUT_PATH') / (1024 * 1024)
    print(f"TorchScript model saved: $OUTPUT_PATH")
    print(f"Model size: {file_size_mb:.1f} MB")
    
    if file_size_mb > 10:
        print("Warning: Model size exceeds 10 MB target")
    else:
        print("✅ Model size meets <10 MB target")
        
except Exception as e:
    print(f"Error exporting TorchScript model: {e}")
    exit(1)
EOF

echo "TorchScript export completed!"

# Test inference
echo "Testing inference..."
python - << EOF
try:
    from backend.lightweight_vsr.infer import VSRInference
    
    # Test loading the exported model
    engine = VSRInference('$OUTPUT_PATH', '$CONFIG_PATH')
    print("✅ TorchScript model loads successfully")
    
    # Test with dummy input if no test videos available
    import torch
    channels = 1 if engine.config.get('grayscale', True) else 3
    dummy_input = torch.randn(
        1, channels,
        engine.config.get('frames', 32),
        engine.config.get('height', 96),
        engine.config.get('width', 96),
        device=engine.device
    )
    
    # Time inference
    import time
    start_time = time.time()
    probs = engine.predict_raw(dummy_input)
    inference_time = (time.time() - start_time) * 1000
    
    print(f"✅ Inference test passed")
    print(f"Inference time: {inference_time:.1f}ms")
    
    if inference_time > 150:
        print("Warning: Inference time exceeds 150ms target")
    else:
        print("✅ Inference time meets <150ms target")
        
except Exception as e:
    print(f"Error testing inference: {e}")
EOF

echo "Export and testing completed!"
