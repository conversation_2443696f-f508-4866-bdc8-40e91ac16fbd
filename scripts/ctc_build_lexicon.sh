#!/bin/bash

# Build ICU lexicon from vocabulary using G2P conversion
# This script creates a pronunciation lexicon for constrained beam search decoding

set -e

echo "Building ICU lexicon..."

# Set paths
CONFIG_PATH="backend/ctc_vsr/config.yaml"
OUTPUT_PATH="artifacts/lexicon/icu_lexicon.json"

# Create output directory
mkdir -p "$(dirname "$OUTPUT_PATH")"

# Build lexicon
python -m backend.ctc_vsr.build_lexicon \
    --config "$CONFIG_PATH" \
    --output "$OUTPUT_PATH" \
    --validate \
    --stats \
    --verbose

echo "Lexicon built successfully: $OUTPUT_PATH"

# Display lexicon stats
if [ -f "$OUTPUT_PATH" ]; then
    echo "Lexicon file size: $(du -h "$OUTPUT_PATH" | cut -f1)"
    echo "Number of entries: $(python -c "import json; print(len(json.load(open('$OUTPUT_PATH'))))")"
else
    echo "Error: Lexicon file not created"
    exit 1
fi

echo "Lexicon building completed!"
