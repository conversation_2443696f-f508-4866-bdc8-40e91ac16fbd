#!/bin/bash

# Benchmark inference performance
# Usage: ./scripts/benchmark_infer.sh [model_path] [num_runs]

set -e  # Exit on any error

MODEL_PATH="${1:-artifacts/vsr_26p_v1/model.ts}"
CONFIG_PATH="configs/phrases26.yaml"
NUM_RUNS="${2:-50}"

# Check if model exists
if [ ! -f "$MODEL_PATH" ]; then
    echo "Error: Model not found: $MODEL_PATH"
    echo "Please train and export the model first"
    exit 1
fi

# Activate virtual environment if it exists
if [ -d ".venv_vsr" ]; then
    echo "Activating virtual environment..."
    source .venv_vsr/bin/activate
fi

echo "Benchmarking inference performance..."
echo "Model: $MODEL_PATH"
echo "Runs: $NUM_RUNS"

# Run benchmark
python - << EOF
import time
import torch
import numpy as np
from backend.lightweight_vsr.infer import VSRInference, benchmark_inference
from pathlib import Path
import glob

# Load inference engine
try:
    engine = VSRInference('$MODEL_PATH', '$CONFIG_PATH')
    print(f"Model loaded on {engine.device}")
    print(f"Model parameters: {sum(p.numel() for p in engine.model.parameters()):,}")
except Exception as e:
    print(f"Error loading model: {e}")
    exit(1)

# Benchmark with dummy data
print("\n=== DUMMY DATA BENCHMARK ===")
stats = benchmark_inference('$MODEL_PATH', '$CONFIG_PATH', num_runs=$NUM_RUNS)
print(f"Mean inference time: {stats['mean_time_ms']:.1f} ± {stats['std_time_ms']:.1f} ms")
print(f"Min/Max: {stats['min_time_ms']:.1f} / {stats['max_time_ms']:.1f} ms")
print(f"Device: {stats['device']}")

# Check performance targets
if stats['mean_time_ms'] <= 150:
    print("✅ Meets <150ms latency target")
else:
    print(f"❌ Exceeds 150ms latency target ({stats['mean_time_ms']:.1f}ms)")

# Benchmark with real videos if available
test_video_patterns = [
    "test_webm_videos/*.mp4",
    "test_webm_videos/*.webm", 
    "data/*/henry_*.mp4",
    "data/*/*.mp4"
]

test_videos = []
for pattern in test_video_patterns:
    test_videos.extend(glob.glob(pattern))

if test_videos:
    print(f"\n=== REAL VIDEO BENCHMARK ({len(test_videos)} videos) ===")
    
    # Limit to reasonable number for benchmarking
    test_videos = test_videos[:min($NUM_RUNS, len(test_videos))]
    
    try:
        real_stats = benchmark_inference(
            '$MODEL_PATH', '$CONFIG_PATH', 
            test_videos=test_videos, 
            num_runs=len(test_videos)
        )
        print(f"Mean inference time: {real_stats['mean_time_ms']:.1f} ± {real_stats['std_time_ms']:.1f} ms")
        print(f"Min/Max: {real_stats['min_time_ms']:.1f} / {real_stats['max_time_ms']:.1f} ms")
        print(f"Videos processed: {real_stats['num_videos']}")
        
        if real_stats['mean_time_ms'] <= 150:
            print("✅ Real video inference meets <150ms target")
        else:
            print(f"❌ Real video inference exceeds 150ms target ({real_stats['mean_time_ms']:.1f}ms)")
            
    except Exception as e:
        print(f"Error benchmarking real videos: {e}")
else:
    print("\n=== NO REAL VIDEOS FOUND ===")
    print("Place test videos in test_webm_videos/ or data/ for real video benchmarking")

# Test a few sample predictions if videos available
if test_videos:
    print(f"\n=== SAMPLE PREDICTIONS ===")
    for i, video_path in enumerate(test_videos[:5]):
        try:
            result = engine.predict_video(video_path)
            print(f"{Path(video_path).name}: {result['phrase']} ({result['confidence']:.3f})")
        except Exception as e:
            print(f"{Path(video_path).name}: Error - {e}")

print("\nBenchmark completed!")
EOF

echo "Benchmark completed!"
