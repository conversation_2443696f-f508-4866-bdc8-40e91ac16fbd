#!/bin/bash
# Step-by-step execution script for ICU 26-phrase training

echo "🚀 Starting ICU 26-phrase classifier training..."

# Step 1: Preprocess videos
echo "📹 Step 1: Preprocessing videos..."
python preprocess_videos.py \
    --input_dir ~/Desktop/icu-26-phrases/ \
    --process_type crop \
    --subfolder_mode

if [ $? -ne 0 ]; then
    echo "❌ Step 1 failed"
    exit 1
fi

# Step 2: Validate preprocessing
echo "✅ Step 1 complete. Validating..."
python validate_preprocessing.py

# Step 3: Create reference database
echo "🧠 Step 2: Creating reference database..."
python -c "
from auto_train_26_phrases import ICU26PhraseTrainer
trainer = ICU26PhraseTrainer()
trainer.step2_create_reference_database()
"

# Step 4: Train classifier
echo "🎯 Step 3: Training classifier..."
python -c "
from auto_train_26_phrases import ICU26PhraseTrainer
trainer = ICU26PhraseTrainer()
trainer.step3_train_classifier()
"

echo "🎉 Training pipeline completed!"