#!/usr/bin/env python3
"""
Comprehensive diagnostic tool for mouth region detection.
Tests the exact coordinates being used and creates visual evidence.
"""

import cv2
import numpy as np
import sys
from pathlib import Path

# Add the current directory to path
sys.path.append('/Users/<USER>/Desktop/app dev 23.5.25')

try:
    from enhanced_video_preprocessor import EnhancedVideoPreprocessor
except ImportError as e:
    print(f"❌ Error importing enhanced_video_preprocessor: {e}")
    sys.exit(1)

def extract_frame_and_analyze(video_path, frame_idx=25):
    """Extract a specific frame and analyze mouth detection"""
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ Cannot open video: {video_path}")
        return None
    
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 Video: {width}x{height}, {total_frames} frames")
    
    # Extract specific frame
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        print(f"❌ Could not read frame {frame_idx}")
        return None
    
    return frame, (width, height)

def test_mouth_detection_step_by_step(frame, video_size):
    """Test mouth detection with detailed step-by-step analysis"""
    width, height = video_size
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    print(f"\n🔍 STEP-BY-STEP MOUTH DETECTION ANALYSIS")
    print(f"Frame size: {width}x{height}")
    
    # Initialize preprocessor
    preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
    
    # Step 1: Check video type detection
    is_original_icu_video = (width == 400 and height == 200)
    is_processed_video = (width == 140 and height == 46)
    
    print(f"Video type detection:")
    print(f"  is_original_icu_video: {is_original_icu_video}")
    print(f"  is_processed_video: {is_processed_video}")
    
    # Step 2: Face detection
    faces = preprocessor.face_cascade.detectMultiScale(
        gray,
        scaleFactor=1.1,
        minNeighbors=5,
        flags=cv2.CASCADE_SCALE_IMAGE,
        minSize=(30, 30) if is_processed_video else (60, 60)
    )
    
    print(f"Face detection:")
    print(f"  Faces detected: {len(faces)}")
    if len(faces) > 0:
        for i, (x, y, fw, fh) in enumerate(faces):
            print(f"  Face {i}: {fw}x{fh} at ({x}, {y})")
    
    # Step 3: Get mouth region using the actual algorithm
    x1, y1, x2, y2 = preprocessor.detect_mouth_region(gray)
    
    print(f"Final mouth region:")
    print(f"  Coordinates: ({x1}, {y1}) to ({x2}, {y2})")
    print(f"  Size: {x2-x1}x{y2-y1}")
    print(f"  Center: ({(x1+x2)//2}, {(y1+y2)//2})")
    print(f"  Y percentage: {y1/height*100:.1f}% to {y2/height*100:.1f}%")
    
    # Step 4: Analyze if this is correct for lips
    expected_lip_y1 = 60  # Expected lip region top for 400x200 videos
    expected_lip_y2 = 92  # Expected lip region bottom for 400x200 videos
    
    if is_original_icu_video:
        print(f"Expected lip region for ICU video:")
        print(f"  Expected Y: {expected_lip_y1} to {expected_lip_y2}")
        print(f"  Actual Y: {y1} to {y2}")
        print(f"  Y offset: {y1 - expected_lip_y1} pixels")
        
        if y1 > expected_lip_y2:
            print(f"  ❌ PROBLEM: Mouth region is BELOW expected lip area!")
            print(f"  ❌ This will capture chin/neck instead of lips!")
        elif y2 < expected_lip_y1:
            print(f"  ❌ PROBLEM: Mouth region is ABOVE expected lip area!")
        else:
            print(f"  ✅ Mouth region overlaps with expected lip area")
    
    return x1, y1, x2, y2

def create_visual_diagnostic(frame, mouth_coords, video_size, output_name="diagnostic"):
    """Create visual diagnostic showing mouth region"""
    width, height = video_size
    x1, y1, x2, y2 = mouth_coords
    
    # Create diagnostic image
    diagnostic = frame.copy()
    
    # Draw mouth region in GREEN
    cv2.rectangle(diagnostic, (x1, y1), (x2, y2), (0, 255, 0), 3)
    cv2.putText(diagnostic, "DETECTED MOUTH REGION", (x1, y1-10), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    # For ICU videos, also show expected lip region
    if width == 400 and height == 200:
        expected_x1, expected_y1 = 40, 60
        expected_x2, expected_y2 = 360, 92
        
        # Draw expected lip region in BLUE
        cv2.rectangle(diagnostic, (expected_x1, expected_y1), (expected_x2, expected_y2), (255, 0, 0), 2)
        cv2.putText(diagnostic, "EXPECTED LIP REGION", (expected_x1, expected_y1-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
    
    # Add frame info
    cv2.putText(diagnostic, f"Frame: {width}x{height}", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    cv2.putText(diagnostic, f"Mouth: ({x1},{y1}) to ({x2},{y2})", (10, 60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # Save diagnostic image
    cv2.imwrite(f"{output_name}_diagnostic.jpg", diagnostic)
    print(f"💾 Saved {output_name}_diagnostic.jpg")
    
    # Extract and save the mouth crop
    mouth_crop = frame[y1:y2, x1:x2]
    if mouth_crop.size > 0:
        # Scale up for visibility
        scale_factor = 4
        scaled_crop = cv2.resize(mouth_crop, (mouth_crop.shape[1]*scale_factor, mouth_crop.shape[0]*scale_factor), 
                                interpolation=cv2.INTER_NEAREST)
        cv2.putText(scaled_crop, f"MOUTH CROP (scaled {scale_factor}x)", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        cv2.imwrite(f"{output_name}_mouth_crop.jpg", scaled_crop)
        print(f"💾 Saved {output_name}_mouth_crop.jpg")
    
    return diagnostic

def test_processing_pipeline(video_path):
    """Test the complete processing pipeline to see final output"""
    print(f"\n🔧 TESTING COMPLETE PROCESSING PIPELINE")
    
    # Remove existing processed video
    processed_path = Path("data/where_am_i/where_am_i__useruser01__18to39__male__not_specified__20250809T053449_processed.mp4")
    if processed_path.exists():
        processed_path.unlink()
        print(f"🗑️ Removed existing processed video")
    
    # Process the video
    preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
    
    # Find the phrase index for "Where am I?"
    phrase_idx = None
    for i, phrase in enumerate(preprocessor.phrases):
        if "where am i" in phrase.lower():
            phrase_idx = i
            break
    
    if phrase_idx is None:
        print(f"❌ Could not find 'Where am I?' phrase")
        return False
    
    print(f"📝 Processing with phrase index {phrase_idx}: '{preprocessor.phrases[phrase_idx]}'")
    
    try:
        result = preprocessor.process_single_video(Path(video_path), phrase_idx)
        
        if result and processed_path.exists():
            print(f"✅ Processing successful")
            
            # Analyze the processed output
            cap = cv2.VideoCapture(str(processed_path))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            print(f"📹 Processed video specs: {width}x{height}, {fps} FPS, {frames} frames")
            
            # Extract middle frame from processed video
            cap.set(cv2.CAP_PROP_POS_FRAMES, 37)  # Middle frame
            ret, processed_frame = cap.read()
            cap.release()
            
            if ret:
                # Scale up for visibility and save
                scaled = cv2.resize(processed_frame, (560, 184), interpolation=cv2.INTER_NEAREST)
                cv2.putText(scaled, "FINAL PROCESSED OUTPUT (scaled 4x)", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                cv2.putText(scaled, f"Original: {width}x{height}", (10, 60), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.imwrite("final_processed_output.jpg", scaled)
                print(f"💾 Saved final_processed_output.jpg")
                
                return True
        else:
            print(f"❌ Processing failed or output not found")
            return False
            
    except Exception as e:
        print(f"❌ Processing error: {e}")
        return False

def main():
    video_path = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    
    if not Path(video_path).exists():
        print(f"❌ Video file not found: {video_path}")
        return
    
    print("🔍 COMPREHENSIVE MOUTH DETECTION DIAGNOSTIC")
    print("=" * 60)
    
    # Step 1: Extract and analyze frame
    result = extract_frame_and_analyze(video_path, frame_idx=25)
    if not result:
        return
    
    frame, video_size = result
    
    # Step 2: Test mouth detection step by step
    mouth_coords = test_mouth_detection_step_by_step(frame, video_size)
    
    # Step 3: Create visual diagnostic
    create_visual_diagnostic(frame, mouth_coords, video_size, "original_frame")
    
    # Step 4: Test complete processing pipeline
    success = test_processing_pipeline(video_path)
    
    print(f"\n📋 DIAGNOSTIC SUMMARY:")
    print(f"Video: {Path(video_path).name}")
    print(f"Frame size: {video_size[0]}x{video_size[1]}")
    print(f"Mouth region: {mouth_coords}")
    print(f"Processing success: {success}")
    
    print(f"\n📁 Generated files:")
    print(f"- original_frame_diagnostic.jpg: Shows detected vs expected mouth regions")
    print(f"- original_frame_mouth_crop.jpg: Shows what the algorithm extracted")
    print(f"- final_processed_output.jpg: Shows final 140x46 output")
    
    print(f"\n🔍 NEXT STEPS:")
    print(f"1. Open the diagnostic images to visually inspect mouth region targeting")
    print(f"2. Check if the mouth crop shows lips or chin/neck area")
    print(f"3. Verify the final processed output shows clear lip movements")

if __name__ == "__main__":
    main()
