# ICU Lipreading Training Pipeline - Complete Guide

## 🎯 Overview

This guide provides complete instructions for training the ICU lipreading classifier using the reference videos as ground truth and processing the additional ~1,560 training videos for a total dataset of ~1,638 videos across 26 ICU phrases.

## 📊 Current Status

### ✅ **Phase 1 - Reference Video Setup (COMPLETE)**

**Reference Dataset Analysis:**
- **Location**: `/Users/<USER>/Desktop/icu-videos-today/`
- **Total Videos**: 80 reference videos
- **Coverage**: All 26 ICU phrases (3+ videos per phrase)
- **Format**: WebM, 400x200 resolution, ~30fps, ~3.3 seconds duration
- **Quality**: Excellent - consistent format and high quality

**Key Files Created:**
- `reference_videos_manifest.csv` - Catalog of all reference videos
- `reference_videos_analysis.json` - Detailed technical analysis
- `analyze_reference_videos.py` - Analysis tool

### ✅ **Phase 2 - Pipeline Preparation (COMPLETE)**

**Quality Control System:**
- `video_quality_validator.py` - Validates videos against reference standards
- `batch_video_processor.py` - Batch processing for large datasets
- `large_dataset_training_config.yaml` - Optimized training configuration

**Training Infrastructure:**
- Updated training pipeline to handle ~1,638 videos
- Configured for 26-class classification
- Optimized data splits and training parameters

## 🚀 Next Steps - Processing Additional Videos

### Step 1: Locate Additional Training Videos

You mentioned having ~60 additional videos per phrase (~1,560 total). Please provide the location(s) of these videos so we can process them.

**Expected structure:**
```
/path/to/additional/videos/
├── phrase1_videos/
├── phrase2_videos/
└── ...
```

### Step 2: Batch Process Additional Videos

Once you provide the video locations, run:

```bash
python batch_video_processor.py \
    --video_dirs /path/to/additional/videos/ \
    --output_dir processed_dataset \
    --max_workers 4 \
    --config large_dataset_training_config.yaml
```

**This will:**
- Discover all videos in specified directories
- Validate each video against reference standards
- Filter by quality (similarity threshold: 0.4)
- Create a combined training manifest
- Generate quality assessment reports

### Step 3: Review Quality Results

Check the generated files:
- `processed_dataset/training_manifest.csv` - Final training dataset
- `processed_dataset/quality_report.json` - Quality analysis
- `processed_dataset/detailed_results.json` - Per-video validation results

### Step 4: Start Training

With the processed dataset:

```bash
python train_vsr.py \
    --manifest processed_dataset/training_manifest.csv \
    --config large_dataset_training_config.yaml \
    --out_dir artifacts/icu_lipreading_large_v1 \
    --epochs 50 \
    --batch_size 16
```

## 📋 Expected Results with Large Dataset

### Dataset Size Projections:
- **Reference videos**: 80 videos
- **Additional videos** (after quality filtering): ~1,200-1,400 videos
- **Total dataset**: ~1,280-1,480 videos
- **Per phrase**: ~49-57 videos average (vs. previous 3 videos)

### Training Improvements:
- **Data sufficiency**: 50+ videos per class (vs. 3 previously)
- **Generalization**: Multiple speakers and conditions
- **Robustness**: Better handling of variations
- **Performance**: Expected accuracy >80% (vs. 0% with small dataset)

## 🔧 Tools and Scripts Reference

### Core Analysis Tools:
1. **`analyze_reference_videos.py`** - Analyze reference video quality and create manifest
2. **`video_quality_validator.py`** - Validate individual videos against standards
3. **`batch_video_processor.py`** - Process large video datasets

### Training Tools:
1. **`train_vsr.py`** - Main training script (existing)
2. **`large_dataset_training_config.yaml`** - Optimized configuration
3. **`debug_video_dimensions.py`** - Debug video processing issues

### Configuration Files:
1. **`large_dataset_training_config.yaml`** - Large dataset training config
2. **`configs/phrases26.yaml`** - Original 26-phrase configuration
3. **`reference_videos_manifest.csv`** - Reference video catalog

## 🎯 Quality Standards

### Reference Video Standards:
- **Resolution**: 400x200 pixels
- **Duration**: ~3.3 seconds
- **Frame Rate**: ~30 fps
- **Format**: WebM (VP9 codec)
- **Quality**: Excellent clarity and consistency

### Acceptance Criteria for Additional Videos:
- **Technical Quality**: Pass resolution, duration, fps checks
- **Processing Compatibility**: Successfully process through pipeline
- **Reference Similarity**: ≥0.4 similarity score to reference videos
- **Content Quality**: Clear lip movements, proper phrase content

## 🚨 Troubleshooting

### Common Issues:
1. **Video format incompatibility**: Use ffmpeg to convert to supported formats
2. **Processing failures**: Check video corruption or unusual formats
3. **Low similarity scores**: Review video quality and phrase labeling
4. **Memory issues**: Reduce batch size or number of workers

### Debug Commands:
```bash
# Test single video validation
python -c "
from video_quality_validator import VideoQualityValidator
validator = VideoQualityValidator('reference_videos_manifest.csv')
result = validator.validate_video('/path/to/video.mp4', 'phrase_name')
print(result)
"

# Check video processing
python debug_video_dimensions.py
```

## 📈 Expected Training Timeline

### With Large Dataset (~1,400 videos):
- **Data processing**: 2-4 hours (depending on video count and quality)
- **Training time**: 4-8 hours (50 epochs, GPU recommended)
- **Total pipeline**: 6-12 hours from raw videos to trained model

### Hardware Recommendations:
- **GPU**: NVIDIA GPU with 8GB+ VRAM (recommended)
- **CPU**: 8+ cores for parallel video processing
- **RAM**: 16GB+ for large dataset handling
- **Storage**: 50GB+ for videos and training artifacts

## 🎉 Ready for Next Phase

The reference video analysis and quality control pipeline is complete and ready. Please provide the location of the additional ~1,560 training videos so we can:

1. Process and validate them against the reference standards
2. Create the final training manifest
3. Begin training the 26-class ICU phrase classifier

The infrastructure is now capable of handling the full dataset size and should achieve significantly better results than the initial small-dataset attempts.
