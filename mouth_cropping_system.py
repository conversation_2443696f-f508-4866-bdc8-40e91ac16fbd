#!/usr/bin/env python3
"""
Mouth-Focused Cropping System for ICU Lipreading Videos
Applies precise spatial cropping to focus on lip movement region
"""

import cv2
import numpy as np
import subprocess
import json
from pathlib import Path
import pandas as pd
from typing import Tuple, List, Dict, Optional
import matplotlib.pyplot as plt
from tqdm import tqdm
import sys

# Add current directory to path
sys.path.append('.')

class MouthCroppingSystem:
    """System for applying mouth-focused cropping to lipreading videos"""
    
    def __init__(self, crop_region: Tuple[int, int, int, int] = None):
        """
        Initialize cropping system
        
        Args:
            crop_region: (x, y, width, height) for cropping. If None, uses default mouth region
        """
        
        # Default crop region: middle column, top row of 3x2 grid on 400x200 frame
        # Grid cell size: 133x100, target: x=133-266, y=0-100
        if crop_region is None:
            self.crop_region = (133, 0, 133, 100)  # (x, y, width, height)
        else:
            self.crop_region = crop_region
        
        print(f"🎯 Mouth cropping region: x={self.crop_region[0]}, y={self.crop_region[1]}, "
              f"w={self.crop_region[2]}, h={self.crop_region[3]}")
        
        # Quality validation settings
        self.min_mouth_area_ratio = 0.1  # Minimum mouth region as % of cropped area
        self.motion_threshold = 0.005    # Minimum motion to detect lip movement
        
    def crop_single_video(self, input_path: str, output_path: str, 
                         preserve_quality: bool = True) -> Dict:
        """
        Crop a single video to focus on mouth region
        
        Args:
            input_path: Path to input video
            output_path: Path for output cropped video
            preserve_quality: Whether to preserve original quality settings
            
        Returns:
            Dictionary with cropping results and quality metrics
        """
        
        print(f"🎬 Cropping video: {Path(input_path).name}")
        
        # Validate input
        if not Path(input_path).exists():
            return {'success': False, 'error': f'Input file not found: {input_path}'}
        
        try:
            # Get original video properties
            original_info = self._get_video_info(input_path)
            if not original_info:
                return {'success': False, 'error': 'Could not read video properties'}
            
            # Validate crop region fits within original frame
            orig_width, orig_height = original_info['width'], original_info['height']
            crop_x, crop_y, crop_w, crop_h = self.crop_region
            
            if crop_x + crop_w > orig_width or crop_y + crop_h > orig_height:
                return {
                    'success': False, 
                    'error': f'Crop region {self.crop_region} exceeds frame size {orig_width}x{orig_height}'
                }
            
            # Apply cropping using ffmpeg for high quality
            success = self._apply_ffmpeg_crop(input_path, output_path, preserve_quality)
            
            if not success:
                return {'success': False, 'error': 'FFmpeg cropping failed'}
            
            # Validate output
            cropped_info = self._get_video_info(output_path)
            if not cropped_info:
                return {'success': False, 'error': 'Could not validate cropped video'}
            
            # Quality assessment
            quality_metrics = self._assess_crop_quality(input_path, output_path)
            
            result = {
                'success': True,
                'input_path': input_path,
                'output_path': output_path,
                'original_size': f"{orig_width}x{orig_height}",
                'cropped_size': f"{cropped_info['width']}x{cropped_info['height']}",
                'crop_region': self.crop_region,
                'quality_metrics': quality_metrics,
                'file_size_reduction': original_info.get('size', 0) / cropped_info.get('size', 1) if cropped_info.get('size', 0) > 0 else 0
            }
            
            print(f"   ✅ Success: {orig_width}x{orig_height} → {cropped_info['width']}x{cropped_info['height']}")
            return result
            
        except Exception as e:
            return {'success': False, 'error': f'Cropping failed: {str(e)}'}
    
    def _get_video_info(self, video_path: str) -> Optional[Dict]:
        """Get video properties using ffprobe"""
        
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(video_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                return None
            
            data = json.loads(result.stdout)
            
            # Find video stream
            video_stream = None
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if not video_stream:
                return None
            
            format_info = data.get('format', {})
            
            return {
                'width': video_stream.get('width'),
                'height': video_stream.get('height'),
                'codec': video_stream.get('codec_name'),
                'fps': eval(video_stream.get('r_frame_rate', '0/1')) if video_stream.get('r_frame_rate') else 0,
                'size': int(format_info.get('size', 0))
            }
            
        except Exception:
            return None
    
    def _apply_ffmpeg_crop(self, input_path: str, output_path: str, 
                          preserve_quality: bool = True) -> bool:
        """Apply cropping using ffmpeg"""
        
        crop_x, crop_y, crop_w, crop_h = self.crop_region
        
        # Build ffmpeg command
        cmd = [
            'ffmpeg', '-i', str(input_path),
            '-vf', f'crop={crop_w}:{crop_h}:{crop_x}:{crop_y}',
            '-y'  # Overwrite output file
        ]
        
        if preserve_quality:
            # Preserve original quality settings
            cmd.extend(['-c:v', 'libvpx-vp9', '-crf', '23', '-b:v', '0'])
        
        cmd.append(str(output_path))
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            return result.returncode == 0
        except Exception:
            return False
    
    def _assess_crop_quality(self, original_path: str, cropped_path: str) -> Dict:
        """Assess quality of cropped video"""
        
        quality_metrics = {
            'motion_detected': False,
            'avg_motion_score': 0.0,
            'frame_consistency': True,
            'visual_quality': 'unknown'
        }
        
        try:
            # Read a few frames from cropped video to assess quality
            cap = cv2.VideoCapture(cropped_path)
            frames = []
            
            for _ in range(min(10, 32)):  # Read up to 10 frames
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY))
            
            cap.release()
            
            if len(frames) >= 2:
                # Calculate motion between frames
                motion_scores = []
                for i in range(1, len(frames)):
                    diff = cv2.absdiff(frames[i-1], frames[i])
                    motion_score = np.mean(diff) / 255.0
                    motion_scores.append(motion_score)
                
                avg_motion = np.mean(motion_scores)
                quality_metrics['avg_motion_score'] = avg_motion
                quality_metrics['motion_detected'] = avg_motion > self.motion_threshold
                
                # Check frame consistency (all frames should have same size)
                sizes = [frame.shape for frame in frames]
                quality_metrics['frame_consistency'] = len(set(sizes)) == 1
                
                # Visual quality assessment
                if avg_motion > 0.02:
                    quality_metrics['visual_quality'] = 'high_motion'
                elif avg_motion > 0.005:
                    quality_metrics['visual_quality'] = 'good_motion'
                else:
                    quality_metrics['visual_quality'] = 'low_motion'
            
        except Exception as e:
            quality_metrics['error'] = str(e)
        
        return quality_metrics
    
    def batch_crop_videos(self, input_dir: str, output_dir: str, 
                         file_pattern: str = "*.webm") -> List[Dict]:
        """
        Batch crop all videos in a directory
        
        Args:
            input_dir: Directory containing input videos
            output_dir: Directory for cropped videos
            file_pattern: Pattern to match video files
            
        Returns:
            List of cropping results for each video
        """
        
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # Find all video files
        video_files = list(input_dir.glob(file_pattern))
        
        if not video_files:
            print(f"❌ No video files found matching pattern '{file_pattern}' in {input_dir}")
            return []
        
        print(f"🎬 Found {len(video_files)} videos to crop")
        print(f"📁 Output directory: {output_dir}")
        
        results = []
        
        # Process each video with progress bar
        for video_file in tqdm(video_files, desc="Cropping videos"):
            # Generate output filename
            output_name = video_file.stem + "_mouth_cropped" + video_file.suffix
            output_path = output_dir / output_name
            
            # Crop video
            result = self.crop_single_video(str(video_file), str(output_path))
            results.append(result)
            
            # Show progress
            if result['success']:
                quality = result['quality_metrics']
                motion_status = "✅" if quality['motion_detected'] else "⚠️"
                print(f"   {motion_status} {video_file.name} → {output_name}")
            else:
                print(f"   ❌ {video_file.name}: {result.get('error', 'Unknown error')}")
        
        return results
    
    def create_visual_comparison(self, original_path: str, cropped_path: str, 
                               output_path: str, num_frames: int = 8) -> bool:
        """Create visual comparison between original and cropped video"""
        
        try:
            # Read frames from both videos
            orig_frames = self._read_sample_frames(original_path, num_frames)
            crop_frames = self._read_sample_frames(cropped_path, num_frames)
            
            if not orig_frames or not crop_frames:
                return False
            
            # Create comparison visualization
            fig, axes = plt.subplots(2, num_frames, figsize=(20, 6))
            fig.suptitle(f'Mouth Cropping Comparison: {Path(original_path).stem}', fontsize=16)
            
            for i in range(min(num_frames, len(orig_frames), len(crop_frames))):
                # Original frame (top row)
                orig_rgb = cv2.cvtColor(orig_frames[i], cv2.COLOR_BGR2RGB)
                axes[0, i].imshow(orig_rgb)
                axes[0, i].set_title(f'Original\nFrame {i}')
                axes[0, i].axis('off')
                
                # Add crop region overlay
                crop_x, crop_y, crop_w, crop_h = self.crop_region
                rect = plt.Rectangle((crop_x, crop_y), crop_w, crop_h, 
                                   linewidth=2, edgecolor='red', facecolor='none')
                axes[0, i].add_patch(rect)
                
                # Cropped frame (bottom row)
                crop_rgb = cv2.cvtColor(crop_frames[i], cv2.COLOR_BGR2RGB)
                axes[1, i].imshow(crop_rgb)
                axes[1, i].set_title(f'Cropped\nFrame {i}')
                axes[1, i].axis('off')
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating visual comparison: {e}")
            return False
    
    def _read_sample_frames(self, video_path: str, num_frames: int) -> List[np.ndarray]:
        """Read sample frames from video"""
        
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames <= 0:
            cap.release()
            return []
        
        # Calculate frame indices to sample evenly
        if total_frames <= num_frames:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = [int(i * total_frames / num_frames) for i in range(num_frames)]
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if ret:
                frames.append(frame)
        
        cap.release()
        return frames
    
    def generate_cropping_report(self, results: List[Dict], output_path: str) -> Dict:
        """Generate comprehensive cropping report"""
        
        report = {
            'summary': {
                'total_videos': len(results),
                'successful_crops': sum(1 for r in results if r['success']),
                'failed_crops': sum(1 for r in results if not r['success']),
                'success_rate': 0.0
            },
            'quality_analysis': {
                'motion_detected': 0,
                'high_quality_crops': 0,
                'avg_motion_score': 0.0,
                'avg_size_reduction': 0.0
            },
            'crop_settings': {
                'crop_region': self.crop_region,
                'target_area': f"{self.crop_region[2]}x{self.crop_region[3]}"
            },
            'failed_videos': [],
            'quality_issues': []
        }
        
        if results:
            successful_results = [r for r in results if r['success']]
            
            # Calculate success rate
            report['summary']['success_rate'] = len(successful_results) / len(results)
            
            if successful_results:
                # Quality analysis
                motion_count = sum(1 for r in successful_results 
                                 if r['quality_metrics'].get('motion_detected', False))
                report['quality_analysis']['motion_detected'] = motion_count
                
                motion_scores = [r['quality_metrics'].get('avg_motion_score', 0) 
                               for r in successful_results]
                report['quality_analysis']['avg_motion_score'] = np.mean(motion_scores)
                
                size_reductions = [r.get('file_size_reduction', 0) for r in successful_results]
                report['quality_analysis']['avg_size_reduction'] = np.mean(size_reductions)
                
                # High quality crops (good motion + no errors)
                high_quality = sum(1 for r in successful_results 
                                 if r['quality_metrics'].get('motion_detected', False) and
                                    r['quality_metrics'].get('frame_consistency', True))
                report['quality_analysis']['high_quality_crops'] = high_quality
            
            # Failed videos
            failed_results = [r for r in results if not r['success']]
            report['failed_videos'] = [
                {'path': r.get('input_path', 'unknown'), 'error': r.get('error', 'unknown')}
                for r in failed_results
            ]
            
            # Quality issues
            quality_issues = []
            for r in successful_results:
                metrics = r['quality_metrics']
                if not metrics.get('motion_detected', True):
                    quality_issues.append({
                        'path': r['input_path'],
                        'issue': 'Low motion detected',
                        'motion_score': metrics.get('avg_motion_score', 0)
                    })
                if not metrics.get('frame_consistency', True):
                    quality_issues.append({
                        'path': r['input_path'],
                        'issue': 'Frame size inconsistency'
                    })
            
            report['quality_issues'] = quality_issues
        
        # Save report
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return report

def main():
    """Main function for batch cropping reference videos"""
    
    print("🎯 ICU Lipreading Mouth-Focused Cropping System")
    print("=" * 60)
    
    # Initialize cropping system
    cropper = MouthCroppingSystem()
    
    # Set up paths
    input_dir = "/Users/<USER>/Desktop/icu-videos-today"
    output_dir = "/Users/<USER>/Desktop/app dev 23.5.25/mouth_cropped_videos"
    
    print(f"📁 Input directory: {input_dir}")
    print(f"📁 Output directory: {output_dir}")
    
    # Batch crop all reference videos
    results = cropper.batch_crop_videos(input_dir, output_dir)
    
    if not results:
        print("❌ No videos processed")
        return
    
    # Generate report
    report_path = Path(output_dir) / "cropping_report.json"
    report = cropper.generate_cropping_report(results, report_path)
    
    # Create visual comparisons for sample videos
    print(f"\n🎨 Creating visual comparisons...")
    
    successful_results = [r for r in results if r['success']]
    sample_videos = successful_results[:3]  # First 3 successful crops
    
    for i, result in enumerate(sample_videos):
        comparison_path = Path(output_dir) / f"comparison_{i+1}_{Path(result['input_path']).stem}.png"
        success = cropper.create_visual_comparison(
            result['input_path'], 
            result['output_path'], 
            comparison_path
        )
        if success:
            print(f"   ✅ Comparison {i+1}: {comparison_path.name}")
    
    # Print summary
    print(f"\n📊 Cropping Summary:")
    print(f"   Total videos: {report['summary']['total_videos']}")
    print(f"   Successful: {report['summary']['successful_crops']}")
    print(f"   Failed: {report['summary']['failed_crops']}")
    print(f"   Success rate: {report['summary']['success_rate']:.1%}")
    print(f"   Motion detected: {report['quality_analysis']['motion_detected']}")
    print(f"   High quality crops: {report['quality_analysis']['high_quality_crops']}")
    print(f"   Average motion score: {report['quality_analysis']['avg_motion_score']:.4f}")
    print(f"   Average size reduction: {report['quality_analysis']['avg_size_reduction']:.1f}x")
    
    if report['failed_videos']:
        print(f"\n❌ Failed videos:")
        for failed in report['failed_videos'][:5]:  # Show first 5 failures
            print(f"   - {Path(failed['path']).name}: {failed['error']}")
    
    if report['quality_issues']:
        print(f"\n⚠️  Quality issues:")
        for issue in report['quality_issues'][:5]:  # Show first 5 issues
            print(f"   - {Path(issue['path']).name}: {issue['issue']}")
    
    print(f"\n💾 Report saved: {report_path}")
    print(f"🎯 Mouth-cropped videos ready for training!")

if __name__ == '__main__':
    main()
