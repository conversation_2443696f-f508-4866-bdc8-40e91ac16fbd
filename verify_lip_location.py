#!/usr/bin/env python3
"""
Verify exact lip location in ICU video layout.
Creates visual grid overlay to confirm lip positioning in upper-middle section.
"""

import cv2
import numpy as np
from pathlib import Path

def analyze_video_layout(video_path):
    """Analyze the 400x200 video layout with 3x2 grid overlay"""
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ Cannot open video: {video_path}")
        return
    
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📹 Video Analysis: {width}x{height}, {total_frames} frames")
    
    # Extract multiple frames for analysis
    frame_indices = [10, 25, 40, 55, 70]
    
    for i, frame_idx in enumerate(frame_indices):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if not ret:
            continue
        
        # Create grid overlay
        grid_frame = frame.copy()
        
        # Draw 3x2 grid (3 columns, 2 rows)
        col_width = width // 3  # 133 pixels per column
        row_height = height // 2  # 100 pixels per row
        
        # Draw vertical lines
        for col in range(1, 3):
            x = col * col_width
            cv2.line(grid_frame, (x, 0), (x, height), (255, 255, 255), 2)
        
        # Draw horizontal line
        y = row_height
        cv2.line(grid_frame, (0, y), (width, y), (255, 255, 255), 2)
        
        # Label grid sections
        sections = [
            ("Top-Left", 10, 30),
            ("Top-Middle", col_width + 10, 30),
            ("Top-Right", 2*col_width + 10, 30),
            ("Bottom-Left", 10, row_height + 30),
            ("Bottom-Middle", col_width + 10, row_height + 30),
            ("Bottom-Right", 2*col_width + 10, row_height + 30)
        ]
        
        for label, x, y in sections:
            cv2.putText(grid_frame, label, (x, y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # Highlight the top-middle section (where lips should be)
        top_middle_x1 = col_width
        top_middle_y1 = 0
        top_middle_x2 = 2 * col_width
        top_middle_y2 = row_height
        
        cv2.rectangle(grid_frame, (top_middle_x1, top_middle_y1), (top_middle_x2, top_middle_y2), (0, 0, 255), 3)
        cv2.putText(grid_frame, "LIPS HERE", (top_middle_x1 + 10, top_middle_y1 + 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Add current mouth detection region
        current_x1, current_y1 = 40, 60
        current_x2, current_y2 = 360, 92
        cv2.rectangle(grid_frame, (current_x1, current_y1), (current_x2, current_y2), (255, 0, 0), 2)
        cv2.putText(grid_frame, "CURRENT ALGORITHM", (current_x1, current_y1-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        # Add frame info
        cv2.putText(grid_frame, f"Frame {frame_idx}: {width}x{height}", (10, height-20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Save grid analysis
        cv2.imwrite(f"grid_analysis_frame_{frame_idx}.jpg", grid_frame)
        print(f"💾 Saved grid_analysis_frame_{frame_idx}.jpg")
        
        # Extract and analyze the top-middle section
        top_middle_crop = frame[top_middle_y1:top_middle_y2, top_middle_x1:top_middle_x2]
        if top_middle_crop.size > 0:
            # Scale up for visibility
            scaled_crop = cv2.resize(top_middle_crop, (400, 300), interpolation=cv2.INTER_NEAREST)
            cv2.putText(scaled_crop, f"TOP-MIDDLE SECTION (Frame {frame_idx})", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(scaled_crop, f"Original: {col_width}x{row_height}", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.imwrite(f"top_middle_section_frame_{frame_idx}.jpg", scaled_crop)
            print(f"💾 Saved top_middle_section_frame_{frame_idx}.jpg")
    
    cap.release()
    
    # Print analysis summary
    print(f"\n📊 LAYOUT ANALYSIS SUMMARY:")
    print(f"Video size: {width}x{height}")
    print(f"Grid layout: 3x2 (3 columns × 2 rows)")
    print(f"Column width: {col_width} pixels")
    print(f"Row height: {row_height} pixels")
    print(f"Top-middle section: ({col_width}, 0) to ({2*col_width}, {row_height})")
    print(f"Current algorithm: (40, 60) to (360, 92)")
    
    # Check if current algorithm targets the right area
    if current_y1 >= 0 and current_y2 <= row_height:
        print(f"✅ Current algorithm Y range ({current_y1}-{current_y2}) is within top row (0-{row_height})")
    else:
        print(f"❌ Current algorithm Y range ({current_y1}-{current_y2}) extends beyond top row (0-{row_height})")
    
    if current_x1 >= col_width and current_x2 <= 2*col_width:
        print(f"✅ Current algorithm X range ({current_x1}-{current_x2}) is within middle column ({col_width}-{2*col_width})")
    else:
        print(f"❌ Current algorithm X range ({current_x1}-{current_x2}) extends beyond middle column ({col_width}-{2*col_width})")
    
    return {
        "video_size": (width, height),
        "grid_size": (col_width, row_height),
        "top_middle_bounds": (col_width, 0, 2*col_width, row_height),
        "current_algorithm_bounds": (current_x1, current_y1, current_x2, current_y2)
    }

def create_corrected_coordinates(analysis):
    """Generate corrected coordinates based on layout analysis"""
    width, height = analysis["video_size"]
    col_width, row_height = analysis["grid_size"]
    
    print(f"\n🔧 GENERATING CORRECTED COORDINATES:")
    
    # Target the top-middle section more precisely
    # Add some margin within the top-middle section for better lip targeting
    margin_x = int(col_width * 0.1)  # 10% margin on sides
    margin_y = int(row_height * 0.2)  # 20% margin on top/bottom
    
    corrected_x1 = col_width + margin_x
    corrected_y1 = margin_y
    corrected_x2 = 2 * col_width - margin_x
    corrected_y2 = row_height - margin_y
    
    print(f"Top-middle section: ({col_width}, 0) to ({2*col_width}, {row_height})")
    print(f"With margins: ({corrected_x1}, {corrected_y1}) to ({corrected_x2}, {corrected_y2})")
    print(f"Region size: {corrected_x2-corrected_x1}x{corrected_y2-corrected_y1}")
    print(f"Y percentage: {corrected_y1/height*100:.1f}% to {corrected_y2/height*100:.1f}%")
    
    return corrected_x1, corrected_y1, corrected_x2, corrected_y2

def test_corrected_coordinates(video_path, corrected_coords):
    """Test the corrected coordinates on sample frames"""
    print(f"\n🧪 TESTING CORRECTED COORDINATES:")
    
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        return
    
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    # Test on middle frame
    cap.set(cv2.CAP_PROP_POS_FRAMES, 37)
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        return
    
    x1, y1, x2, y2 = corrected_coords
    
    # Create comparison image
    comparison = frame.copy()
    
    # Draw current algorithm in RED
    current_x1, current_y1, current_x2, current_y2 = 40, 60, 360, 92
    cv2.rectangle(comparison, (current_x1, current_y1), (current_x2, current_y2), (0, 0, 255), 2)
    cv2.putText(comparison, "CURRENT", (current_x1, current_y1-10), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
    
    # Draw corrected algorithm in GREEN
    cv2.rectangle(comparison, (x1, y1), (x2, y2), (0, 255, 0), 3)
    cv2.putText(comparison, "CORRECTED", (x1, y1-10), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    cv2.imwrite("coordinate_comparison.jpg", comparison)
    print(f"💾 Saved coordinate_comparison.jpg")
    
    # Extract corrected crop
    corrected_crop = frame[y1:y2, x1:x2]
    if corrected_crop.size > 0:
        scaled_crop = cv2.resize(corrected_crop, (400, 200), interpolation=cv2.INTER_NEAREST)
        cv2.putText(scaled_crop, "CORRECTED LIP REGION", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        cv2.imwrite("corrected_lip_crop.jpg", scaled_crop)
        print(f"💾 Saved corrected_lip_crop.jpg")

def main():
    video_path = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    
    if not Path(video_path).exists():
        print(f"❌ Video file not found: {video_path}")
        return
    
    print("🔍 ICU VIDEO LAYOUT VERIFICATION")
    print("=" * 50)
    
    # Analyze video layout with grid overlay
    analysis = analyze_video_layout(video_path)
    
    # Generate corrected coordinates
    corrected_coords = create_corrected_coordinates(analysis)
    
    # Test corrected coordinates
    test_corrected_coordinates(video_path, corrected_coords)
    
    print(f"\n📋 RECOMMENDATIONS:")
    print(f"1. Update detect_mouth_region() to use coordinates: {corrected_coords}")
    print(f"2. Test the updated algorithm on sample videos")
    print(f"3. Verify lip visibility in processed output")
    print(f"4. Reprocess full dataset only after validation")

if __name__ == "__main__":
    main()
