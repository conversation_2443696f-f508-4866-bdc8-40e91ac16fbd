#!/usr/bin/env python3
"""
Test the automated video processing system with a small subset
"""

import sys
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from automated_video_processor import AutomatedVideoProcessor

def test_video_discovery():
    """Test video discovery functionality"""
    
    print("🧪 Testing Video Discovery System")
    print("=" * 40)
    
    # Test with the actual training directory
    source_dir = "/Users/<USER>/Desktop/icu-videos-for-training 14.8.25"
    target_dir = "/Users/<USER>/Desktop/test_processed_videos"
    
    # Initialize processor
    processor = AutomatedVideoProcessor(source_dir, target_dir)
    
    # Test discovery
    video_files = processor.discover_videos()
    
    if video_files:
        print(f"✅ Discovery test successful!")
        print(f"📊 Found {len(video_files)} video files")
        
        # Show first few files for verification
        print(f"\n📋 Sample video files:")
        for i, video_file in enumerate(video_files[:10]):
            print(f"   {i+1}. {video_file.name}")
        
        if len(video_files) > 10:
            print(f"   ... and {len(video_files) - 10} more files")
        
        return True, video_files
    else:
        print(f"❌ No video files found in {source_dir}")
        return False, []

def test_metadata_parsing(video_files):
    """Test metadata parsing on sample files"""
    
    print(f"\n🧪 Testing Metadata Parsing")
    print("=" * 30)
    
    source_dir = "/Users/<USER>/Desktop/icu-videos-for-training 14.8.25"
    target_dir = "/Users/<USER>/Desktop/test_processed_videos"
    
    processor = AutomatedVideoProcessor(source_dir, target_dir)
    
    # Test parsing on first 10 files
    sample_files = video_files[:10]
    
    print(f"📊 Testing metadata parsing on {len(sample_files)} sample files:")
    
    parsed_count = 0
    for video_file in sample_files:
        metadata = processor.parse_video_metadata(video_file)
        
        if metadata:
            parsed_count += 1
            phrase = metadata['phrase']
            speaker = metadata['speaker_id']
            print(f"   ✅ {video_file.name}")
            print(f"      Phrase: '{phrase}'")
            print(f"      Speaker: {speaker}")
        else:
            print(f"   ❌ {video_file.name} - parsing failed")
    
    success_rate = (parsed_count / len(sample_files)) * 100
    print(f"\n📊 Parsing Results:")
    print(f"   Successful: {parsed_count}/{len(sample_files)} ({success_rate:.1f}%)")
    
    return parsed_count > 0

def test_phrase_filtering(video_files):
    """Test ICU phrase filtering"""
    
    print(f"\n🧪 Testing ICU Phrase Filtering")
    print("=" * 35)
    
    source_dir = "/Users/<USER>/Desktop/icu-videos-for-training 14.8.25"
    target_dir = "/Users/<USER>/Desktop/test_processed_videos"
    
    processor = AutomatedVideoProcessor(source_dir, target_dir)
    
    # Parse metadata for sample files
    sample_files = video_files[:50]  # Test with 50 files
    video_metadata = []
    
    for video_file in sample_files:
        metadata = processor.parse_video_metadata(video_file)
        if metadata:
            video_metadata.append(metadata)
    
    print(f"📊 Parsed metadata for {len(video_metadata)} videos")
    
    # Test filtering
    filtered_videos = processor.filter_icu_phrases(video_metadata)
    
    if filtered_videos:
        print(f"✅ Filtering test successful!")
        print(f"📊 {len(filtered_videos)} videos match ICU phrases")
        
        # Show phrase distribution
        phrase_counts = {}
        for video in filtered_videos:
            phrase = video['phrase']
            phrase_counts[phrase] = phrase_counts.get(phrase, 0) + 1
        
        print(f"\n📋 Phrase distribution in sample:")
        for phrase, count in sorted(phrase_counts.items()):
            print(f"   {phrase.title()}: {count} video{'s' if count != 1 else ''}")
        
        return True, filtered_videos
    else:
        print(f"❌ No videos match ICU phrases")
        return False, []

def test_single_video_processing(filtered_videos):
    """Test processing a single video"""
    
    print(f"\n🧪 Testing Single Video Processing")
    print("=" * 35)
    
    if not filtered_videos:
        print("❌ No filtered videos available for processing test")
        return False
    
    source_dir = "/Users/<USER>/Desktop/icu-videos-for-training 14.8.25"
    target_dir = "/Users/<USER>/Desktop/test_processed_videos"
    
    processor = AutomatedVideoProcessor(source_dir, target_dir)
    
    # Test with first filtered video
    test_video = filtered_videos[0]
    video_path = Path(test_video['file_path'])
    
    print(f"📹 Testing video: {video_path.name}")
    print(f"🎯 Phrase: {test_video['phrase'].title()}")
    
    # Get original video properties
    original_props = processor.get_video_properties(video_path)
    
    if original_props:
        print(f"📊 Original properties:")
        print(f"   Size: {original_props['width']}×{original_props['height']}")
        print(f"   Frames: {original_props['frame_count']}")
        print(f"   File size: {original_props['file_size']/1024:.1f} KB")
        
        # Generate output path
        phrase_part = test_video['phrase'].replace(" ", "_")
        output_name = f"{phrase_part}__test_processing_mouth_cropped.webm"
        output_path = Path(target_dir) / output_name
        
        # Test cropping
        print(f"🔄 Applying mouth cropping...")
        
        crop_result = processor.cropper.crop_single_video(
            str(video_path),
            str(output_path),
            preserve_quality=True
        )
        
        if crop_result['success']:
            print(f"✅ Cropping successful!")
            
            # Get processed properties
            processed_props = processor.get_video_properties(output_path)
            
            if processed_props:
                print(f"📊 Processed properties:")
                print(f"   Size: {processed_props['width']}×{processed_props['height']}")
                print(f"   File size: {processed_props['file_size']/1024:.1f} KB")
                print(f"   Size reduction: {original_props['file_size']/processed_props['file_size']:.1f}x")
                
                # Verify dimensions
                if processed_props['width'] in [132, 133] and processed_props['height'] == 100:
                    print(f"✅ Dimensions correct: {processed_props['width']}×{processed_props['height']}")
                    return True
                else:
                    print(f"⚠️  Unexpected dimensions: {processed_props['width']}×{processed_props['height']}")
                    return False
            else:
                print(f"❌ Could not read processed video properties")
                return False
        else:
            print(f"❌ Cropping failed: {crop_result.get('error', 'Unknown error')}")
            return False
    else:
        print(f"❌ Could not read original video properties")
        return False

def main():
    """Main test function"""
    
    print("🧪 Automated Video Processing System - Test Suite")
    print("=" * 60)
    
    # Test 1: Video Discovery
    discovery_success, video_files = test_video_discovery()
    
    if not discovery_success:
        print("❌ Discovery test failed. Cannot proceed.")
        return False
    
    # Test 2: Metadata Parsing
    parsing_success = test_metadata_parsing(video_files)
    
    if not parsing_success:
        print("❌ Metadata parsing test failed. Cannot proceed.")
        return False
    
    # Test 3: Phrase Filtering
    filtering_success, filtered_videos = test_phrase_filtering(video_files)
    
    if not filtering_success:
        print("❌ Phrase filtering test failed. Cannot proceed.")
        return False
    
    # Test 4: Single Video Processing
    processing_success = test_single_video_processing(filtered_videos)
    
    # Summary
    print(f"\n🎯 Test Suite Summary")
    print("=" * 25)
    print(f"   Video Discovery: {'✅ PASS' if discovery_success else '❌ FAIL'}")
    print(f"   Metadata Parsing: {'✅ PASS' if parsing_success else '❌ FAIL'}")
    print(f"   Phrase Filtering: {'✅ PASS' if filtering_success else '❌ FAIL'}")
    print(f"   Video Processing: {'✅ PASS' if processing_success else '❌ FAIL'}")
    
    all_tests_passed = all([discovery_success, parsing_success, filtering_success, processing_success])
    
    print(f"\n🎉 Overall Result: {'✅ ALL TESTS PASSED' if all_tests_passed else '❌ SOME TESTS FAILED'}")
    
    if all_tests_passed:
        print(f"🚀 System ready for full processing!")
        print(f"📊 Found {len(video_files)} total videos")
        print(f"🎯 {len(filtered_videos)} videos match ICU phrases in sample")
    else:
        print(f"⚠️  Please fix issues before running full processing")
    
    return all_tests_passed

if __name__ == '__main__':
    main()
