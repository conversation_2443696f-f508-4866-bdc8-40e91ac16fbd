#!/usr/bin/env python3
"""
ICU Reference Video Analyzer for LipNet Perfect 10 Validation
Analyzes 5 randomly selected videos from ICU reference training dataset
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
from pathlib import Path
import sys
import json
import time
import random
import re
from typing import List, Dict, Optional, Tuple

# Add current directory to path
sys.path.append('.')

from lipnet_perfect_10 import LipNetPerfect10, LipNetPerfect10Manager
from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class ICUReferenceVideoAnalyzer:
    """Analyzer for ICU reference videos with LipNet Perfect 10 validation"""
    
    def __init__(self):
        """Initialize the ICU reference video analyzer"""
        
        self.device = torch.device('cpu')
        
        # Perfect 10 phrases (exact format for classification)
        self.perfect_phrases = [
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        
        # Video source paths (in priority order)
        self.video_sources = [
            "/Users/<USER>/Desktop/reference videos for training",
            "/Users/<USER>/Desktop/icu-videos-today",
            "/Users/<USER>/Desktop/processed_perfect_10_videos/"
        ]
        
        # Model paths
        self.lipnet_model_path = "checkpoints/lipnet_perfect_10/best_lipnet_perfect_10_model.pth"
        self.mobile3d_model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
        
        # Models
        self.lipnet_model = None
        self.mobile3d_model = None
        self.active_model = None
        self.active_model_name = None
        
        # Enhanced video processor
        self.video_processor = VideoProcessor(
            target_frames=64,
            target_size=(112, 112),
            grayscale=True,
            fps=25.0,
            mouth_crop=None,  # Will be set based on video type
            use_dataset_normalization=True
        )
        
        print(f"🎯 ICU Reference Video Analyzer Initialized")
        print(f"   Perfect phrases: {len(self.perfect_phrases)}")
        print(f"   Device: {self.device}")
        print(f"   Enhanced preprocessing: 64 frames, 112×112, z-score normalization")
    
    def find_available_videos(self) -> Tuple[str, List[str]]:
        """Find available videos from priority sources"""
        
        print(f"\n📁 Searching for ICU Reference Videos")
        print("=" * 40)
        
        for source_path in self.video_sources:
            source = Path(source_path)
            print(f"🔍 Checking: {source}")
            
            if source.exists():
                # Find video files
                video_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
                video_files = []
                
                for ext in video_extensions:
                    video_files.extend(list(source.glob(f"*{ext}")))
                    video_files.extend(list(source.glob(f"**/*{ext}")))
                
                if video_files:
                    print(f"   ✅ Found {len(video_files)} videos")
                    
                    # Filter for Perfect 10 phrases if possible
                    perfect_10_videos = self.filter_perfect_10_videos(video_files)
                    
                    if perfect_10_videos:
                        print(f"   🎯 Perfect 10 videos: {len(perfect_10_videos)}")
                        return str(source), perfect_10_videos
                    else:
                        print(f"   ⚠️  No Perfect 10 videos identified, using all videos")
                        return str(source), [str(f) for f in video_files]
                else:
                    print(f"   ❌ No videos found")
            else:
                print(f"   ❌ Path does not exist")
        
        print(f"\n❌ No video sources available")
        return "", []
    
    def filter_perfect_10_videos(self, video_files: List[Path]) -> List[str]:
        """Filter videos that contain Perfect 10 phrases based on filename"""
        
        perfect_10_videos = []
        
        for video_file in video_files:
            filename = video_file.name.lower()
            
            # Check if filename contains any Perfect 10 phrase keywords
            for phrase in self.perfect_phrases:
                # Create search patterns from phrase
                phrase_words = phrase.split()
                
                # Check if most words from phrase appear in filename
                matches = sum(1 for word in phrase_words if word in filename)
                if matches >= len(phrase_words) - 1:  # Allow one missing word
                    perfect_10_videos.append(str(video_file))
                    print(f"   🎯 Perfect 10 match: {video_file.name} -> '{phrase}'")
                    break
        
        return perfect_10_videos
    
    def extract_ground_truth(self, video_path: str) -> Optional[str]:
        """Extract ground truth phrase from video filename if possible"""
        
        filename = Path(video_path).name.lower()
        
        # Try to match filename to Perfect 10 phrases
        best_match = None
        best_score = 0
        
        for phrase in self.perfect_phrases:
            phrase_words = phrase.split()
            
            # Count word matches
            matches = sum(1 for word in phrase_words if word in filename)
            score = matches / len(phrase_words)
            
            if score > best_score and score >= 0.6:  # At least 60% word match
                best_score = score
                best_match = phrase
        
        return best_match
    
    def load_models(self) -> Tuple[bool, bool]:
        """Load available models (LipNet priority, Mobile3DTiny fallback)"""
        
        print(f"\n🤖 Loading Models")
        print("=" * 20)
        
        lipnet_loaded = False
        mobile3d_loaded = False
        
        # Try to load LipNet model (priority)
        if Path(self.lipnet_model_path).exists():
            try:
                print(f"🧠 Loading LipNet Perfect 10...")
                checkpoint = torch.load(self.lipnet_model_path, map_location=self.device)
                
                manager = LipNetPerfect10Manager()
                self.lipnet_model = manager.create_model(
                    hidden_dim=256,
                    num_rnn_layers=2,
                    rnn_type='LSTM',
                    dropout=0.3
                )
                
                self.lipnet_model.load_state_dict(checkpoint['model_state_dict'])
                self.lipnet_model.to(self.device)
                self.lipnet_model.eval()
                
                self.active_model = self.lipnet_model
                self.active_model_name = "LipNet Perfect 10"
                
                accuracy = checkpoint.get('best_val_accuracy', 0.0)
                print(f"   ✅ LipNet loaded: {self.lipnet_model.get_num_parameters():,} params, {accuracy:.1%} accuracy")
                lipnet_loaded = True
                
            except Exception as e:
                print(f"   ❌ LipNet loading failed: {e}")
        else:
            print(f"🧠 LipNet model not found: {self.lipnet_model_path}")
        
        # Try to load Mobile3DTiny model (fallback)
        if Path(self.mobile3d_model_path).exists():
            try:
                print(f"📱 Loading Enhanced Mobile3DTiny...")
                checkpoint = torch.load(self.mobile3d_model_path, map_location=self.device)
                
                self.mobile3d_model = Perfect10Mobile3DTiny(num_classes=10)
                self.mobile3d_model.load_state_dict(checkpoint['model_state_dict'])
                self.mobile3d_model.to(self.device)
                self.mobile3d_model.eval()
                
                if not lipnet_loaded:
                    self.active_model = self.mobile3d_model
                    self.active_model_name = "Enhanced Mobile3DTiny"
                
                accuracy = checkpoint.get('best_val_accuracy', 0.0)
                print(f"   ✅ Mobile3DTiny loaded: {self.mobile3d_model.get_num_parameters():,} params, {accuracy:.1%} accuracy")
                mobile3d_loaded = True
                
            except Exception as e:
                print(f"   ❌ Mobile3DTiny loading failed: {e}")
        else:
            print(f"📱 Mobile3DTiny model not found: {self.mobile3d_model_path}")
        
        if self.active_model:
            print(f"\n🎯 Active Model: {self.active_model_name}")
        
        return lipnet_loaded, mobile3d_loaded
    
    def create_temporal_crops(self, video_tensor: torch.Tensor) -> List[torch.Tensor]:
        """Create 3 temporal crops for Test-Time Augmentation"""
        
        C, T, H, W = video_tensor.shape
        target_frames = 64
        
        if T <= target_frames:
            return [video_tensor]
        
        crops = []
        
        # Crop 1: Beginning
        crop1 = video_tensor[:, :target_frames, :, :]
        crops.append(crop1)
        
        # Crop 2: Middle
        middle = T // 2
        start_middle = max(0, middle - target_frames // 2)
        end_middle = min(T, start_middle + target_frames)
        crop2 = video_tensor[:, start_middle:end_middle, :, :]
        
        if crop2.shape[1] < target_frames:
            padding_needed = target_frames - crop2.shape[1]
            last_frame = crop2[:, -1:, :, :].repeat(1, padding_needed, 1, 1)
            crop2 = torch.cat([crop2, last_frame], dim=1)
        
        crops.append(crop2)
        
        # Crop 3: End
        crop3 = video_tensor[:, -target_frames:, :, :]
        crops.append(crop3)
        
        return crops
    
    def analyze_single_video(self, video_path: str, use_tta: bool = True) -> Dict:
        """Analyze a single video with the active model"""
        
        video_name = Path(video_path).name
        print(f"\n🎬 Analyzing: {video_name}")
        
        if not self.active_model:
            return {
                'success': False,
                'error': 'No model loaded',
                'video_path': video_path
            }
        
        try:
            # Extract ground truth if possible
            ground_truth = self.extract_ground_truth(video_path)
            
            # Check if video is pre-cropped
            is_pre_cropped = "_mouth_cropped" in video_name or "processed_" in video_name
            
            # Set mouth cropping based on video type
            if is_pre_cropped:
                self.video_processor.mouth_crop = None
            else:
                self.video_processor.mouth_crop = (133, 0, 133, 100)
            
            # Start timing
            start_time = time.time()
            
            # Process video
            video_tensor = self.video_processor.process_video(video_path)
            
            print(f"   📊 Processed tensor: {video_tensor.shape}")
            print(f"   📊 Value range: [{video_tensor.min():.3f}, {video_tensor.max():.3f}]")
            print(f"   🔧 Pre-cropped: {is_pre_cropped}")
            
            # Apply TTA if enabled and video is long enough
            if use_tta and video_tensor.shape[1] > 64:
                temporal_crops = self.create_temporal_crops(video_tensor)
                print(f"   🔄 TTA: {len(temporal_crops)} temporal crops")
                
                all_logits = []
                for crop in temporal_crops:
                    crop_batch = crop.unsqueeze(0).to(self.device)
                    with torch.no_grad():
                        logits = self.active_model(crop_batch)
                        all_logits.append(logits[0])
                
                final_logits = torch.stack(all_logits).mean(dim=0)
                probabilities = F.softmax(final_logits, dim=0)
                tta_used = True
                
            else:
                video_batch = video_tensor.unsqueeze(0).to(self.device)
                with torch.no_grad():
                    logits = self.active_model(video_batch)
                    final_logits = logits[0]
                    probabilities = F.softmax(final_logits, dim=0)
                tta_used = False
            
            # End timing
            inference_time = (time.time() - start_time) * 1000  # Convert to ms
            
            # Get predictions
            top3_probs, top3_indices = torch.topk(probabilities, 3)
            top3_probs = top3_probs.cpu().numpy()
            top3_indices = top3_indices.cpu().numpy()
            top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
            
            # Determine confidence level
            top_confidence = float(top3_probs[0])
            if top_confidence >= 0.8:
                confidence_level = "Very High"
                confidence_emoji = "🟢"
            elif top_confidence >= 0.6:
                confidence_level = "High"
                confidence_emoji = "🟡"
            elif top_confidence >= 0.4:
                confidence_level = "Moderate"
                confidence_emoji = "🟠"
            else:
                confidence_level = "Low"
                confidence_emoji = "🔴"
            
            # Check accuracy if ground truth available
            is_correct = None
            if ground_truth:
                is_correct = (top3_phrases[0] == ground_truth)
            
            return {
                'success': True,
                'video_path': video_path,
                'video_name': video_name,
                'ground_truth': ground_truth,
                'model_used': self.active_model_name,
                'tensor_shape': list(video_tensor.shape),
                'value_range': [float(video_tensor.min()), float(video_tensor.max())],
                'tta_used': tta_used,
                'is_pre_cropped': is_pre_cropped,
                'top_prediction': top3_phrases[0],
                'top_confidence': top_confidence,
                'confidence_level': confidence_level,
                'confidence_emoji': confidence_emoji,
                'top3_phrases': top3_phrases,
                'top3_probabilities': top3_probs.tolist(),
                'all_probabilities': probabilities.cpu().numpy().tolist(),
                'is_correct': is_correct,
                'inference_time_ms': inference_time
            }
            
        except Exception as e:
            print(f"   ❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'video_path': video_path
            }
    
    def display_video_result(self, result: Dict):
        """Display formatted result for a single video"""
        
        if not result['success']:
            print(f"❌ {result.get('video_name', 'Unknown')}: {result['error']}")
            return
        
        print(f"\nVideo: {result['video_name']}")
        if result['ground_truth']:
            print(f"Ground Truth: {result['ground_truth'].title()}")
        else:
            print(f"Ground Truth: [Not determinable from filename]")
        
        print(f"Model Used: {result['model_used']}")
        print(f"Processing: {result['tensor_shape']}, range [{result['value_range'][0]:.3f}, {result['value_range'][1]:.3f}], TTA: {'Yes' if result['tta_used'] else 'No'}")
        
        print(f"\n🏆 TOP PREDICTION: \"{result['top_prediction'].title()}\" ({result['top_confidence']:.1%} confidence)")
        print(f"Confidence Level: {result['confidence_emoji']} {result['confidence_level']}")
        
        print(f"\n📊 TOP-3 PREDICTIONS:")
        rank_emojis = ["🥇", "🥈", "🥉"]
        for i, (phrase, prob) in enumerate(zip(result['top3_phrases'], result['top3_probabilities'])):
            print(f"{rank_emojis[i]} {phrase.title()}: {prob:.1%}")
        
        if result['is_correct'] is not None:
            accuracy_status = "✅ CORRECT" if result['is_correct'] else "❌ INCORRECT"
            print(f"\n{accuracy_status}")
        
        print(f"⏱️ Inference Time: {result['inference_time_ms']:.0f}ms")
    
    def generate_summary_report(self, results: List[Dict]) -> Dict:
        """Generate comprehensive summary report"""
        
        successful_results = [r for r in results if r['success']]
        
        if not successful_results:
            return {'error': 'No successful analyses'}
        
        # Calculate metrics
        total_videos = len(successful_results)
        
        # Accuracy calculation
        accuracy_results = [r for r in successful_results if r['is_correct'] is not None]
        if accuracy_results:
            correct_predictions = sum(1 for r in accuracy_results if r['is_correct'])
            accuracy_rate = correct_predictions / len(accuracy_results)
        else:
            accuracy_rate = None
        
        # Confidence statistics
        confidences = [r['top_confidence'] for r in successful_results]
        avg_confidence = np.mean(confidences)
        confidence_std = np.std(confidences)
        
        # Performance statistics
        inference_times = [r['inference_time_ms'] for r in successful_results]
        avg_inference_time = np.mean(inference_times)
        
        # TTA usage
        tta_used_count = sum(1 for r in successful_results if r['tta_used'])
        
        # Model usage
        model_used = successful_results[0]['model_used'] if successful_results else "Unknown"
        
        return {
            'total_videos_analyzed': total_videos,
            'successful_analyses': len(successful_results),
            'accuracy_rate': accuracy_rate,
            'accuracy_count': len(accuracy_results) if accuracy_results else 0,
            'avg_confidence': avg_confidence,
            'confidence_std': confidence_std,
            'avg_inference_time_ms': avg_inference_time,
            'tta_usage_count': tta_used_count,
            'model_used': model_used,
            'confidence_distribution': {
                'very_high': sum(1 for c in confidences if c >= 0.8),
                'high': sum(1 for c in confidences if 0.6 <= c < 0.8),
                'moderate': sum(1 for c in confidences if 0.4 <= c < 0.6),
                'low': sum(1 for c in confidences if c < 0.4)
            }
        }
    
    def display_summary_report(self, summary: Dict):
        """Display formatted summary report"""
        
        if 'error' in summary:
            print(f"❌ Summary Report Error: {summary['error']}")
            return
        
        print(f"\n📊 SUMMARY REPORT - ICU Reference Video Analysis")
        print("=" * 55)
        
        print(f"📈 Analysis Overview:")
        print(f"   Total videos analyzed: {summary['total_videos_analyzed']}")
        print(f"   Successful analyses: {summary['successful_analyses']}")
        print(f"   Model used: {summary['model_used']}")
        
        if summary['accuracy_rate'] is not None:
            print(f"\n🎯 Accuracy Performance:")
            print(f"   Overall accuracy: {summary['accuracy_rate']:.1%}")
            print(f"   Videos with ground truth: {summary['accuracy_count']}")
        else:
            print(f"\n🎯 Accuracy Performance:")
            print(f"   Ground truth not available for accuracy calculation")
        
        print(f"\n📊 Confidence Analysis:")
        print(f"   Average confidence: {summary['avg_confidence']:.1%}")
        print(f"   Confidence std dev: {summary['confidence_std']:.1%}")
        
        dist = summary['confidence_distribution']
        print(f"   Confidence distribution:")
        print(f"     Very High (≥80%): {dist['very_high']} videos")
        print(f"     High (60-79%): {dist['high']} videos")
        print(f"     Moderate (40-59%): {dist['moderate']} videos")
        print(f"     Low (<40%): {dist['low']} videos")
        
        print(f"\n⏱️ Performance Metrics:")
        print(f"   Average inference time: {summary['avg_inference_time_ms']:.0f}ms")
        print(f"   TTA usage: {summary['tta_usage_count']}/{summary['total_videos_analyzed']} videos")
        
        print(f"\n🔧 Technical Specifications:")
        print(f"   Preprocessing: 64 frames, 112×112, z-score normalization")
        print(f"   Model architecture: {summary['model_used']}")
        print(f"   TTA: 3 temporal crops for videos >64 frames")
        print(f"   ICU mouth-cropping: Applied for non-pre-cropped videos")

def main():
    """Main analysis function"""
    
    print("🎯 ICU Reference Video Analysis with LipNet Perfect 10")
    print("=" * 60)
    print("Analyzing 5 randomly selected videos from ICU reference training dataset")
    
    # Initialize analyzer
    analyzer = ICUReferenceVideoAnalyzer()
    
    # Find available videos
    source_path, available_videos = analyzer.find_available_videos()
    
    if not available_videos:
        print("\n❌ No videos found in any source location")
        print("📁 Checked sources:")
        for source in analyzer.video_sources:
            print(f"   - {source}")
        return
    
    # Load models
    lipnet_loaded, mobile3d_loaded = analyzer.load_models()
    
    if not (lipnet_loaded or mobile3d_loaded):
        print("\n❌ No models available for analysis")
        print("💡 Training instructions:")
        print("   LipNet: python lipnet_perfect_10_training.py")
        print("   Mobile3DTiny: python enhanced_perfect_10_training.py")
        return
    
    # Randomly select 5 videos
    random.seed(42)  # For reproducible results
    selected_videos = random.sample(available_videos, min(5, len(available_videos)))
    
    print(f"\n🎲 Randomly Selected Videos ({len(selected_videos)}/5):")
    for i, video in enumerate(selected_videos, 1):
        print(f"   {i}. {Path(video).name}")
    
    # Analyze each video
    results = []
    for video_path in selected_videos:
        result = analyzer.analyze_single_video(video_path, use_tta=True)
        results.append(result)
        analyzer.display_video_result(result)
    
    # Generate and display summary
    summary = analyzer.generate_summary_report(results)
    analyzer.display_summary_report(summary)
    
    # Save results
    output_data = {
        'source_path': source_path,
        'selected_videos': selected_videos,
        'individual_results': results,
        'summary_report': summary,
        'analysis_timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    output_file = "icu_reference_analysis_results.json"
    with open(output_file, 'w') as f:
        json.dump(output_data, f, indent=2, default=str)
    
    print(f"\n💾 Analysis results saved: {output_file}")
    print(f"\n🎉 ICU Reference Video Analysis Complete!")

if __name__ == '__main__':
    main()
