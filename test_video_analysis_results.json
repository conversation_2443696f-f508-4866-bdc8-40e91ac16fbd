{"success": true, "video_path": "/Users/<USER>/Desktop/test 1.webm", "video_shape": [1, 32, 96, 96], "top_prediction": "i feel anxious", "top_confidence": 0.6032909154891968, "top3_phrases": ["i feel anxious", "i need to sit up", "where am i"], "top3_probabilities": [0.6032909154891968, 0.12398062646389008, 0.08265792578458786], "all_probabilities": [0.03419104591012001, 0.6032909154891968, 0.05576435104012489, 0.033181749284267426, 0.12398062646389008, 0.01084212027490139, 0.01463286392390728, 0.03557976335287094, 0.08265792578458786, 0.005878617987036705], "raw_outputs": [-0.2641511559486389, 2.6062846183776855, 0.22501999139785767, -0.2941148281097412, 1.024010419845581, -1.412676453590393, -1.1128450632095337, -0.2243378460407257, 0.6185958981513977, -2.0247929096221924], "analyzer_info": {"model_type": "Perfect10Mobile3DTiny", "num_classes": 10, "perfect_phrases": ["am i getting better", "i feel anxious", "i m confused", "i need to move", "i need to sit up", "i want to phone my family", "what happened to me", "what time is my wife coming", "where am i", "who is with me today"], "preprocessing": {"target_frames": 32, "target_size": [96, 96], "grayscale": true, "normalization": "[-1, 1]"}}}