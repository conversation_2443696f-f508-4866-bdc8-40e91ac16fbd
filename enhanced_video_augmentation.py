#!/usr/bin/env python3
"""
Enhanced video augmentation for Perfect 10 training
Implements comprehensive temporal and spatial augmentations for >90% accuracy
"""

import torch
import torch.nn.functional as F
import numpy as np
import random
import cv2
from typing import Tuple, List, Optional

class EnhancedVideoAugmentation:
    """Enhanced video augmentation with temporal and spatial transformations"""
    
    def __init__(self,
                 # Temporal augmentations
                 temporal_jitter: int = 6,
                 time_warping: float = 0.1,
                 temporal_probability: float = 0.5,
                 
                 # Spatial augmentations
                 random_crops: float = 0.06,
                 random_translations: int = 4,
                 photometric_jitter: float = 0.15,
                 gaussian_blur_sigma: Tuple[float, float] = (0.5, 1.0),
                 gaussian_blur_prob: float = 0.1,
                 
                 # Disabled augmentations
                 horizontal_flip: bool = False):
        
        self.temporal_jitter = temporal_jitter
        self.time_warping = time_warping
        self.temporal_probability = temporal_probability
        
        self.random_crops = random_crops
        self.random_translations = random_translations
        self.photometric_jitter = photometric_jitter
        self.gaussian_blur_sigma = gaussian_blur_sigma
        self.gaussian_blur_prob = gaussian_blur_prob
        
        self.horizontal_flip = horizontal_flip
        
        print(f"🎨 Enhanced Video Augmentation Initialized")
        print(f"   Temporal jitter: ±{temporal_jitter} frames")
        print(f"   Time warping: ±{time_warping*100:.1f}%")
        print(f"   Spatial crops: ±{random_crops*100:.1f}%")
        print(f"   Translations: ±{random_translations}px")
        print(f"   Photometric jitter: ±{photometric_jitter*100:.1f}%")
        print(f"   Gaussian blur: σ={gaussian_blur_sigma}, p={gaussian_blur_prob}")
    
    def apply_temporal_jitter(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random temporal jitter (±6 frames)"""
        if random.random() < self.temporal_probability:
            C, T, H, W = frames.shape
            shift = random.randint(-self.temporal_jitter, self.temporal_jitter)
            
            if shift != 0:
                if shift > 0:
                    # Shift forward in time
                    frames = torch.cat([
                        frames[:, shift:, :, :],
                        frames[:, -shift:, :, :].repeat(1, shift, 1, 1)
                    ], dim=1)
                else:
                    # Shift backward in time
                    shift = abs(shift)
                    frames = torch.cat([
                        frames[:, :shift, :, :].repeat(1, shift, 1, 1),
                        frames[:, :-shift, :, :]
                    ], dim=1)
        
        return frames
    
    def apply_time_warping(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply time warping (0.9×-1.1× playback speed) with shape preservation"""
        if random.random() < self.temporal_probability:
            C, T, H, W = frames.shape

            # Random speed factor
            speed_factor = 1.0 + random.uniform(-self.time_warping, self.time_warping)

            # Calculate new temporal length
            new_T = int(T * speed_factor)

            if new_T != T and new_T > 0:
                # Resample temporal dimension
                frames = F.interpolate(
                    frames.unsqueeze(0),  # Add batch dimension
                    size=(new_T, H, W),
                    mode='trilinear',
                    align_corners=False
                ).squeeze(0)

                # Always ensure we return to original temporal length
                if new_T > T:
                    # Crop from center
                    start_t = (new_T - T) // 2
                    frames = frames[:, start_t:start_t+T, :, :]
                elif new_T < T:
                    # Pad by repeating last frame
                    padding_needed = T - new_T
                    last_frames = frames[:, -1:, :, :].repeat(1, padding_needed, 1, 1)
                    frames = torch.cat([frames, last_frames], dim=1)

            # Ensure exact shape preservation
            assert frames.shape == (C, T, H, W), f"Shape mismatch: expected {(C, T, H, W)}, got {frames.shape}"

        return frames
    
    def apply_random_crops(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random crops (±6% of image size) with shape preservation"""
        if random.random() < 0.5:
            C, T, H, W = frames.shape

            # Calculate crop size
            crop_factor = 1.0 + random.uniform(-self.random_crops, self.random_crops)
            new_H, new_W = int(H * crop_factor), int(W * crop_factor)

            # Ensure minimum size
            new_H = max(new_H, H // 2)
            new_W = max(new_W, W // 2)

            # Resize to new size
            frames = F.interpolate(
                frames.unsqueeze(0),
                size=(T, new_H, new_W),
                mode='trilinear',
                align_corners=False
            ).squeeze(0)

            # Always crop or pad back to exact original size
            if new_H > H or new_W > W:
                # Crop from center
                start_h = max(0, (new_H - H) // 2)
                start_w = max(0, (new_W - W) // 2)
                frames = frames[:, :, start_h:start_h+H, start_w:start_w+W]
            elif new_H < H or new_W < W:
                # Pad to exact size
                pad_h_before = (H - new_H) // 2
                pad_h_after = H - new_H - pad_h_before
                pad_w_before = (W - new_W) // 2
                pad_w_after = W - new_W - pad_w_before
                frames = F.pad(
                    frames,
                    (pad_w_before, pad_w_after, pad_h_before, pad_h_after),
                    mode='replicate'
                )

            # Ensure exact shape preservation
            assert frames.shape == (C, T, H, W), f"Shape mismatch: expected {(C, T, H, W)}, got {frames.shape}"

        return frames
    
    def apply_random_translations(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random translations (±4 pixels) with shape preservation"""
        if random.random() < 0.5:
            C, T, H, W = frames.shape

            # Random translation
            shift_h = random.randint(-self.random_translations, self.random_translations)
            shift_w = random.randint(-self.random_translations, self.random_translations)

            if shift_h != 0 or shift_w != 0:
                # Apply translation using padding and cropping
                pad_h = abs(shift_h)
                pad_w = abs(shift_w)
                frames = F.pad(frames, (pad_w, pad_w, pad_h, pad_h), mode='replicate')

                # Crop to exact original size with offset
                start_h = pad_h + shift_h
                start_w = pad_w + shift_w
                frames = frames[:, :, start_h:start_h+H, start_w:start_w+W]

                # Ensure exact shape preservation
                assert frames.shape == (C, T, H, W), f"Shape mismatch: expected {(C, T, H, W)}, got {frames.shape}"

        return frames
    
    def apply_photometric_jitter(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply photometric jitter (±15% brightness/contrast)"""
        if random.random() < 0.5:
            # Brightness
            brightness_factor = 1.0 + random.uniform(-self.photometric_jitter, self.photometric_jitter)
            frames = frames * brightness_factor
            
            # Contrast
            contrast_factor = 1.0 + random.uniform(-self.photometric_jitter, self.photometric_jitter)
            mean = frames.mean()
            frames = (frames - mean) * contrast_factor + mean
            
            # Clamp to valid range (assuming z-score normalized input)
            frames = torch.clamp(frames, -3.0, 3.0)  # Reasonable range for z-score
        
        return frames
    
    def apply_gaussian_blur(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply Gaussian blur (σ=0.5-1.0, p=0.1)"""
        if random.random() < self.gaussian_blur_prob:
            C, T, H, W = frames.shape
            
            # Random sigma
            sigma = random.uniform(self.gaussian_blur_sigma[0], self.gaussian_blur_sigma[1])
            
            # Apply blur to each frame
            blurred_frames = []
            for t in range(T):
                frame = frames[0, t, :, :].cpu().numpy()  # Assuming single channel
                
                # Convert to uint8 for OpenCV (temporary)
                frame_uint8 = ((frame + 3.0) / 6.0 * 255).astype(np.uint8)  # Assuming z-score range [-3, 3]
                
                # Apply Gaussian blur
                kernel_size = int(6 * sigma + 1)
                if kernel_size % 2 == 0:
                    kernel_size += 1
                
                blurred = cv2.GaussianBlur(frame_uint8, (kernel_size, kernel_size), sigma)
                
                # Convert back to z-score range
                blurred_normalized = (blurred.astype(np.float32) / 255.0 * 6.0) - 3.0
                
                blurred_frames.append(torch.from_numpy(blurred_normalized))
            
            frames = torch.stack(blurred_frames, dim=1).unsqueeze(0)  # Restore shape [C, T, H, W]
        
        return frames
    
    def __call__(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply all enhanced augmentations"""
        
        # Temporal augmentations
        frames = self.apply_temporal_jitter(frames)
        frames = self.apply_time_warping(frames)
        
        # Spatial augmentations
        frames = self.apply_random_crops(frames)
        frames = self.apply_random_translations(frames)
        frames = self.apply_photometric_jitter(frames)
        frames = self.apply_gaussian_blur(frames)
        
        return frames

class WeightedPhrasesSampler:
    """WeightedRandomSampler for equal representation of all 10 Perfect phrases"""
    
    def __init__(self, phrase_labels: List[int], num_classes: int = 10):
        """Initialize weighted sampler for class balancing"""
        
        self.phrase_labels = phrase_labels
        self.num_classes = num_classes
        
        # Calculate class frequencies
        class_counts = torch.bincount(torch.tensor(phrase_labels), minlength=num_classes)
        
        # Calculate weights (inverse frequency)
        class_weights = 1.0 / class_counts.float()
        class_weights[class_counts == 0] = 0.0  # Handle empty classes
        
        # Assign weights to each sample
        self.sample_weights = class_weights[phrase_labels]
        
        print(f"⚖️  Weighted Phrases Sampler Initialized")
        print(f"   Class counts: {class_counts.tolist()}")
        print(f"   Class weights: {class_weights.tolist()}")
        print(f"   Total samples: {len(phrase_labels)}")
    
    def get_sampler(self) -> torch.utils.data.WeightedRandomSampler:
        """Get the WeightedRandomSampler"""
        
        return torch.utils.data.WeightedRandomSampler(
            weights=self.sample_weights,
            num_samples=len(self.sample_weights),
            replacement=True
        )

def create_enhanced_augmentation(config: dict) -> EnhancedVideoAugmentation:
    """Factory function to create enhanced augmentation from config"""
    
    augmentation_config = config.get('augmentation', {})
    
    return EnhancedVideoAugmentation(
        temporal_jitter=augmentation_config.get('temporal_jitter', 6),
        time_warping=augmentation_config.get('time_warping', 0.1),
        temporal_probability=augmentation_config.get('temporal_probability', 0.5),
        random_crops=augmentation_config.get('random_crops', 0.06),
        random_translations=augmentation_config.get('random_translations', 4),
        photometric_jitter=augmentation_config.get('photometric_jitter', 0.15),
        gaussian_blur_sigma=tuple(augmentation_config.get('gaussian_blur_sigma', [0.5, 1.0])),
        gaussian_blur_prob=augmentation_config.get('gaussian_blur_prob', 0.1),
        horizontal_flip=augmentation_config.get('horizontal_flip', False)
    )

def test_enhanced_augmentation():
    """Test the enhanced augmentation pipeline"""
    
    print("🧪 Testing Enhanced Video Augmentation")
    print("=" * 40)
    
    # Create test tensor (64 frames, 112×112)
    test_frames = torch.randn(1, 64, 112, 112)  # [C, T, H, W]
    
    print(f"📊 Input tensor: {test_frames.shape}")
    print(f"   Value range: [{test_frames.min():.3f}, {test_frames.max():.3f}]")
    
    # Create augmentation
    augmentation = EnhancedVideoAugmentation()
    
    # Apply augmentations
    augmented_frames = augmentation(test_frames)
    
    print(f"\n📊 Augmented tensor: {augmented_frames.shape}")
    print(f"   Value range: [{augmented_frames.min():.3f}, {augmented_frames.max():.3f}]")
    
    # Validate shape preservation
    if augmented_frames.shape == test_frames.shape:
        print(f"✅ Shape preservation: PASSED")
    else:
        print(f"❌ Shape preservation: FAILED")
    
    print(f"\n🎉 Enhanced augmentation test complete!")

if __name__ == '__main__':
    test_enhanced_augmentation()
