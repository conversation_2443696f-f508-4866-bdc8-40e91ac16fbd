#!/usr/bin/env python3
"""
Video Inference Testing System for ICU Lipreading Classifier
Evaluates model predictions on sample videos with confidence scores
"""

import torch
import torch.nn.functional as F
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import yaml
from typing import List, Dict, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.model import Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class VideoInferenceTester:
    """System for testing video inference with trained ICU lipreading model"""
    
    def __init__(self, model_path: str, config_path: str):
        """Initialize the inference tester"""
        
        self.model_path = Path(model_path)
        self.config_path = config_path
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # ICU phrases (26 classes)
        self.icu_phrases = [
            "where am i",
            "who is with me today", 
            "what happened to me",
            "am i getting better",
            "please explain again",
            "where is my wife",
            "where is my husband",
            "i want to phone my family",
            "i want to see my wife",
            "i want to see my husband",
            "what time is my wife coming",
            "what time is my husband coming",
            "i feel anxious",
            "stay with me please",
            "my chest hurts",
            "my back hurts",
            "i m confused",
            "i m in pain",
            "i have a headache",
            "i m uncomfortable",
            "i need a medication",
            "i need to lie down",
            "i need to use the toilet",
            "i need to sit up",
            "i need help",
            "i need to move"
        ]
        
        # Create phrase mappings
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.icu_phrases)}
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.icu_phrases)}
        
        # Initialize components
        self.model = None
        self.video_processor = None
        self.device = torch.device('cpu')  # Use CPU for consistency
        
        print(f"🧪 Video Inference Tester Initialized")
        print(f"   Model path: {self.model_path}")
        print(f"   Device: {self.device}")
        print(f"   Classes: {len(self.icu_phrases)}")
    
    def load_model(self) -> bool:
        """Load the trained model from checkpoint"""
        
        print(f"\n🤖 Loading Trained Model")
        print("=" * 25)
        
        if not self.model_path.exists():
            print(f"❌ Model checkpoint not found: {self.model_path}")
            return False
        
        try:
            # Load checkpoint
            checkpoint = torch.load(self.model_path, map_location=self.device)
            
            # Create model
            self.model = Mobile3DTiny(num_classes=len(self.icu_phrases))
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            # Get training info
            best_val_accuracy = checkpoint.get('best_val_accuracy', 0.0)
            epoch = checkpoint.get('epoch', 0)
            
            print(f"✅ Model loaded successfully")
            print(f"   Parameters: {self.model.get_num_parameters():,}")
            print(f"   Training epoch: {epoch}")
            print(f"   Best validation accuracy: {best_val_accuracy:.3f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            return False
    
    def initialize_video_processor(self):
        """Initialize video processor with same settings as training"""
        
        self.video_processor = VideoProcessor(
            target_frames=self.config['data']['frames'],
            target_size=(self.config['data']['height'], self.config['data']['width']),
            grayscale=self.config['data']['grayscale']
        )
        
        print(f"✅ Video processor initialized")
        print(f"   Target frames: {self.config['data']['frames']}")
        print(f"   Target size: {self.config['data']['height']}×{self.config['data']['width']}")
        print(f"   Grayscale: {self.config['data']['grayscale']}")
    
    def predict_video(self, video_path: str) -> Dict:
        """Predict ICU phrase for a single video"""
        
        try:
            # Process video
            video_tensor = self.video_processor.process_video(video_path)
            
            # Add batch dimension
            video_batch = video_tensor.unsqueeze(0).to(self.device)
            
            # Get prediction
            with torch.no_grad():
                outputs = self.model(video_batch)
                probabilities = F.softmax(outputs, dim=1)
                
                # Get top-3 predictions
                top3_probs, top3_indices = torch.topk(probabilities, 3, dim=1)
                
                top3_probs = top3_probs[0].cpu().numpy()
                top3_indices = top3_indices[0].cpu().numpy()
                
                # Convert to phrases
                top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
                
                return {
                    'success': True,
                    'top3_phrases': top3_phrases,
                    'top3_probabilities': top3_probs,
                    'predicted_phrase': top3_phrases[0],
                    'confidence': top3_probs[0],
                    'raw_outputs': outputs[0].cpu().numpy()
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_video_set(self, video_paths: List[str], ground_truth_phrases: List[str]) -> Dict:
        """Test model on a set of videos"""
        
        print(f"\n🧪 Testing Model on Video Set")
        print("=" * 30)
        print(f"📊 Testing {len(video_paths)} videos")
        
        results = []
        correct_predictions = 0
        top3_correct = 0
        
        for i, (video_path, gt_phrase) in enumerate(zip(video_paths, ground_truth_phrases)):
            print(f"\n📹 Video {i+1}/{len(video_paths)}: {Path(video_path).name}")
            print(f"🎯 Ground truth: {gt_phrase.title()}")
            
            # Get prediction
            prediction = self.predict_video(video_path)
            
            if prediction['success']:
                predicted_phrase = prediction['predicted_phrase']
                confidence = prediction['confidence']
                top3_phrases = prediction['top3_phrases']
                top3_probs = prediction['top3_probabilities']
                
                # Check accuracy
                is_correct = predicted_phrase == gt_phrase
                is_top3_correct = gt_phrase in top3_phrases
                
                if is_correct:
                    correct_predictions += 1
                if is_top3_correct:
                    top3_correct += 1
                
                # Display results
                print(f"🔮 Prediction: {predicted_phrase.title()} ({confidence:.1%})")
                print(f"📊 Top-3 predictions:")
                for j, (phrase, prob) in enumerate(zip(top3_phrases, top3_probs)):
                    marker = "✅" if phrase == gt_phrase else "  "
                    print(f"   {j+1}. {marker} {phrase.title()}: {prob:.1%}")
                
                result_marker = "✅ CORRECT" if is_correct else "❌ INCORRECT"
                print(f"📋 Result: {result_marker}")
                
                # Store result
                results.append({
                    'video_path': video_path,
                    'ground_truth': gt_phrase,
                    'predicted': predicted_phrase,
                    'confidence': confidence,
                    'is_correct': is_correct,
                    'is_top3_correct': is_top3_correct,
                    'top3_phrases': top3_phrases,
                    'top3_probabilities': top3_probs.tolist()
                })
                
            else:
                print(f"❌ Prediction failed: {prediction['error']}")
                results.append({
                    'video_path': video_path,
                    'ground_truth': gt_phrase,
                    'predicted': None,
                    'confidence': 0.0,
                    'is_correct': False,
                    'is_top3_correct': False,
                    'error': prediction['error']
                })
        
        # Calculate overall statistics
        total_videos = len(video_paths)
        accuracy = correct_predictions / total_videos if total_videos > 0 else 0
        top3_accuracy = top3_correct / total_videos if total_videos > 0 else 0
        
        print(f"\n📊 Overall Results")
        print("=" * 20)
        print(f"   Total videos tested: {total_videos}")
        print(f"   Correct predictions: {correct_predictions}")
        print(f"   Top-1 accuracy: {accuracy:.1%}")
        print(f"   Top-3 accuracy: {top3_accuracy:.1%}")
        
        return {
            'results': results,
            'total_videos': total_videos,
            'correct_predictions': correct_predictions,
            'top1_accuracy': accuracy,
            'top3_accuracy': top3_accuracy,
            'detailed_results': results
        }
    
    def analyze_errors(self, test_results: Dict):
        """Analyze prediction errors and patterns"""
        
        print(f"\n🔍 Error Analysis")
        print("=" * 20)
        
        results = test_results['detailed_results']
        
        # Confusion analysis
        confusion_data = defaultdict(list)
        phrase_accuracy = defaultdict(lambda: {'correct': 0, 'total': 0})
        
        for result in results:
            if result.get('predicted') is not None:
                gt = result['ground_truth']
                pred = result['predicted']
                
                phrase_accuracy[gt]['total'] += 1
                if result['is_correct']:
                    phrase_accuracy[gt]['correct'] += 1
                else:
                    confusion_data[gt].append(pred)
        
        # Show phrase-wise accuracy
        print(f"📊 Phrase-wise Accuracy:")
        for phrase in sorted(phrase_accuracy.keys()):
            stats = phrase_accuracy[phrase]
            acc = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
            print(f"   {phrase.title()}: {stats['correct']}/{stats['total']} ({acc:.1%})")
        
        # Show common confusions
        print(f"\n🔄 Common Confusions:")
        for gt_phrase, confused_with in confusion_data.items():
            if confused_with:
                most_common = max(set(confused_with), key=confused_with.count)
                count = confused_with.count(most_common)
                print(f"   '{gt_phrase.title()}' → '{most_common.title()}' ({count} times)")
    
    def create_results_visualization(self, test_results: Dict, output_path: str):
        """Create visualization of test results"""
        
        results = test_results['detailed_results']
        
        # Create figure
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. Accuracy summary
        accuracy_data = [
            test_results['top1_accuracy'],
            test_results['top3_accuracy'],
            1/26  # Random baseline
        ]
        accuracy_labels = ['Top-1 Accuracy', 'Top-3 Accuracy', 'Random Baseline']
        
        bars = ax1.bar(accuracy_labels, accuracy_data, color=['#2E8B57', '#4682B4', '#CD5C5C'])
        ax1.set_ylabel('Accuracy')
        ax1.set_title('Model Performance vs Random Baseline')
        ax1.set_ylim(0, max(accuracy_data) * 1.2)
        
        # Add value labels on bars
        for bar, value in zip(bars, accuracy_data):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.1%}', ha='center', va='bottom', fontweight='bold')
        
        # 2. Confidence distribution
        confidences = [r['confidence'] for r in results if r.get('confidence', 0) > 0]
        correct_confidences = [r['confidence'] for r in results if r.get('is_correct', False)]
        incorrect_confidences = [r['confidence'] for r in results if not r.get('is_correct', True) and r.get('confidence', 0) > 0]
        
        ax2.hist(correct_confidences, bins=10, alpha=0.7, label='Correct', color='green')
        ax2.hist(incorrect_confidences, bins=10, alpha=0.7, label='Incorrect', color='red')
        ax2.set_xlabel('Confidence Score')
        ax2.set_ylabel('Count')
        ax2.set_title('Prediction Confidence Distribution')
        ax2.legend()
        
        # 3. Phrase-wise accuracy
        phrase_accuracy = defaultdict(lambda: {'correct': 0, 'total': 0})
        for result in results:
            if result.get('predicted') is not None:
                gt = result['ground_truth']
                phrase_accuracy[gt]['total'] += 1
                if result['is_correct']:
                    phrase_accuracy[gt]['correct'] += 1
        
        phrases = list(phrase_accuracy.keys())
        accuracies = [phrase_accuracy[p]['correct'] / phrase_accuracy[p]['total'] 
                     if phrase_accuracy[p]['total'] > 0 else 0 for p in phrases]
        
        if phrases:
            y_pos = np.arange(len(phrases))
            ax3.barh(y_pos, accuracies)
            ax3.set_yticks(y_pos)
            ax3.set_yticklabels([p.title()[:20] for p in phrases], fontsize=8)
            ax3.set_xlabel('Accuracy')
            ax3.set_title('Per-Phrase Accuracy')
        
        # 4. Summary statistics
        ax4.text(0.1, 0.8, f"Total Videos: {test_results['total_videos']}", fontsize=14)
        ax4.text(0.1, 0.7, f"Correct Predictions: {test_results['correct_predictions']}", fontsize=14)
        ax4.text(0.1, 0.6, f"Top-1 Accuracy: {test_results['top1_accuracy']:.1%}", fontsize=14)
        ax4.text(0.1, 0.5, f"Top-3 Accuracy: {test_results['top3_accuracy']:.1%}", fontsize=14)
        ax4.text(0.1, 0.4, f"Model Parameters: {self.model.get_num_parameters():,}", fontsize=14)
        ax4.text(0.1, 0.3, f"Classes: {len(self.icu_phrases)}", fontsize=14)
        ax4.set_title('Test Summary')
        ax4.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 Results visualization saved: {output_path}")

def load_test_videos(manifest_path: str) -> Tuple[List[str], List[str]]:
    """Load test videos from the training manifest"""

    print(f"\n📋 Loading Test Videos")
    print("=" * 25)

    # Load manifest
    manifest_df = pd.read_csv(manifest_path)

    # For now, use all videos as test set since we don't have split info
    # In a real scenario, we'd use the actual test split
    video_paths = manifest_df['video_path'].tolist()
    phrases = manifest_df['phrase'].tolist()

    # Verify files exist
    existing_paths = []
    existing_phrases = []

    for path, phrase in zip(video_paths, phrases):
        if Path(path).exists():
            existing_paths.append(path)
            existing_phrases.append(phrase)
        else:
            print(f"⚠️  Video not found: {path}")

    print(f"✅ Found {len(existing_paths)} test videos")

    # Show phrase distribution
    phrase_counts = {}
    for phrase in existing_phrases:
        phrase_counts[phrase] = phrase_counts.get(phrase, 0) + 1

    print(f"📊 Test set phrase distribution:")
    for phrase, count in sorted(phrase_counts.items()):
        print(f"   {phrase.title()}: {count} videos")

    return existing_paths, existing_phrases

def run_comprehensive_test():
    """Run comprehensive testing on available videos"""

    print("🧪 ICU Lipreading Video Inference Testing System")
    print("=" * 55)

    # Configuration
    model_path = "checkpoints/reference_training/best_model.pth"
    config_path = "configs/reference_training.yaml"
    manifest_path = "reference_videos_manifest_fixed.csv"

    # Initialize tester
    tester = VideoInferenceTester(model_path, config_path)

    # Load model
    if not tester.load_model():
        print("❌ Failed to load model. Make sure training has produced a checkpoint.")
        return

    # Initialize video processor
    tester.initialize_video_processor()

    # Load test videos
    video_paths, ground_truth_phrases = load_test_videos(manifest_path)

    if not video_paths:
        print("❌ No test videos found")
        return

    # Run testing
    test_results = tester.test_video_set(video_paths, ground_truth_phrases)

    # Analyze errors
    tester.analyze_errors(test_results)

    # Create visualization
    output_path = "inference_test_results.png"
    tester.create_results_visualization(test_results, output_path)

    # Save detailed results
    results_path = "inference_test_results.json"
    import json
    with open(results_path, 'w') as f:
        json.dump(test_results, f, indent=2, default=str)

    print(f"\n💾 Results saved:")
    print(f"   Visualization: {output_path}")
    print(f"   Detailed results: {results_path}")

    return test_results

def test_single_video(video_path: str, expected_phrase: str = None):
    """Test inference on a single video"""

    print(f"🧪 Single Video Inference Test")
    print("=" * 35)

    # Configuration
    model_path = "checkpoints/reference_training/best_model.pth"
    config_path = "configs/reference_training.yaml"

    # Initialize tester
    tester = VideoInferenceTester(model_path, config_path)

    # Load model
    if not tester.load_model():
        print("❌ Failed to load model")
        return

    # Initialize video processor
    tester.initialize_video_processor()

    # Test video
    print(f"\n📹 Testing video: {Path(video_path).name}")
    if expected_phrase:
        print(f"🎯 Expected phrase: {expected_phrase.title()}")

    prediction = tester.predict_video(video_path)

    if prediction['success']:
        print(f"\n🔮 Prediction Results:")
        print(f"   Top prediction: {prediction['predicted_phrase'].title()}")
        print(f"   Confidence: {prediction['confidence']:.1%}")

        print(f"\n📊 Top-3 predictions:")
        for i, (phrase, prob) in enumerate(zip(prediction['top3_phrases'], prediction['top3_probabilities'])):
            marker = "🎯" if expected_phrase and phrase == expected_phrase else "  "
            print(f"   {i+1}. {marker} {phrase.title()}: {prob:.1%}")

        if expected_phrase:
            is_correct = prediction['predicted_phrase'] == expected_phrase
            is_top3 = expected_phrase in prediction['top3_phrases']

            print(f"\n📋 Accuracy:")
            print(f"   Top-1: {'✅ CORRECT' if is_correct else '❌ INCORRECT'}")
            print(f"   Top-3: {'✅ CORRECT' if is_top3 else '❌ INCORRECT'}")

    else:
        print(f"❌ Prediction failed: {prediction['error']}")

def main():
    """Main testing function"""

    import sys

    if len(sys.argv) > 1:
        # Test single video
        video_path = sys.argv[1]
        expected_phrase = sys.argv[2] if len(sys.argv) > 2 else None
        test_single_video(video_path, expected_phrase)
    else:
        # Run comprehensive test
        run_comprehensive_test()

if __name__ == '__main__':
    main()
