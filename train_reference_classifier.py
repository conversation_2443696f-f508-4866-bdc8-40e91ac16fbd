#!/usr/bin/env python3
"""
Train ICU lipreading classifier on reference videos
Baseline training with 80 mouth-cropped reference videos
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
import seaborn as sns
from tqdm import tqdm
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.model import Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor
from reference_data_loader import create_reference_dataloaders

class ReferenceVideoTrainer:
    """Trainer for ICU lipreading classifier using reference videos"""
    
    def __init__(self, config_path: str):
        """Initialize trainer with configuration"""
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Set random seed for reproducibility
        self.set_seed(self.config.get('seed', 42))
        
        # Setup device
        self.device = self.setup_device()
        
        # Create directories
        self.setup_directories()
        
        # Initialize components
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        
        # Training state
        self.current_epoch = 0
        self.best_val_accuracy = 0.0
        self.training_history = {
            'train_loss': [],
            'train_accuracy': [],
            'val_loss': [],
            'val_accuracy': [],
            'learning_rate': []
        }
        
        print(f"🎯 Reference Video Trainer Initialized")
        print(f"   Device: {self.device}")
        print(f"   Config: {config_path}")
    
    def set_seed(self, seed: int):
        """Set random seed for reproducibility"""
        torch.manual_seed(seed)
        np.random.seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
    
    def setup_device(self):
        """Setup training device"""
        if torch.cuda.is_available():
            device = torch.device('cuda')
            print(f"🚀 Using GPU: {torch.cuda.get_device_name()}")
        else:
            # Use CPU for compatibility with 3D operations
            device = torch.device('cpu')
            print(f"💻 Using CPU (for 3D operation compatibility)")

        return device
    
    def setup_directories(self):
        """Create necessary directories"""
        
        # Checkpoints directory
        self.checkpoint_dir = Path(self.config['checkpoints']['save_dir'])
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # Logs directory
        self.log_dir = Path(self.config['logging']['log_dir'])
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Directories created:")
        print(f"   Checkpoints: {self.checkpoint_dir}")
        print(f"   Logs: {self.log_dir}")
    
    def prepare_data(self):
        """Prepare data loaders for training"""
        
        print(f"\n📊 Preparing Reference Video Dataset")
        print("=" * 40)
        
        # Check if manifest exists
        manifest_path = self.config['data']['manifest_path']
        if not Path(manifest_path).exists():
            raise FileNotFoundError(f"Manifest not found: {manifest_path}")
        
        # Load and analyze manifest
        manifest_df = pd.read_csv(manifest_path)
        print(f"📋 Loaded manifest: {len(manifest_df)} videos")
        
        # Show phrase distribution
        phrase_counts = manifest_df['phrase'].value_counts()
        print(f"📊 Phrase distribution:")
        for phrase, count in phrase_counts.items():
            print(f"   {phrase.title()}: {count} videos")
        
        # Create data loaders for reference videos
        try:
            train_loader, val_loader, test_loader, data_info = create_reference_dataloaders(
                self.config, manifest_path
            )
            
            self.train_loader = train_loader
            self.val_loader = val_loader
            self.test_loader = test_loader
            self.data_info = data_info
            
            print(f"\n✅ Data loaders created:")
            print(f"   Train: {data_info['train_size']} videos")
            print(f"   Validation: {data_info['val_size']} videos")
            print(f"   Test: {data_info['test_size']} videos")
            print(f"   Classes: {data_info['num_classes']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create data loaders: {e}")
            return False
    
    def initialize_model(self):
        """Initialize model, optimizer, and loss function"""
        
        print(f"\n🤖 Initializing Model")
        print("=" * 25)
        
        # Create model
        self.model = Mobile3DTiny(num_classes=self.config['model']['num_classes'])
        self.model.to(self.device)
        
        print(f"✅ Model: {self.model.__class__.__name__}")
        print(f"   Parameters: {self.model.get_num_parameters():,}")
        print(f"   Classes: {self.config['model']['num_classes']}")
        
        # Initialize optimizer
        optimizer_name = self.config['training']['optimizer']
        lr = self.config['training']['learning_rate']
        weight_decay = self.config['training']['weight_decay']
        
        if optimizer_name == 'AdamW':
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=lr,
                weight_decay=weight_decay
            )
        else:
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=lr,
                weight_decay=weight_decay
            )
        
        # Initialize scheduler
        scheduler_name = self.config['training']['scheduler']
        num_epochs = self.config['training']['num_epochs']
        
        if scheduler_name == 'CosineAnnealingLR':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=num_epochs
            )
        else:
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, step_size=30, gamma=0.1
            )
        
        # Initialize loss function with label smoothing
        label_smoothing = self.config['regularization'].get('label_smoothing', 0.0)
        self.criterion = nn.CrossEntropyLoss(label_smoothing=label_smoothing)
        
        print(f"✅ Optimizer: {optimizer_name} (lr={lr})")
        print(f"✅ Scheduler: {scheduler_name}")
        print(f"✅ Loss: CrossEntropyLoss (smoothing={label_smoothing})")
    
    def train_epoch(self):
        """Train for one epoch"""
        
        self.model.train()
        total_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        progress_bar = tqdm(self.train_loader, desc=f"Epoch {self.current_epoch+1}")
        
        for batch_idx, (videos, labels, metadata) in enumerate(progress_bar):
            # Move data to device
            videos = videos.to(self.device)
            labels = labels.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(videos)
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            if self.config['training'].get('grad_clip_norm'):
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config['training']['grad_clip_norm']
                )
            
            self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_samples += labels.size(0)
            correct_predictions += (predicted == labels).sum().item()
            
            # Update progress bar
            current_accuracy = correct_predictions / total_samples
            progress_bar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'Acc': f"{current_accuracy:.3f}"
            })
        
        # Calculate epoch metrics
        epoch_loss = total_loss / len(self.train_loader)
        epoch_accuracy = correct_predictions / total_samples
        
        return epoch_loss, epoch_accuracy
    
    def validate_epoch(self):
        """Validate for one epoch"""
        
        self.model.eval()
        total_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        with torch.no_grad():
            for videos, labels, metadata in self.val_loader:
                # Move data to device
                videos = videos.to(self.device)
                labels = labels.to(self.device)
                
                # Forward pass
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                
                # Statistics
                total_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total_samples += labels.size(0)
                correct_predictions += (predicted == labels).sum().item()
        
        # Calculate epoch metrics
        epoch_loss = total_loss / len(self.val_loader)
        epoch_accuracy = correct_predictions / total_samples
        
        return epoch_loss, epoch_accuracy

    def save_checkpoint(self, epoch: int, is_best: bool = False):
        """Save model checkpoint"""

        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_accuracy': self.best_val_accuracy,
            'training_history': self.training_history,
            'config': self.config
        }

        # Save regular checkpoint
        checkpoint_path = self.checkpoint_dir / f"checkpoint_epoch_{epoch:03d}.pth"
        torch.save(checkpoint, checkpoint_path)

        # Save best model
        if is_best:
            best_path = self.checkpoint_dir / "best_model.pth"
            torch.save(checkpoint, best_path)
            print(f"💾 Best model saved: {best_path}")

    def plot_training_curves(self):
        """Plot and save training curves"""

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        epochs = range(1, len(self.training_history['train_loss']) + 1)

        # Loss curves
        ax1.plot(epochs, self.training_history['train_loss'], 'b-', label='Train Loss')
        ax1.plot(epochs, self.training_history['val_loss'], 'r-', label='Val Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)

        # Accuracy curves
        ax2.plot(epochs, self.training_history['train_accuracy'], 'b-', label='Train Accuracy')
        ax2.plot(epochs, self.training_history['val_accuracy'], 'r-', label='Val Accuracy')
        ax2.set_title('Training and Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True)

        # Learning rate
        ax3.plot(epochs, self.training_history['learning_rate'], 'g-')
        ax3.set_title('Learning Rate Schedule')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.grid(True)

        # Best metrics summary
        ax4.text(0.1, 0.8, f"Best Val Accuracy: {self.best_val_accuracy:.3f}", fontsize=14)
        ax4.text(0.1, 0.6, f"Final Train Accuracy: {self.training_history['train_accuracy'][-1]:.3f}", fontsize=14)
        ax4.text(0.1, 0.4, f"Total Epochs: {len(epochs)}", fontsize=14)
        ax4.text(0.1, 0.2, f"Model Parameters: {self.model.get_num_parameters():,}", fontsize=14)
        ax4.set_title('Training Summary')
        ax4.axis('off')

        plt.tight_layout()

        # Save plot
        plot_path = self.log_dir / "training_curves.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 Training curves saved: {plot_path}")

    def evaluate_model(self):
        """Evaluate model on test set"""

        print(f"\n🧪 Evaluating Model on Test Set")
        print("=" * 35)

        self.model.eval()
        all_predictions = []
        all_labels = []
        all_phrases = []

        with torch.no_grad():
            for videos, labels, metadata in self.test_loader:
                videos = videos.to(self.device)
                labels = labels.to(self.device)

                outputs = self.model(videos)
                _, predicted = torch.max(outputs, 1)

                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

                # Get phrase names from metadata
                for meta in metadata:
                    all_phrases.append(meta.get('phrase', 'unknown'))

        # Calculate metrics
        accuracy = accuracy_score(all_labels, all_predictions)
        f1 = f1_score(all_labels, all_predictions, average='weighted')

        print(f"📊 Test Results:")
        print(f"   Accuracy: {accuracy:.3f}")
        print(f"   F1-Score: {f1:.3f}")

        # Create confusion matrix
        cm = confusion_matrix(all_labels, all_predictions)

        # Plot confusion matrix
        plt.figure(figsize=(12, 10))
        phrase_names = self.config['phrases']
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=[p.title()[:15] for p in phrase_names],
                   yticklabels=[p.title()[:15] for p in phrase_names])
        plt.title('Confusion Matrix - Test Set')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()

        # Save confusion matrix
        cm_path = self.log_dir / "confusion_matrix.png"
        plt.savefig(cm_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 Confusion matrix saved: {cm_path}")

        return {
            'accuracy': accuracy,
            'f1_score': f1,
            'predictions': all_predictions,
            'labels': all_labels,
            'phrases': all_phrases
        }

    def train(self):
        """Main training loop"""

        print(f"\n🚀 Starting Training Loop")
        print("=" * 30)

        num_epochs = self.config['training']['num_epochs']
        val_every_n_epochs = self.config['training']['val_every_n_epochs']
        save_every_n_epochs = self.config['training']['save_every_n_epochs']

        # Early stopping setup
        early_stopping_enabled = self.config['training']['early_stopping']['enabled']
        patience = self.config['training']['early_stopping']['patience']
        min_delta = self.config['training']['early_stopping']['min_delta']
        patience_counter = 0

        for epoch in range(num_epochs):
            self.current_epoch = epoch

            # Training phase
            train_loss, train_accuracy = self.train_epoch()

            # Update learning rate
            self.scheduler.step()
            current_lr = self.optimizer.param_groups[0]['lr']

            # Store training metrics
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_accuracy'].append(train_accuracy)
            self.training_history['learning_rate'].append(current_lr)

            # Validation phase
            if (epoch + 1) % val_every_n_epochs == 0:
                val_loss, val_accuracy = self.validate_epoch()

                self.training_history['val_loss'].append(val_loss)
                self.training_history['val_accuracy'].append(val_accuracy)

                # Check for best model
                is_best = val_accuracy > self.best_val_accuracy + min_delta
                if is_best:
                    self.best_val_accuracy = val_accuracy
                    patience_counter = 0
                else:
                    patience_counter += 1

                print(f"Epoch {epoch+1:3d}/{num_epochs} | "
                      f"Train: {train_loss:.4f}/{train_accuracy:.3f} | "
                      f"Val: {val_loss:.4f}/{val_accuracy:.3f} | "
                      f"LR: {current_lr:.6f} | "
                      f"Best: {self.best_val_accuracy:.3f}")

                # Save checkpoint
                if (epoch + 1) % save_every_n_epochs == 0 or is_best:
                    self.save_checkpoint(epoch + 1, is_best)

                # Early stopping check
                if early_stopping_enabled and patience_counter >= patience:
                    print(f"🛑 Early stopping triggered after {patience} epochs without improvement")
                    break
            else:
                print(f"Epoch {epoch+1:3d}/{num_epochs} | "
                      f"Train: {train_loss:.4f}/{train_accuracy:.3f} | "
                      f"LR: {current_lr:.6f}")

        print(f"\n✅ Training completed!")
        print(f"🎯 Best validation accuracy: {self.best_val_accuracy:.3f}")

        # Plot training curves
        self.plot_training_curves()

        # Final evaluation
        test_results = self.evaluate_model()

        # Save training report
        self.save_training_report(test_results)

        return test_results

    def save_training_report(self, test_results):
        """Save comprehensive training report"""

        report = {
            'experiment': self.config['experiment'],
            'model': {
                'name': self.config['model']['name'],
                'parameters': self.model.get_num_parameters(),
                'num_classes': self.config['model']['num_classes']
            },
            'dataset': {
                'train_size': self.data_info['train_size'],
                'val_size': self.data_info['val_size'],
                'test_size': self.data_info['test_size'],
                'total_videos': self.data_info['train_size'] + self.data_info['val_size'] + self.data_info['test_size']
            },
            'training': {
                'epochs_completed': len(self.training_history['train_loss']),
                'best_val_accuracy': self.best_val_accuracy,
                'final_train_accuracy': self.training_history['train_accuracy'][-1],
                'final_train_loss': self.training_history['train_loss'][-1]
            },
            'test_results': test_results,
            'config': self.config,
            'timestamp': datetime.now().isoformat()
        }

        # Save report
        report_path = self.log_dir / "training_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        print(f"📋 Training report saved: {report_path}")

def main():
    """Main training function"""
    
    print("🎬 ICU Lipreading Reference Video Training")
    print("=" * 50)
    
    # Initialize trainer
    config_path = "configs/reference_training.yaml"
    trainer = ReferenceVideoTrainer(config_path)
    
    # Prepare data
    if not trainer.prepare_data():
        print("❌ Data preparation failed. Exiting.")
        return
    
    # Initialize model
    trainer.initialize_model()
    
    print(f"\n🚀 Starting Training")
    print("=" * 20)
    print(f"📊 Dataset: {trainer.data_info['train_size']} train, {trainer.data_info['val_size']} val")
    print(f"🎯 Target: 26 ICU phrases")
    print(f"⏱️  Epochs: {trainer.config['training']['num_epochs']}")
    
    # Start training
    try:
        test_results = trainer.train()

        print(f"\n🎉 Training Complete!")
        print("=" * 25)
        print(f"✅ Best validation accuracy: {trainer.best_val_accuracy:.3f}")
        print(f"✅ Test accuracy: {test_results['accuracy']:.3f}")
        print(f"✅ Test F1-score: {test_results['f1_score']:.3f}")
        print(f"📁 Results saved in: {trainer.log_dir}")

    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
