#!/usr/bin/env python3
"""
Focused Mobile3DTiny model for 13-class ICU phrase classification
Implements transfer learning from 26-class baseline model
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from pathlib import Path
import sys
import json

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.model import Mobile3DTiny

class FocusedMobile3DTiny(nn.Module):
    """
    Focused version of Mobile3DTiny for 13 high-performing ICU phrases
    Supports transfer learning from 26-class baseline model
    """
    
    def __init__(self, num_classes=13, pretrained_26_class_path=None):
        super(FocusedMobile3DTiny, self).__init__()
        
        self.num_classes = num_classes
        
        # Create base 26-class model first
        self.base_model = Mobile3DTiny(num_classes=26)
        
        # Load pretrained weights if provided
        if pretrained_26_class_path and Path(pretrained_26_class_path).exists():
            self.load_pretrained_weights(pretrained_26_class_path)
            print(f"✅ Loaded pretrained weights from: {pretrained_26_class_path}")
        
        # Replace final classifier for 13 classes
        # Get the feature dimension from the original classifier
        if hasattr(self.base_model, 'classifier'):
            in_features = self.base_model.classifier.in_features
        elif hasattr(self.base_model, 'fc'):
            in_features = self.base_model.fc.in_features
        else:
            # Default feature dimension for Mobile3DTiny
            in_features = 512
        
        # Create new classifier for 13 classes
        self.focused_classifier = nn.Sequential(
            nn.Dropout(0.3),
            nn.Linear(in_features, num_classes)
        )
        
        # Replace the original classifier
        if hasattr(self.base_model, 'classifier'):
            self.base_model.classifier = self.focused_classifier
        elif hasattr(self.base_model, 'fc'):
            self.base_model.fc = self.focused_classifier
        
        print(f"🎯 FocusedMobile3DTiny initialized for {num_classes} classes")
    
    def load_pretrained_weights(self, checkpoint_path: str):
        """Load pretrained weights from 26-class baseline model"""
        
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            
            # Extract model state dict
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint
            
            # Load weights into base model (excluding final classifier)
            model_dict = self.base_model.state_dict()
            
            # Filter out classifier weights (we'll replace this layer)
            pretrained_dict = {}
            for k, v in state_dict.items():
                if 'classifier' not in k and 'fc' not in k:
                    if k in model_dict and model_dict[k].shape == v.shape:
                        pretrained_dict[k] = v
                        print(f"   Loaded: {k} {v.shape}")
                    else:
                        print(f"   Skipped: {k} (shape mismatch or not found)")
            
            # Update model dict
            model_dict.update(pretrained_dict)
            self.base_model.load_state_dict(model_dict, strict=False)
            
            print(f"✅ Transfer learning: Loaded {len(pretrained_dict)} layers")
            
        except Exception as e:
            print(f"⚠️  Failed to load pretrained weights: {e}")
    
    def forward(self, x):
        """Forward pass through the focused model"""
        return self.base_model(x)
    
    def get_num_parameters(self):
        """Get total number of parameters"""
        return sum(p.numel() for p in self.parameters())
    
    def freeze_backbone(self, freeze=True):
        """Freeze/unfreeze backbone layers for fine-tuning"""
        
        for name, param in self.base_model.named_parameters():
            if 'classifier' not in name and 'fc' not in name:
                param.requires_grad = not freeze
        
        status = "frozen" if freeze else "unfrozen"
        print(f"🔒 Backbone layers {status}")
    
    def get_feature_extractor(self):
        """Get feature extractor (without classifier)"""
        
        # Create a copy of the model without the final classifier
        feature_extractor = Mobile3DTiny(num_classes=26)
        
        # Copy weights except classifier
        model_dict = feature_extractor.state_dict()
        base_dict = self.base_model.state_dict()
        
        for k, v in base_dict.items():
            if 'classifier' not in k and 'fc' not in k and k in model_dict:
                model_dict[k] = v
        
        feature_extractor.load_state_dict(model_dict, strict=False)
        
        # Remove classifier
        if hasattr(feature_extractor, 'classifier'):
            feature_extractor.classifier = nn.Identity()
        elif hasattr(feature_extractor, 'fc'):
            feature_extractor.fc = nn.Identity()
        
        return feature_extractor

class FocusedModelManager:
    """Manager for focused model operations"""
    
    def __init__(self, baseline_model_path: str, focused_phrases_path: str):
        """Initialize model manager"""
        
        self.baseline_model_path = baseline_model_path
        self.focused_phrases_path = focused_phrases_path
        
        # Load focused phrases info
        with open(focused_phrases_path, 'r') as f:
            self.focused_info = json.load(f)
        
        self.target_phrases = self.focused_info['target_phrases']
        self.phrase_to_idx = self.focused_info['phrase_to_idx']
        self.num_classes = len(self.target_phrases)
        
        print(f"🎯 Focused Model Manager Initialized")
        print(f"   Target phrases: {self.num_classes}")
        print(f"   Baseline model: {baseline_model_path}")
    
    def create_focused_model(self) -> FocusedMobile3DTiny:
        """Create focused model with transfer learning"""
        
        print(f"\n🤖 Creating Focused Model")
        print("=" * 25)
        
        # Create model with transfer learning
        model = FocusedMobile3DTiny(
            num_classes=self.num_classes,
            pretrained_26_class_path=self.baseline_model_path
        )
        
        print(f"✅ Model created with {model.get_num_parameters():,} parameters")
        
        return model
    
    def create_class_mapping(self):
        """Create mapping from 26-class to 13-class indices"""
        
        # Load baseline model info to get original phrase mapping
        baseline_phrases = [
            "where am i", "who is with me today", "what happened to me",
            "am i getting better", "please explain again", "where is my wife",
            "where is my husband", "i want to phone my family", "i want to see my wife",
            "i want to see my husband", "what time is my wife coming",
            "what time is my husband coming", "i feel anxious", "stay with me please",
            "my chest hurts", "my back hurts", "i m confused", "i m in pain",
            "i have a headache", "i m uncomfortable", "i need a medication",
            "i need to lie down", "i need to use the toilet", "i need to sit up",
            "i need help", "i need to move"
        ]
        
        baseline_phrase_to_idx = {phrase: idx for idx, phrase in enumerate(baseline_phrases)}
        
        # Create mapping from baseline indices to focused indices
        baseline_to_focused = {}
        focused_to_baseline = {}
        
        for focused_phrase, focused_idx in self.phrase_to_idx.items():
            if focused_phrase in baseline_phrase_to_idx:
                baseline_idx = baseline_phrase_to_idx[focused_phrase]
                baseline_to_focused[baseline_idx] = focused_idx
                focused_to_baseline[focused_idx] = baseline_idx
        
        return baseline_to_focused, focused_to_baseline
    
    def save_focused_model(self, model: FocusedMobile3DTiny, 
                          save_path: str, epoch: int = 0, 
                          best_accuracy: float = 0.0):
        """Save focused model checkpoint"""
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'num_classes': self.num_classes,
            'target_phrases': self.target_phrases,
            'phrase_to_idx': self.phrase_to_idx,
            'best_accuracy': best_accuracy,
            'model_info': {
                'name': 'FocusedMobile3DTiny',
                'parameters': model.get_num_parameters(),
                'baseline_model': self.baseline_model_path,
                'transfer_learning': True
            }
        }
        
        torch.save(checkpoint, save_path)
        print(f"💾 Focused model saved: {save_path}")

def test_focused_model():
    """Test the focused model creation and transfer learning"""
    
    print("🧪 Testing Focused Model Creation")
    print("=" * 35)
    
    # Paths
    baseline_model_path = "checkpoints/reference_training/best_model.pth"
    focused_summary_path = "focused_13_phrases_summary.json"
    
    # Check if files exist
    if not Path(baseline_model_path).exists():
        print(f"⚠️  Baseline model not found: {baseline_model_path}")
        return False
    
    if not Path(focused_summary_path).exists():
        print(f"⚠️  Focused summary not found: {focused_summary_path}")
        return False
    
    try:
        # Create model manager
        manager = FocusedModelManager(baseline_model_path, focused_summary_path)
        
        # Create focused model
        model = manager.create_focused_model()
        
        # Test forward pass
        batch_size = 2
        frames = 32
        height = 96
        width = 96
        
        dummy_input = torch.randn(batch_size, 1, frames, height, width)
        
        model.eval()
        with torch.no_grad():
            output = model(dummy_input)
        
        print(f"✅ Forward pass test:")
        print(f"   Input shape: {dummy_input.shape}")
        print(f"   Output shape: {output.shape}")
        print(f"   Expected classes: {manager.num_classes}")
        
        # Test class mapping
        baseline_to_focused, focused_to_baseline = manager.create_class_mapping()
        print(f"✅ Class mapping created:")
        print(f"   Baseline→Focused mappings: {len(baseline_to_focused)}")
        print(f"   Focused→Baseline mappings: {len(focused_to_baseline)}")
        
        # Test model saving
        test_save_path = "test_focused_model.pth"
        manager.save_focused_model(model, test_save_path, epoch=0, best_accuracy=0.0)
        
        # Clean up test file
        Path(test_save_path).unlink()
        
        print(f"\n🎉 Focused Model Test Successful!")
        return True
        
    except Exception as e:
        print(f"❌ Focused model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    
    # Test focused model
    success = test_focused_model()
    
    if success:
        print(f"\n🚀 Ready for Focused Training!")
        print("=" * 30)
        print("✅ Focused model architecture ready")
        print("✅ Transfer learning implemented")
        print("✅ 13-class classification configured")
        print("✅ Class mapping created")
    else:
        print(f"\n❌ Focused model setup failed")

if __name__ == '__main__':
    main()
