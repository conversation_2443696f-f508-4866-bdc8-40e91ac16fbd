#!/usr/bin/env python3
"""
Create filtered dataset for 13 high-performing ICU phrases
Combines reference videos with larger training dataset
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import shutil
from typing import List, Dict
import os

class FocusedDatasetCreator:
    """Creates focused dataset for 13 high-performing phrases"""
    
    def __init__(self):
        """Initialize the dataset creator"""
        
        # 13 high-performing phrases from baseline testing
        self.target_phrases = [
            # Perfect Performance Group (100% accuracy)
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today",
            
            # Strong Performance Group (66-67% accuracy)
            "i have a headache",
            "my back hurts",
            "stay with me please"
        ]
        
        # Create phrase mapping for 13 classes
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.target_phrases)}
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.target_phrases)}
        
        print(f"🎯 Focused Dataset Creator Initialized")
        print(f"   Target phrases: {len(self.target_phrases)}")
        print(f"   Focus: High-performing phrases from baseline testing")
    
    def load_reference_videos(self, manifest_path: str) -> pd.DataFrame:
        """Load reference videos and filter for target phrases"""
        
        print(f"\n📋 Loading Reference Videos")
        print("=" * 30)
        
        # Load reference manifest
        ref_df = pd.read_csv(manifest_path)
        print(f"📊 Total reference videos: {len(ref_df)}")
        
        # Filter for target phrases
        filtered_ref = ref_df[ref_df['phrase'].isin(self.target_phrases)].copy()
        print(f"📊 Filtered reference videos: {len(filtered_ref)}")
        
        # Show distribution
        phrase_counts = filtered_ref['phrase'].value_counts()
        print(f"📊 Reference phrase distribution:")
        for phrase, count in sorted(phrase_counts.items()):
            print(f"   {phrase.title()}: {count} videos")
        
        # Add source column
        filtered_ref['source'] = 'reference'
        
        return filtered_ref
    
    def find_larger_dataset_videos(self, dataset_root: str) -> pd.DataFrame:
        """Find videos from larger dataset for target phrases"""
        
        print(f"\n🔍 Searching Larger Dataset")
        print("=" * 30)
        
        dataset_path = Path(dataset_root)
        if not dataset_path.exists():
            print(f"⚠️  Dataset path not found: {dataset_path}")
            return pd.DataFrame()
        
        # Look for processed videos directory
        processed_dirs = [
            dataset_path / "processed_videos",
            dataset_path / "mouth_cropped_videos", 
            dataset_path / "videos",
            dataset_path
        ]
        
        video_files = []
        for proc_dir in processed_dirs:
            if proc_dir.exists():
                print(f"📁 Searching: {proc_dir}")
                
                # Find video files
                for ext in ['*.webm', '*.mp4', '*.avi', '*.mov']:
                    video_files.extend(list(proc_dir.glob(f"**/{ext}")))
                
                if video_files:
                    print(f"✅ Found {len(video_files)} video files")
                    break
        
        if not video_files:
            print(f"⚠️  No video files found in larger dataset")
            return pd.DataFrame()
        
        # Extract phrase information from filenames
        larger_dataset_videos = []
        
        for video_file in video_files:
            filename = video_file.name.lower()
            
            # Try to match target phrases in filename
            matched_phrase = None
            for phrase in self.target_phrases:
                # Convert phrase to filename format
                phrase_filename = phrase.replace(" ", "_")
                
                if phrase_filename in filename:
                    matched_phrase = phrase
                    break
            
            if matched_phrase:
                larger_dataset_videos.append({
                    'video_path': str(video_file),
                    'phrase': matched_phrase,
                    'source': 'larger_dataset',
                    'filename': video_file.name
                })
        
        # Create DataFrame
        larger_df = pd.DataFrame(larger_dataset_videos)
        
        if len(larger_df) > 0:
            print(f"📊 Matched videos from larger dataset: {len(larger_df)}")
            
            # Show distribution
            phrase_counts = larger_df['phrase'].value_counts()
            print(f"📊 Larger dataset phrase distribution:")
            for phrase, count in sorted(phrase_counts.items()):
                print(f"   {phrase.title()}: {count} videos")
        else:
            print(f"⚠️  No matching videos found in larger dataset")
        
        return larger_df
    
    def create_combined_manifest(self, reference_df: pd.DataFrame, 
                               larger_df: pd.DataFrame, 
                               output_path: str) -> pd.DataFrame:
        """Combine reference and larger dataset videos"""
        
        print(f"\n🔗 Creating Combined Dataset")
        print("=" * 30)
        
        # Combine datasets
        combined_df = pd.concat([reference_df, larger_df], ignore_index=True)
        
        # Verify all videos exist
        existing_videos = []
        missing_count = 0
        
        for _, row in combined_df.iterrows():
            if Path(row['video_path']).exists():
                existing_videos.append(row)
            else:
                missing_count += 1
                print(f"⚠️  Missing: {row['video_path']}")
        
        # Create final DataFrame
        final_df = pd.DataFrame(existing_videos)
        
        print(f"📊 Combined dataset statistics:")
        print(f"   Total videos: {len(final_df)}")
        print(f"   Reference videos: {len(final_df[final_df['source'] == 'reference'])}")
        print(f"   Larger dataset videos: {len(final_df[final_df['source'] == 'larger_dataset'])}")
        print(f"   Missing videos: {missing_count}")
        
        # Show final phrase distribution
        phrase_counts = final_df['phrase'].value_counts()
        print(f"\n📊 Final phrase distribution:")
        total_videos = 0
        for phrase, count in sorted(phrase_counts.items()):
            print(f"   {phrase.title()}: {count} videos")
            total_videos += count
        
        print(f"   Total: {total_videos} videos across {len(phrase_counts)} phrases")
        
        # Add class indices
        final_df['class_idx'] = final_df['phrase'].map(self.phrase_to_idx)
        
        # Save manifest
        final_df.to_csv(output_path, index=False)
        print(f"💾 Combined manifest saved: {output_path}")
        
        return final_df
    
    def create_dataset_summary(self, manifest_df: pd.DataFrame, output_path: str):
        """Create summary of the focused dataset"""
        
        summary = {
            'dataset_info': {
                'name': 'ICU Lipreading Focused Dataset',
                'description': '13 high-performing phrases from baseline testing',
                'total_videos': len(manifest_df),
                'num_classes': len(self.target_phrases),
                'creation_date': pd.Timestamp.now().isoformat()
            },
            'target_phrases': self.target_phrases,
            'phrase_to_idx': self.phrase_to_idx,
            'phrase_distribution': manifest_df['phrase'].value_counts().to_dict(),
            'source_distribution': manifest_df['source'].value_counts().to_dict(),
            'baseline_performance': {
                'perfect_performance': [
                    "am i getting better", "i feel anxious", "i m confused",
                    "i need to move", "i need to sit up", "i want to phone my family",
                    "what happened to me", "what time is my wife coming", 
                    "where am i", "who is with me today"
                ],
                'strong_performance': [
                    "i have a headache", "my back hurts", "stay with me please"
                ]
            }
        }
        
        # Save summary
        with open(output_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"📋 Dataset summary saved: {output_path}")

def main():
    """Main function to create focused dataset"""
    
    print("🎯 Creating Focused ICU Lipreading Dataset")
    print("=" * 45)
    print("Target: 13 high-performing phrases from baseline testing")
    
    # Initialize creator
    creator = FocusedDatasetCreator()
    
    # Load reference videos
    reference_manifest = "reference_videos_manifest_fixed.csv"
    reference_df = creator.load_reference_videos(reference_manifest)
    
    # Search for larger dataset videos
    # Try multiple possible locations
    possible_dataset_paths = [
        "/Users/<USER>/Desktop/icu-videos-today",
        "/Users/<USER>/Desktop/processed_videos",
        "/Users/<USER>/Desktop/training_videos",
        "/Users/<USER>/Desktop"
    ]
    
    larger_df = pd.DataFrame()
    for dataset_path in possible_dataset_paths:
        if Path(dataset_path).exists():
            print(f"🔍 Checking: {dataset_path}")
            larger_df = creator.find_larger_dataset_videos(dataset_path)
            if len(larger_df) > 0:
                break
    
    # Create combined manifest
    output_manifest = "focused_13_phrases_manifest.csv"
    combined_df = creator.create_combined_manifest(reference_df, larger_df, output_manifest)
    
    # Create dataset summary
    summary_path = "focused_13_phrases_summary.json"
    creator.create_dataset_summary(combined_df, summary_path)
    
    print(f"\n🎉 Focused Dataset Creation Complete!")
    print("=" * 40)
    print(f"✅ Manifest: {output_manifest}")
    print(f"✅ Summary: {summary_path}")
    print(f"✅ Total videos: {len(combined_df)}")
    print(f"✅ Target phrases: {len(creator.target_phrases)}")
    
    # Show next steps
    print(f"\n🚀 Next Steps:")
    print(f"   1. Update model architecture for 13 classes")
    print(f"   2. Configure transfer learning from 26-class baseline")
    print(f"   3. Train focused model on expanded dataset")
    print(f"   4. Evaluate performance improvements")

if __name__ == '__main__':
    main()
