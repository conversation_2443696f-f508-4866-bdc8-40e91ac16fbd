#!/usr/bin/env python3
"""
Tool to check and verify a specific cropped reference video
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import subprocess
import json

def list_available_videos():
    """List all available cropped videos for selection"""
    
    cropped_dir = Path("mouth_cropped_videos")
    
    if not cropped_dir.exists():
        print("❌ Cropped videos directory not found")
        return []
    
    cropped_videos = list(cropped_dir.glob("*_mouth_cropped.webm"))
    
    if not cropped_videos:
        print("❌ No cropped videos found")
        return []
    
    print(f"📁 Found {len(cropped_videos)} cropped videos:")
    print("-" * 50)
    
    for i, video in enumerate(cropped_videos[:10]):  # Show first 10
        # Extract phrase from filename
        filename = video.stem.replace("_mouth_cropped", "")
        phrase = filename.split("__")[0].replace("_", " ")
        
        print(f"{i+1:2d}. {phrase.title()}")
        print(f"    File: {video.name}")
    
    if len(cropped_videos) > 10:
        print(f"    ... and {len(cropped_videos) - 10} more videos")
    
    return cropped_videos

def check_video_properties(video_path):
    """Check technical properties of the video"""
    
    print(f"\n🔍 Checking Video Properties")
    print("=" * 30)
    
    # Use OpenCV to get basic properties
    cap = cv2.VideoCapture(str(video_path))
    
    if not cap.isOpened():
        print("❌ Could not open video file")
        return None
    
    # Get properties
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    cap.release()
    
    # Get file size
    file_size = video_path.stat().st_size
    
    print(f"📊 Technical Properties:")
    print(f"   Resolution: {width}×{height} pixels")
    print(f"   Frame rate: {fps:.2f} fps")
    print(f"   Frame count: {frame_count}")
    print(f"   Duration: {frame_count/fps:.2f} seconds" if fps > 0 else "   Duration: Unknown")
    print(f"   File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
    
    # Verify expected cropped dimensions
    expected_width, expected_height = 133, 100
    
    print(f"\n✅ Dimension Verification:")
    if width == expected_width and height == expected_height:
        print(f"   ✅ Correct dimensions: {width}×{height} matches expected {expected_width}×{expected_height}")
    else:
        print(f"   ❌ Incorrect dimensions: {width}×{height} (expected {expected_width}×{expected_height})")
    
    return {
        'width': width,
        'height': height,
        'fps': fps,
        'frame_count': frame_count,
        'file_size': file_size,
        'duration': frame_count/fps if fps > 0 else 0
    }

def extract_and_display_frames(video_path, num_frames=8):
    """Extract and display frames from the cropped video"""
    
    print(f"\n🎬 Extracting Sample Frames")
    print("=" * 30)
    
    cap = cv2.VideoCapture(str(video_path))
    
    if not cap.isOpened():
        print("❌ Could not open video")
        return None
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    if total_frames == 0:
        print("❌ No frames found in video")
        cap.release()
        return None
    
    # Calculate frame indices for even distribution
    if total_frames <= num_frames:
        frame_indices = list(range(total_frames))
    else:
        frame_indices = [int(i * total_frames / num_frames) for i in range(num_frames)]
    
    frames = []
    
    for frame_idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if ret:
            frames.append(frame)
        else:
            print(f"⚠️  Could not read frame {frame_idx}")
    
    cap.release()
    
    if not frames:
        print("❌ No frames extracted")
        return None
    
    print(f"✅ Extracted {len(frames)} frames")
    
    # Create visualization
    create_frame_display(frames, video_path.stem)
    
    return frames

def create_frame_display(frames, video_name):
    """Create a display of extracted frames"""
    
    num_frames = len(frames)
    cols = min(4, num_frames)
    rows = (num_frames + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(16, 4*rows))
    
    if rows == 1:
        axes = [axes] if cols == 1 else axes
    else:
        axes = axes.flatten()
    
    for i, frame in enumerate(frames):
        if i < len(axes):
            # Convert BGR to RGB for matplotlib
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            axes[i].imshow(frame_rgb)
            axes[i].set_title(f'Frame {i+1}', fontweight='bold')
            axes[i].axis('off')
            
            # Add frame info
            h, w = frame_rgb.shape[:2]
            axes[i].text(5, h-5, f'{w}×{h}', color='white', fontweight='bold',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor='black', alpha=0.7))
    
    # Hide unused subplots
    for i in range(len(frames), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle(f'Cropped Video Frames: {video_name.replace("_", " ").title()}', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # Save the display
    output_path = f"cropped_video_check_{video_name}.png"
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    print(f"💾 Frame display saved: {output_path}")
    
    plt.show()
    plt.close()

def analyze_mouth_content(frames):
    """Analyze the content to verify mouth region is properly captured"""
    
    print(f"\n👄 Mouth Content Analysis")
    print("=" * 30)
    
    if not frames:
        print("❌ No frames to analyze")
        return
    
    # Convert frames to grayscale for analysis
    gray_frames = [cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) for frame in frames]
    
    # Calculate motion between consecutive frames
    motion_scores = []
    for i in range(1, len(gray_frames)):
        diff = cv2.absdiff(gray_frames[i-1], gray_frames[i])
        motion_score = np.mean(diff)
        motion_scores.append(motion_score)
    
    avg_motion = np.mean(motion_scores) if motion_scores else 0
    max_motion = max(motion_scores) if motion_scores else 0
    
    print(f"📊 Motion Analysis:")
    print(f"   Average motion: {avg_motion:.2f}")
    print(f"   Maximum motion: {max_motion:.2f}")
    print(f"   Motion detected: {'Yes' if avg_motion > 5.0 else 'Low/None'}")
    
    # Analyze brightness distribution (should be focused on face/mouth area)
    sample_frame = gray_frames[len(gray_frames)//2]  # Middle frame
    
    # Calculate brightness statistics
    mean_brightness = np.mean(sample_frame)
    std_brightness = np.std(sample_frame)
    
    print(f"\n📊 Brightness Analysis:")
    print(f"   Mean brightness: {mean_brightness:.1f}")
    print(f"   Brightness std: {std_brightness:.1f}")
    print(f"   Dynamic range: {np.min(sample_frame)} - {np.max(sample_frame)}")
    
    # Check for proper contrast (mouth should have good contrast)
    contrast_score = std_brightness / mean_brightness if mean_brightness > 0 else 0
    
    print(f"   Contrast score: {contrast_score:.3f}")
    print(f"   Contrast quality: {'Good' if contrast_score > 0.3 else 'Low'}")
    
    # Analyze center region (where mouth should be)
    h, w = sample_frame.shape
    center_region = sample_frame[h//4:3*h//4, w//4:3*w//4]
    center_brightness = np.mean(center_region)
    
    print(f"\n📊 Center Region Analysis:")
    print(f"   Center brightness: {center_brightness:.1f}")
    print(f"   Center vs overall: {center_brightness/mean_brightness:.2f}x")
    
    return {
        'avg_motion': avg_motion,
        'max_motion': max_motion,
        'mean_brightness': mean_brightness,
        'contrast_score': contrast_score,
        'center_brightness': center_brightness
    }

def compare_with_original(cropped_video_path):
    """Compare cropped video with its original version"""
    
    print(f"\n🔄 Comparing with Original")
    print("=" * 30)
    
    # Find original video
    cropped_name = cropped_video_path.stem.replace("_mouth_cropped", "")
    original_path = Path("/Users/<USER>/Desktop/icu-videos-today") / f"{cropped_name}.webm"
    
    if not original_path.exists():
        print(f"⚠️  Original video not found: {original_path}")
        return
    
    print(f"📹 Original: {original_path.name}")
    print(f"📹 Cropped: {cropped_video_path.name}")
    
    # Get properties of both videos
    orig_cap = cv2.VideoCapture(str(original_path))
    crop_cap = cv2.VideoCapture(str(cropped_video_path))
    
    if orig_cap.isOpened() and crop_cap.isOpened():
        orig_width = int(orig_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        orig_height = int(orig_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        orig_frames = int(orig_cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        crop_width = int(crop_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        crop_height = int(crop_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        crop_frames = int(crop_cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"📊 Size Comparison:")
        print(f"   Original: {orig_width}×{orig_height} ({orig_frames} frames)")
        print(f"   Cropped:  {crop_width}×{crop_height} ({crop_frames} frames)")
        print(f"   Spatial reduction: {(orig_width*orig_height)/(crop_width*crop_height):.1f}x")
        
        # File size comparison
        orig_size = original_path.stat().st_size
        crop_size = cropped_video_path.stat().st_size
        
        print(f"📊 File Size Comparison:")
        print(f"   Original: {orig_size:,} bytes ({orig_size/1024:.1f} KB)")
        print(f"   Cropped:  {crop_size:,} bytes ({crop_size/1024:.1f} KB)")
        print(f"   Size reduction: {orig_size/crop_size:.1f}x")
        
        # Verify crop region
        expected_crop_region = (133, 0, 133, 100)  # x, y, w, h
        print(f"\n✅ Crop Verification:")
        print(f"   Expected crop region: {expected_crop_region}")
        print(f"   Expected result size: 133×100")
        print(f"   Actual result size: {crop_width}×{crop_height}")
        
        if crop_width == 133 and crop_height == 100:
            print(f"   ✅ Cropping appears correct!")
        else:
            print(f"   ❌ Cropping may be incorrect!")
    
    orig_cap.release()
    crop_cap.release()

def main():
    """Main function to check a cropped video"""
    
    print("🔍 Cropped Reference Video Checker")
    print("=" * 50)
    
    # List available videos
    available_videos = list_available_videos()
    
    if not available_videos:
        return
    
    # Let user select a video or use default
    print(f"\n📹 Select a video to check:")
    print(f"Enter number (1-{min(10, len(available_videos))}) or press Enter for default:")
    
    try:
        choice = input().strip()
        if choice:
            video_index = int(choice) - 1
            if 0 <= video_index < len(available_videos):
                selected_video = available_videos[video_index]
            else:
                print(f"Invalid choice, using first video")
                selected_video = available_videos[0]
        else:
            selected_video = available_videos[0]  # Default to first video
    except:
        selected_video = available_videos[0]  # Default on any error
    
    print(f"\n🎬 Checking: {selected_video.name}")
    print("=" * 60)
    
    # Check video properties
    properties = check_video_properties(selected_video)
    
    if properties is None:
        print("❌ Could not analyze video properties")
        return
    
    # Extract and display frames
    frames = extract_and_display_frames(selected_video)
    
    if frames is None:
        print("❌ Could not extract frames")
        return
    
    # Analyze mouth content
    analysis = analyze_mouth_content(frames)
    
    # Compare with original
    compare_with_original(selected_video)
    
    # Final assessment
    print(f"\n🎯 Final Assessment")
    print("=" * 20)
    
    issues = []
    
    if properties['width'] != 133 or properties['height'] != 100:
        issues.append("Incorrect dimensions")
    
    if analysis and analysis['avg_motion'] < 5.0:
        issues.append("Low motion detected")
    
    if analysis and analysis['contrast_score'] < 0.3:
        issues.append("Low contrast")
    
    if not issues:
        print("✅ Video appears to be cropped correctly!")
        print("✅ Proper dimensions (133×100)")
        print("✅ Good motion detection")
        print("✅ Adequate contrast")
        print("✅ Ready for training pipeline")
    else:
        print("⚠️  Potential issues found:")
        for issue in issues:
            print(f"   - {issue}")
    
    print(f"\n💾 Check complete! Frame display saved as image file.")

if __name__ == '__main__':
    main()
