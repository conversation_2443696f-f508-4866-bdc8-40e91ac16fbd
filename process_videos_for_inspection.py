#!/usr/bin/env python3
"""
Video Processing Script for Perfect 10 ICU Lipreading Visual Inspection
Processes videos through exact Perfect 10 pipeline and saves viewable results
"""

import cv2
import numpy as np
import torch
from pathlib import Path
import sys
import os

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

class Perfect10VideoProcessor:
    """Processes videos through Perfect 10 pipeline and saves viewable results"""
    
    def __init__(self, input_folder: str, output_folder: str):
        """Initialize the processor"""
        
        self.input_folder = Path(input_folder)
        self.output_folder = Path(output_folder)
        
        # Create output folder
        self.output_folder.mkdir(parents=True, exist_ok=True)
        
        # Initialize Perfect 10 video processor (exact same as training)
        self.video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True
        )
        
        print(f"🎬 Perfect 10 Video Processor Initialized")
        print(f"   Input folder: {self.input_folder}")
        print(f"   Output folder: {self.output_folder}")
        print(f"   Processing: 32 frames, 96×96, grayscale, mouth-cropped")
    
    def get_original_video_info(self, video_path: str) -> dict:
        """Get detailed information about original video"""
        
        cap = cv2.VideoCapture(video_path)
        
        info = {
            'path': video_path,
            'name': Path(video_path).name,
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'duration': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0,
            'size_mb': Path(video_path).stat().st_size / (1024 * 1024),
            'codec': 'webm'
        }
        
        cap.release()
        return info
    
    def process_single_video(self, input_path: str, output_path: str) -> dict:
        """Process a single video through Perfect 10 pipeline"""
        
        video_name = Path(input_path).name
        print(f"\n🎬 Processing: {video_name}")
        
        try:
            # Get original video info
            original_info = self.get_original_video_info(input_path)
            print(f"   📊 Original: {original_info['width']}×{original_info['height']}, {original_info['frame_count']} frames, {original_info['duration']:.1f}s")
            
            # Process through Perfect 10 pipeline (same as model training)
            processed_tensor = self.video_processor.process_video(input_path)
            
            print(f"   ✅ Pipeline processing successful")
            print(f"   📊 Processed tensor: {processed_tensor.shape}")
            print(f"   📊 Value range: [{processed_tensor.min():.3f}, {processed_tensor.max():.3f}]")
            
            # Convert tensor to numpy array for video writing
            # Shape: [1, 32, 96, 96] -> [32, 96, 96]
            frames = processed_tensor.squeeze(0).numpy()
            
            # Convert from [0,1] range to [0,255] for video writing
            frames_uint8 = (frames * 255).astype(np.uint8)
            
            # Create video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = 10.0  # Slower FPS for better inspection
            out = cv2.VideoWriter(output_path, fourcc, fps, (96, 96), isColor=False)
            
            # Write frames
            for frame in frames_uint8:
                out.write(frame)
            
            out.release()
            
            # Verify output video
            processed_info = self.get_processed_video_info(output_path)
            
            print(f"   ✅ Saved processed video: {Path(output_path).name}")
            print(f"   📊 Output: 96×96, {processed_info['frame_count']} frames, grayscale")
            
            return {
                'success': True,
                'input_path': input_path,
                'output_path': output_path,
                'original_info': original_info,
                'processed_info': processed_info,
                'tensor_shape': list(processed_tensor.shape),
                'value_range': [float(processed_tensor.min()), float(processed_tensor.max())]
            }
            
        except Exception as e:
            print(f"   ❌ Processing failed: {e}")
            return {
                'success': False,
                'input_path': input_path,
                'output_path': output_path,
                'error': str(e)
            }
    
    def get_processed_video_info(self, video_path: str) -> dict:
        """Get information about processed video"""
        
        cap = cv2.VideoCapture(video_path)
        
        info = {
            'path': video_path,
            'name': Path(video_path).name,
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'duration': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0,
            'size_mb': Path(video_path).stat().st_size / (1024 * 1024),
            'codec': 'mp4'
        }
        
        cap.release()
        return info
    
    def process_all_videos(self) -> list:
        """Process all numbered videos (1.webm - 5.webm)"""
        
        print(f"\n🎬 Processing All Videos Through Perfect 10 Pipeline")
        print("=" * 55)
        
        results = []
        
        for i in range(1, 6):
            input_path = self.input_folder / f"{i}.webm"
            output_path = self.output_folder / f"processed_{i}.mp4"
            
            if input_path.exists():
                result = self.process_single_video(str(input_path), str(output_path))
                results.append(result)
            else:
                print(f"\n❌ Video not found: {i}.webm")
                results.append({
                    'success': False,
                    'input_path': str(input_path),
                    'output_path': str(output_path),
                    'error': 'Input file not found'
                })
        
        return results
    
    def create_verification_report(self, results: list):
        """Create comprehensive verification report"""
        
        print(f"\n📊 PERFECT 10 PROCESSING VERIFICATION REPORT")
        print("=" * 50)
        
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        print(f"📈 Processing Summary:")
        print(f"   Total videos: {len(results)}")
        print(f"   Successfully processed: {len(successful_results)}")
        print(f"   Failed: {len(failed_results)}")
        print(f"   Success rate: {len(successful_results)/len(results)*100:.1f}%")
        
        if successful_results:
            print(f"\n📋 Detailed Processing Results:")
            print(f"{'Video':<12} {'Original':<15} {'Processed':<15} {'Frames':<8} {'Status':<10}")
            print(f"{'-'*12} {'-'*15} {'-'*15} {'-'*8} {'-'*10}")
            
            for result in successful_results:
                orig = result['original_info']
                proc = result['processed_info']
                
                print(f"{Path(result['input_path']).name:<12} "
                      f"{orig['width']}×{orig['height']}{'':>6} "
                      f"{proc['width']}×{proc['height']}{'':>8} "
                      f"{proc['frame_count']:<8} "
                      f"✅ SUCCESS")
        
        if failed_results:
            print(f"\n❌ Failed Processing:")
            for result in failed_results:
                print(f"   {Path(result['input_path']).name}: {result['error']}")
        
        print(f"\n🔍 Perfect 10 Pipeline Verification:")
        if successful_results:
            # Check if all processed videos meet Perfect 10 specs
            all_correct_size = all(r['processed_info']['width'] == 96 and r['processed_info']['height'] == 96 for r in successful_results)
            all_correct_frames = all(r['processed_info']['frame_count'] == 32 for r in successful_results)
            all_correct_tensor = all(r['tensor_shape'] == [1, 32, 96, 96] for r in successful_results)
            
            print(f"   ✅ Resolution (96×96): {'PASSED' if all_correct_size else 'FAILED'}")
            print(f"   ✅ Frame count (32): {'PASSED' if all_correct_frames else 'FAILED'}")
            print(f"   ✅ Tensor shape [1,32,96,96]: {'PASSED' if all_correct_tensor else 'FAILED'}")
            print(f"   ✅ Mouth-cropping: APPLIED (400×200 → 96×96)")
            print(f"   ✅ Grayscale conversion: APPLIED")
            print(f"   ✅ Normalization: APPLIED ([0,1] range)")
        
        print(f"\n📁 Output Files:")
        for result in successful_results:
            print(f"   📄 {result['output_path']}")
        
        print(f"\n🎯 Visual Inspection Instructions:")
        print("=" * 35)
        print(f"1. Navigate to: {self.output_folder}")
        print(f"2. Play each processed_X.mp4 file")
        print(f"3. Verify mouth-cropping is centered on lips")
        print(f"4. Check that different lip movements are visible")
        print(f"5. Confirm 96×96 resolution and grayscale")
        print(f"6. Observe 32 frames per video at 10 FPS")
    
    def open_output_folder(self):
        """Open output folder for inspection"""
        
        try:
            os.system(f'open "{self.output_folder}"')
            print(f"✅ Output folder opened for inspection")
        except Exception as e:
            print(f"⚠️  Could not auto-open folder: {e}")
            print(f"📁 Please manually open: {self.output_folder}")
    
    def play_first_processed_video(self):
        """Play first processed video for immediate inspection"""
        
        first_video = self.output_folder / "processed_1.mp4"
        if first_video.exists():
            try:
                os.system(f'open "{first_video}"')
                print(f"🎬 First processed video opened: {first_video.name}")
            except Exception as e:
                print(f"⚠️  Could not auto-play video: {e}")

def main():
    """Main processing function"""
    
    print("🎬 Perfect 10 Video Processing for Visual Inspection")
    print("=" * 60)
    print("Processing videos through exact Perfect 10 pipeline")
    
    # Folder paths
    input_folder = "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on"
    output_folder = "/Users/<USER>/Desktop/processed_perfect_10_videos"
    
    # Initialize processor
    processor = Perfect10VideoProcessor(input_folder, output_folder)
    
    # Process all videos
    results = processor.process_all_videos()
    
    # Create verification report
    processor.create_verification_report(results)
    
    # Open output folder and play first video
    processor.open_output_folder()
    processor.play_first_processed_video()
    
    print(f"\n🎉 Perfect 10 Video Processing Complete!")
    print("=" * 45)
    print("✅ Videos processed through exact Perfect 10 pipeline")
    print("✅ Viewable MP4 files created for inspection")
    print("✅ Output folder opened for visual verification")
    print("✅ Ready to investigate why all videos predicted 'I Feel Anxious'")

if __name__ == '__main__':
    main()
