#!/usr/bin/env python3
"""
Simple script to show video file paths and basic preprocessing verification
"""

import cv2
import numpy as np
import torch
from pathlib import Path
import sys
import os

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

def get_video_info(video_path):
    """Get basic video information"""
    cap = cv2.VideoCapture(video_path)
    
    info = {
        'path': video_path,
        'name': Path(video_path).name,
        'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
        'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        'fps': cap.get(cv2.CAP_PROP_FPS),
        'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
        'duration': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0,
        'size_mb': Path(video_path).stat().st_size / (1024 * 1024)
    }
    
    cap.release()
    return info

def verify_preprocessing(video_path):
    """Verify preprocessing pipeline on a video"""
    
    print(f"\n🔄 Testing preprocessing on: {Path(video_path).name}")
    
    try:
        # Initialize video processor (same as Perfect 10 training)
        video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True
        )
        
        # Process video
        processed_tensor = video_processor.process_video(video_path)
        
        print(f"   ✅ Preprocessing successful")
        print(f"   📊 Output shape: {processed_tensor.shape}")
        print(f"   📊 Value range: [{processed_tensor.min():.3f}, {processed_tensor.max():.3f}]")
        print(f"   📊 Expected: [1, 32, 96, 96] with values in [0, 1] range")
        
        # Verify shape
        expected_shape = torch.Size([1, 32, 96, 96])
        if processed_tensor.shape == expected_shape:
            print(f"   ✅ Shape verification: PASSED")
        else:
            print(f"   ❌ Shape verification: FAILED (got {processed_tensor.shape}, expected {expected_shape})")
        
        # Verify value range
        if 0 <= processed_tensor.min() and processed_tensor.max() <= 1:
            print(f"   ✅ Value range verification: PASSED")
        else:
            print(f"   ❌ Value range verification: FAILED")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Preprocessing failed: {e}")
        return False

def main():
    """Main function to show video paths and verify preprocessing"""
    
    print("📹 Video File Paths and Preprocessing Verification")
    print("=" * 60)
    
    # Video folder
    videos_folder = "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on"
    
    print(f"📁 Videos folder: {videos_folder}")
    print(f"📁 Folder opened in browser for visual inspection")
    
    # Find all videos
    video_files = []
    for i in range(1, 6):
        video_path = Path(videos_folder) / f"{i}.webm"
        if video_path.exists():
            video_files.append(str(video_path))
    
    if not video_files:
        print(f"❌ No numbered videos (1.webm - 5.webm) found")
        return
    
    print(f"\n🎬 Found {len(video_files)} test videos:")
    print("=" * 40)
    
    # Show video information
    for video_path in video_files:
        info = get_video_info(video_path)
        print(f"\n📄 {info['name']}")
        print(f"   📁 Full path: {info['path']}")
        print(f"   📊 Resolution: {info['width']}×{info['height']}")
        print(f"   📊 Frames: {info['frame_count']}")
        print(f"   📊 FPS: {info['fps']:.1f}")
        print(f"   📊 Duration: {info['duration']:.1f}s")
        print(f"   📊 File size: {info['size_mb']:.1f} MB")
    
    print(f"\n🔍 Preprocessing Pipeline Verification")
    print("=" * 40)
    
    # Test preprocessing on each video
    successful_count = 0
    for video_path in video_files:
        success = verify_preprocessing(video_path)
        if success:
            successful_count += 1
    
    print(f"\n📊 Preprocessing Verification Summary")
    print("=" * 40)
    print(f"   Total videos: {len(video_files)}")
    print(f"   Successfully processed: {successful_count}")
    print(f"   Success rate: {successful_count/len(video_files)*100:.1f}%")
    
    if successful_count == len(video_files):
        print(f"   ✅ All videos processed correctly")
        print(f"   ✅ Mouth-cropping applied")
        print(f"   ✅ Resized to 96×96 pixels")
        print(f"   ✅ Converted to grayscale")
        print(f"   ✅ 32 frames extracted")
        print(f"   ✅ Values normalized to [0,1] range")
    else:
        print(f"   ⚠️  Some videos failed preprocessing")
    
    print(f"\n🎯 Manual Inspection Instructions:")
    print("=" * 35)
    print(f"1. The folder is now open in your browser/Finder")
    print(f"2. Double-click any video (1.webm - 5.webm) to play it")
    print(f"3. Verify the videos show clear lip movements")
    print(f"4. Check if the speaker appears to be saying different phrases")
    
    print(f"\n📋 Video File Paths for Manual Opening:")
    print("=" * 45)
    for video_path in video_files:
        print(f"   📄 {video_path}")
    
    # Try to open first video
    if video_files:
        first_video = video_files[0]
        print(f"\n🎬 Attempting to open first video: {Path(first_video).name}")
        try:
            # Use macOS 'open' command
            os.system(f'open "{first_video}"')
            print(f"   ✅ Video should open in default player")
        except Exception as e:
            print(f"   ⚠️  Auto-open failed: {e}")
    
    print(f"\n🎉 Verification Complete!")
    print("=" * 25)
    print("✅ Video paths displayed")
    print("✅ Preprocessing pipeline verified")
    print("✅ Folder opened for manual inspection")

if __name__ == '__main__':
    main()
