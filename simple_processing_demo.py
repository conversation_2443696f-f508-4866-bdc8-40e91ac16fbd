#!/usr/bin/env python3
"""
Simple demonstration of video processing steps
"""

import cv2
import numpy as np
import torch
import sys
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

def demonstrate_processing_steps():
    """Show the key processing steps with actual numbers"""
    
    print("🎬 ICU Lipreading Video Processing Steps")
    print("=" * 50)
    
    # Use a reference video
    video_path = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    
    if not Path(video_path).exists():
        print(f"❌ Video not found: {video_path}")
        return
    
    print(f"📹 Processing: {Path(video_path).name}")
    
    # Step 1: Read original video with OpenCV
    print(f"\n📥 Step 1: Loading Original Video")
    cap = cv2.VideoCapture(video_path)
    
    frames = []
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frames.append(frame)
        frame_count += 1
    
    cap.release()
    
    if frames:
        h, w, c = frames[0].shape
        print(f"   ✅ Loaded {frame_count} frames")
        print(f"   ✅ Original size: {w}x{h} pixels, {c} channels (BGR)")
        print(f"   ✅ Duration: ~{frame_count/30:.1f} seconds at 30fps")
    
    # Step 2: Convert to tensor format
    print(f"\n🔄 Step 2: Convert to Tensor Format")
    frames_array = np.array(frames)
    print(f"   ✅ NumPy array shape: {frames_array.shape} (T, H, W, C)")
    
    # Convert to PyTorch tensor and rearrange dimensions
    frames_tensor = torch.from_numpy(frames_array).permute(0, 3, 1, 2).float()
    print(f"   ✅ Tensor shape: {frames_tensor.shape} (T, C, H, W)")
    print(f"   ✅ Value range: [0, 255] (uint8 pixel values)")
    
    # Step 3: Resize to target resolution
    print(f"\n📏 Step 3: Resize to Target Resolution (96x96)")
    import torch.nn.functional as F
    
    resized_frames = F.interpolate(
        frames_tensor, 
        size=(96, 96), 
        mode='bilinear', 
        align_corners=False
    )
    print(f"   ✅ Resized shape: {resized_frames.shape}")
    print(f"   ✅ Spatial compression: {w}x{h} → 96x96 ({w*h/(96*96):.1f}x reduction)")
    
    # Step 4: Convert to grayscale
    print(f"\n⚫ Step 4: Convert to Grayscale")
    if resized_frames.shape[1] == 3:  # RGB/BGR
        # Standard grayscale conversion weights
        gray_frames = (0.299 * resized_frames[:, 0:1] + 
                      0.587 * resized_frames[:, 1:2] + 
                      0.114 * resized_frames[:, 2:3])
        print(f"   ✅ Grayscale shape: {gray_frames.shape}")
        print(f"   ✅ Channels reduced: 3 → 1 (RGB → Grayscale)")
    else:
        gray_frames = resized_frames
        print(f"   ✅ Already grayscale: {gray_frames.shape}")
    
    # Step 5: Temporal cropping/padding to exactly 32 frames
    print(f"\n⏱️  Step 5: Temporal Adjustment to 32 Frames")
    current_frames = gray_frames.shape[0]
    target_frames = 32
    
    print(f"   📊 Current frames: {current_frames}")
    print(f"   🎯 Target frames: {target_frames}")
    
    if current_frames > target_frames:
        # Crop from center
        start_idx = (current_frames - target_frames) // 2
        final_frames = gray_frames[start_idx:start_idx + target_frames]
        print(f"   ✂️  Cropped from center: frames {start_idx} to {start_idx + target_frames - 1}")
        print(f"   ✅ Final temporal shape: {final_frames.shape}")
    elif current_frames < target_frames:
        # Pad by repeating last frame
        padding_needed = target_frames - current_frames
        last_frame = gray_frames[-1:].repeat(padding_needed, 1, 1, 1)
        final_frames = torch.cat([gray_frames, last_frame], dim=0)
        print(f"   📎 Padded with {padding_needed} repeated frames")
        print(f"   ✅ Final temporal shape: {final_frames.shape}")
    else:
        final_frames = gray_frames
        print(f"   ✅ No adjustment needed: {final_frames.shape}")
    
    # Step 6: Normalize pixel values
    print(f"\n📊 Step 6: Normalize Pixel Values")
    print(f"   📥 Input range: [0, 255] (uint8)")
    
    # Convert to [0, 1] range
    normalized_frames = final_frames / 255.0
    print(f"   🔄 Normalized to [0, 1]: range [{normalized_frames.min():.3f}, {normalized_frames.max():.3f}]")
    
    # Convert to [-1, 1] range (standard for neural networks)
    final_normalized = (normalized_frames - 0.5) / 0.5
    print(f"   ✅ Final range [-1, 1]: [{final_normalized.min():.3f}, {final_normalized.max():.3f}]")
    
    # Step 7: Rearrange to final format
    print(f"\n🔄 Step 7: Rearrange to Final Format")
    print(f"   📥 Current shape: {final_normalized.shape} (T, C, H, W)")
    
    # Rearrange to (C, T, H, W) format expected by the model
    final_tensor = final_normalized.permute(1, 0, 2, 3)
    print(f"   ✅ Final shape: {final_tensor.shape} (C, T, H, W)")
    print(f"   ✅ Ready for model input!")
    
    # Step 8: Compare with actual processor
    print(f"\n🔍 Step 8: Verify with Actual Processor")
    processor = VideoProcessor(target_frames=32, target_size=(96, 96), grayscale=True)
    processed_tensor = processor.process_video(video_path)
    
    print(f"   📊 Manual processing: {final_tensor.shape}")
    print(f"   📊 Processor output: {processed_tensor.shape}")
    print(f"   ✅ Shapes match: {final_tensor.shape == processed_tensor.shape}")
    
    # Show value comparison
    manual_range = f"[{final_tensor.min():.3f}, {final_tensor.max():.3f}]"
    processor_range = f"[{processed_tensor.min():.3f}, {processed_tensor.max():.3f}]"
    print(f"   📊 Manual values: {manual_range}")
    print(f"   📊 Processor values: {processor_range}")
    
    # Calculate difference
    if final_tensor.shape == processed_tensor.shape:
        diff = torch.abs(final_tensor - processed_tensor).mean()
        print(f"   📊 Mean difference: {diff:.6f}")
        print(f"   ✅ Processing {'matches' if diff < 0.01 else 'differs'}")
    
    # Summary
    print(f"\n📋 Processing Summary")
    print("=" * 30)
    print(f"🔸 Original: {w}x{h}x{current_frames} frames, 3 channels")
    print(f"🔸 Final: 96x96x32 frames, 1 channel")
    print(f"🔸 Spatial reduction: {(w*h)/(96*96):.1f}x")
    print(f"🔸 Temporal adjustment: {current_frames} → 32 frames")
    print(f"🔸 Data reduction: {(w*h*current_frames*3)/(96*96*32*1):.1f}x")
    print(f"🔸 Value range: [0,255] → [-1,1]")
    print(f"🔸 Format: (T,H,W,C) → (C,T,H,W)")
    
    return final_tensor, processed_tensor

def show_key_frames(video_path, processed_tensor):
    """Show key frames from the processing"""
    
    print(f"\n🖼️  Key Frames Analysis")
    print("=" * 30)
    
    # Show frame statistics
    print(f"📊 Processed tensor shape: {processed_tensor.shape}")
    print(f"📊 Frame statistics:")
    
    for i in [0, 8, 16, 24, 31]:  # Show frames at different time points
        frame = processed_tensor[0, i]  # Get frame i from channel 0
        print(f"   Frame {i:2d}: min={frame.min():.3f}, max={frame.max():.3f}, mean={frame.mean():.3f}")
    
    # Check for motion (difference between consecutive frames)
    print(f"\n🎬 Motion Analysis:")
    motion_scores = []
    for i in range(1, processed_tensor.shape[1]):
        diff = torch.abs(processed_tensor[0, i] - processed_tensor[0, i-1]).mean()
        motion_scores.append(diff.item())
    
    avg_motion = np.mean(motion_scores)
    max_motion = max(motion_scores)
    
    print(f"   Average motion: {avg_motion:.4f}")
    print(f"   Maximum motion: {max_motion:.4f}")
    print(f"   Motion detected: {'Yes' if avg_motion > 0.01 else 'No'}")
    
    return motion_scores

def main():
    """Main demonstration"""
    
    # Run the processing demonstration
    manual_tensor, processor_tensor = demonstrate_processing_steps()
    
    # Analyze key frames
    video_path = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    motion_scores = show_key_frames(video_path, processor_tensor)
    
    print(f"\n🎯 Demonstration Complete!")
    print(f"The reference video has been successfully processed from:")
    print(f"   📥 400x200x99 frames (RGB) → 📤 96x96x32 frames (Grayscale)")
    print(f"   📊 Ready for training the lipreading model!")

if __name__ == '__main__':
    main()
