#!/usr/bin/env python3
"""
Debug dataset to find tensor shape issues
"""

import sys
sys.path.append('.')

import yaml
from backend.lightweight_vsr.dataset import create_dataloaders

def test_dataset():
    """Test dataset creation and first few samples"""
    
    # Load config
    with open('configs/phrases26.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    config['manifest_path'] = 'data/manifest.csv'
    config['batch_size'] = 4
    config['val_split'] = 0.1
    config['test_split'] = 0.1
    config['num_workers'] = 0
    
    print("Creating dataloaders...")
    train_loader, val_loader, test_loader, data_info = create_dataloaders(config)
    
    print(f"Dataset sizes: train={data_info['train_size']}, val={data_info['val_size']}, test={data_info['test_size']}")
    
    print("\nTesting first few training samples...")
    for i, (batch_videos, batch_labels, batch_metadata) in enumerate(train_loader):
        print(f"Batch {i+1}:")
        print(f"  Videos shape: {batch_videos.shape}")
        print(f"  Labels shape: {batch_labels.shape}")
        print(f"  Sample shapes: {[v.shape for v in batch_videos]}")
        
        if i >= 2:  # Test first 3 batches
            break
    
    print("\n✅ Dataset test completed!")


if __name__ == '__main__':
    test_dataset()
