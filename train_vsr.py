#!/usr/bin/env python3
"""
Standalone training script for lightweight VSR
"""

import argparse
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
from pathlib import Path
import json
import time
from tqdm import tqdm
import numpy as np
import sys
import os

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.model import Mobile3DTiny
from backend.lightweight_vsr.dataset import create_dataloaders
from backend.lightweight_vsr.metrics import (
    compute_metrics, compute_demographics_metrics, 
    save_metrics, print_metrics_summary, check_acceptance_criteria,
    evaluate_model
)


class FocalLoss(nn.Module):
    """Focal Loss for handling class imbalance"""
    
    def __init__(self, alpha=1, gamma=2, weight=None):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.weight = weight
        
    def forward(self, inputs, targets):
        ce_loss = nn.functional.cross_entropy(inputs, targets, weight=self.weight, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        return focal_loss.mean()


def main():
    parser = argparse.ArgumentParser(description='Train lightweight VSR model')
    parser.add_argument('--manifest', type=str, default='data/manifest.csv', help='Path to manifest CSV file')
    parser.add_argument('--config', type=str, default='configs/phrases26.yaml', help='Path to config YAML file')
    parser.add_argument('--out_dir', type=str, default='artifacts/vsr_26p_v1', help='Output directory')
    parser.add_argument('--epochs', type=int, default=40, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--val_split', type=float, default=0.1, help='Validation split')
    parser.add_argument('--test_split', type=float, default=0.1, help='Test split')
    parser.add_argument('--num_workers', type=int, default=2, help='Number of data loader workers')
    parser.add_argument('--amp', type=int, default=1, choices=[0, 1], help='Use mixed precision')
    
    args = parser.parse_args()
    
    # Load config
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
    
    # Override config with command line arguments
    config['manifest_path'] = args.manifest
    config['batch_size'] = args.batch_size
    config['val_split'] = args.val_split
    config['test_split'] = args.test_split
    config['num_workers'] = args.num_workers
    config['amp'] = bool(args.amp)
    config['epochs'] = args.epochs
    
    output_dir = Path(args.out_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create dataloaders
    print("Creating dataloaders...")
    train_loader, val_loader, test_loader, data_info = create_dataloaders(
        config, 
        manifest_path=config.get('manifest_path'),
        data_dir=config.get('data_dir')
    )
    
    print(f"Dataset sizes: train={data_info['train_size']}, "
          f"val={data_info['val_size']}, test={data_info['test_size']}")
    
    # Create model
    model = Mobile3DTiny(
        num_classes=data_info['num_classes'],
        hidden_dim=config.get('model', {}).get('hidden_dim', 256),
        num_gru_layers=config.get('model', {}).get('num_gru_layers', 2),
        dropout=config.get('model', {}).get('dropout', 0.2)
    ).to(device)
    
    print(f"Model parameters: {model.get_num_parameters():,}")
    
    # Loss function
    criterion = nn.CrossEntropyLoss(weight=data_info['class_weights'].to(device))
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config.get('optimizer', {}).get('lr', 0.001),
        weight_decay=config.get('optimizer', {}).get('weight_decay', 0.01)
    )
    
    # Scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, 
        T_max=args.epochs,
        eta_min=1e-6
    )
    
    # Mixed precision
    use_amp = config.get('amp', True)
    scaler = torch.cuda.amp.GradScaler() if use_amp and device.type == 'cuda' else None
    
    # Tensorboard
    writer = SummaryWriter(output_dir / 'tensorboard')
    
    # Training state
    best_val_f1 = 0.0
    
    print(f"Starting training for {args.epochs} epochs...")
    
    for epoch in range(args.epochs):
        start_time = time.time()
        
        # Train
        model.train()
        total_loss = 0.0
        num_batches = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{args.epochs} Train')
        
        for batch_videos, batch_labels, _ in pbar:
            batch_videos = batch_videos.to(device)
            batch_labels = batch_labels.to(device)
            
            optimizer.zero_grad()
            
            # Forward pass with mixed precision
            if use_amp and scaler:
                with torch.cuda.amp.autocast():
                    logits = model(batch_videos)
                    loss = criterion(logits, batch_labels)
                
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
            else:
                logits = model(batch_videos)
                loss = criterion(logits, batch_labels)
                loss.backward()
                optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_loss = total_loss / num_batches
        
        # Validate
        model.eval()
        phrase_names = [data_info['idx_to_phrase'][i] for i in range(data_info['num_classes'])]
        
        y_true, y_pred, metadata = evaluate_model(model, val_loader, device, phrase_names)
        val_metrics = compute_demographics_metrics(y_true, y_pred, metadata, phrase_names)
        val_f1 = val_metrics['overall']['macro_f1']
        
        # Update learning rate
        scheduler.step()
        current_lr = scheduler.get_last_lr()[0]
        
        # Log metrics
        epoch_time = time.time() - start_time
        
        writer.add_scalar('Train/Loss', avg_loss, epoch)
        writer.add_scalar('Val/MacroF1', val_f1, epoch)
        writer.add_scalar('Val/Accuracy', val_metrics['overall']['accuracy'], epoch)
        writer.add_scalar('Learning_Rate', current_lr, epoch)
        
        print(f"Epoch {epoch+1}/{args.epochs} ({epoch_time:.1f}s) - "
              f"Train Loss: {avg_loss:.4f}, "
              f"Val F1: {val_f1:.4f}, LR: {current_lr:.2e}")
        
        # Save checkpoint
        is_best = val_f1 > best_val_f1
        if is_best:
            best_val_f1 = val_f1
            
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_val_f1': best_val_f1,
                'config': config,
                'data_info': data_info
            }
            
            torch.save(checkpoint, output_dir / 'best.ckpt')
            print(f"New best model saved with val F1: {best_val_f1:.4f}")
        
        # Save last checkpoint
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'best_val_f1': best_val_f1,
            'config': config,
            'data_info': data_info
        }
        torch.save(checkpoint, output_dir / 'last.ckpt')
        
        # Early stopping check
        if val_f1 >= 0.90:
            print(f"Reached target F1 of 0.90 at epoch {epoch+1}")
            break
    
    # Final test evaluation
    print("\nEvaluating on test set...")
    y_true, y_pred, metadata = evaluate_model(model, test_loader, device, phrase_names)
    test_metrics = compute_demographics_metrics(y_true, y_pred, metadata, phrase_names)
    
    # Save final metrics
    save_metrics(val_metrics, output_dir / 'metrics.json')
    save_metrics(test_metrics, output_dir / 'test_metrics.json')
    
    # Print final results
    print_metrics_summary(test_metrics)
    
    # Check acceptance criteria
    passed, issues = check_acceptance_criteria(test_metrics)
    print(f"\nAcceptance criteria: {'✅ PASSED' if passed else '❌ FAILED'}")
    if issues:
        for issue in issues:
            print(f"  - {issue}")
    
    writer.close()
    
    print(f"\nTraining completed! Best validation F1: {best_val_f1:.4f}")
    print(f"Results saved to: {output_dir}")


if __name__ == '__main__':
    main()
