#!/usr/bin/env python3
"""
Integration verification for processed training videos
"""

import torch
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import yaml
from typing import List, Dict

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor
from backend.lightweight_vsr.dataset import create_dataloaders
from backend.lightweight_vsr.model import Mobile3DTiny

class IntegrationVerifier:
    """Verify integration of processed videos with training pipeline"""
    
    def __init__(self, processed_dir: str, manifest_path: str):
        self.processed_dir = Path(processed_dir)
        self.manifest_path = Path(manifest_path)
        
        # Load configuration
        with open('configs/phrases26.yaml', 'r') as f:
            self.config = yaml.safe_load(f)
    
    def verify_video_processing(self, sample_size: int = 5) -> Dict:
        """Test processed videos with VideoProcessor"""
        
        print(f"🧪 Testing Video Processing Pipeline")
        print("=" * 35)
        
        if not self.manifest_path.exists():
            print(f"❌ Manifest not found: {self.manifest_path}")
            return {'success': False, 'error': 'Manifest not found'}
        
        # Load manifest
        try:
            manifest_df = pd.read_csv(self.manifest_path)
            print(f"📊 Loaded manifest with {len(manifest_df)} videos")
        except Exception as e:
            print(f"❌ Could not load manifest: {e}")
            return {'success': False, 'error': f'Manifest load failed: {e}'}
        
        # Select sample videos
        sample_videos = manifest_df.head(sample_size)
        
        # Initialize video processor
        processor = VideoProcessor(
            target_frames=self.config.get('frames', 32),
            target_size=(self.config.get('height', 96), self.config.get('width', 96)),
            grayscale=self.config.get('grayscale', True)
        )
        
        results = {
            'success': True,
            'tested_videos': 0,
            'successful_processing': 0,
            'failed_processing': 0,
            'tensor_shapes': [],
            'value_ranges': [],
            'motion_scores': [],
            'errors': []
        }
        
        print(f"🔄 Testing {len(sample_videos)} sample videos...")
        
        for idx, row in sample_videos.iterrows():
            video_path = Path(row['video_path'])
            
            # Make path absolute if relative
            if not video_path.is_absolute():
                video_path = self.processed_dir.parent / video_path
            
            if not video_path.exists():
                results['failed_processing'] += 1
                results['errors'].append(f"Video not found: {video_path}")
                continue
            
            try:
                # Process video
                tensor = processor.process_video(str(video_path))
                
                # Verify tensor properties
                expected_shape = (1, self.config.get('frames', 32), 
                                self.config.get('height', 96), 
                                self.config.get('width', 96))
                
                if tensor.shape == expected_shape:
                    results['successful_processing'] += 1
                    results['tensor_shapes'].append(tensor.shape)
                    results['value_ranges'].append((tensor.min().item(), tensor.max().item()))
                    
                    # Calculate motion score
                    motion_score = self._calculate_motion_score(tensor)
                    results['motion_scores'].append(motion_score)
                    
                    print(f"   ✅ {row['phrase'].title()}: {tensor.shape}, range [{tensor.min():.3f}, {tensor.max():.3f}], motion {motion_score:.4f}")
                else:
                    results['failed_processing'] += 1
                    results['errors'].append(f"Wrong tensor shape: {tensor.shape} (expected {expected_shape})")
                    print(f"   ❌ {row['phrase'].title()}: Wrong shape {tensor.shape}")
                
            except Exception as e:
                results['failed_processing'] += 1
                results['errors'].append(f"Processing failed for {video_path.name}: {str(e)}")
                print(f"   ❌ {row['phrase'].title()}: Processing failed - {e}")
            
            results['tested_videos'] += 1
        
        # Summary
        success_rate = (results['successful_processing'] / results['tested_videos'] * 100) if results['tested_videos'] > 0 else 0
        
        print(f"\n📊 Video Processing Test Results:")
        print(f"   Tested: {results['tested_videos']}")
        print(f"   Successful: {results['successful_processing']}")
        print(f"   Failed: {results['failed_processing']}")
        print(f"   Success rate: {success_rate:.1f}%")
        
        if results['motion_scores']:
            avg_motion = np.mean(results['motion_scores'])
            print(f"   Average motion score: {avg_motion:.4f}")
        
        results['success'] = success_rate >= 80  # 80% success threshold
        
        return results
    
    def verify_model_compatibility(self) -> Dict:
        """Test model compatibility with processed videos"""
        
        print(f"\n🤖 Testing Model Compatibility")
        print("=" * 30)
        
        try:
            # Create model
            model = Mobile3DTiny(num_classes=26)
            model.eval()
            
            print(f"✅ Model created: {model.get_num_parameters():,} parameters")
            
            # Test with synthetic input matching processed video format
            batch_size = 2
            channels = 1
            frames = self.config.get('frames', 32)
            height = self.config.get('height', 96)
            width = self.config.get('width', 96)
            
            test_input = torch.randn(batch_size, channels, frames, height, width)
            
            print(f"📥 Test input shape: {test_input.shape}")
            
            # Forward pass
            with torch.no_grad():
                output = model(test_input)
            
            print(f"📤 Model output shape: {output.shape}")
            print(f"✅ Model forward pass successful!")
            
            # Test with actual processed video if available
            if self.manifest_path.exists():
                manifest_df = pd.read_csv(self.manifest_path)
                
                if len(manifest_df) > 0:
                    # Get first video
                    first_video_path = Path(manifest_df.iloc[0]['video_path'])
                    
                    if not first_video_path.is_absolute():
                        first_video_path = self.processed_dir.parent / first_video_path
                    
                    if first_video_path.exists():
                        processor = VideoProcessor(
                            target_frames=frames,
                            target_size=(height, width),
                            grayscale=True
                        )
                        
                        video_tensor = processor.process_video(str(first_video_path))
                        video_batch = video_tensor.unsqueeze(0)  # Add batch dimension
                        
                        print(f"📹 Real video shape: {video_batch.shape}")
                        
                        with torch.no_grad():
                            real_output = model(video_batch)
                        
                        print(f"📤 Real video output: {real_output.shape}")
                        print(f"📊 Output probabilities (first 5): {torch.softmax(real_output, dim=1)[0][:5].tolist()}")
                        print(f"✅ Real video test successful!")
            
            return {
                'success': True,
                'model_parameters': model.get_num_parameters(),
                'input_shape': test_input.shape,
                'output_shape': output.shape,
                'forward_pass_successful': True
            }
            
        except Exception as e:
            print(f"❌ Model compatibility test failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def verify_dataloader_integration(self) -> Dict:
        """Test dataloader creation with processed videos"""
        
        print(f"\n🗂️  Testing Dataloader Integration")
        print("=" * 35)
        
        if not self.manifest_path.exists():
            print(f"❌ Manifest not found for dataloader test")
            return {'success': False, 'error': 'Manifest not found'}
        
        try:
            # Update config for testing
            test_config = self.config.copy()
            test_config['batch_size'] = 2
            test_config['num_workers'] = 0
            test_config['augmentation'] = {'enabled': False}
            
            # Create dataloaders
            train_loader, val_loader, test_loader, data_info = create_dataloaders(
                test_config, manifest_path=str(self.manifest_path)
            )
            
            print(f"✅ Dataloaders created successfully")
            print(f"   Train samples: {data_info['train_size']}")
            print(f"   Val samples: {data_info['val_size']}")
            print(f"   Test samples: {data_info['test_size']}")
            
            # Test first batch
            print(f"\n🔍 Testing first batch...")
            
            for i, (videos, labels, metadata) in enumerate(train_loader):
                print(f"   Batch {i}: videos.shape = {videos.shape}, labels.shape = {labels.shape}")
                print(f"   Video value range: [{videos.min():.3f}, {videos.max():.3f}]")
                print(f"   Labels: {labels.tolist()}")
                
                if i >= 1:  # Test first 2 batches
                    break
            
            return {
                'success': True,
                'train_size': data_info['train_size'],
                'val_size': data_info['val_size'],
                'test_size': data_info['test_size'],
                'batch_shape': videos.shape,
                'dataloader_functional': True
            }
            
        except Exception as e:
            print(f"❌ Dataloader integration test failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _calculate_motion_score(self, video_tensor: torch.Tensor) -> float:
        """Calculate motion score for video tensor"""
        
        if video_tensor.shape[1] < 2:
            return 0.0
        
        motion_scores = []
        for i in range(1, video_tensor.shape[1]):
            diff = torch.abs(video_tensor[0, i] - video_tensor[0, i-1]).mean()
            motion_scores.append(diff.item())
        
        return np.mean(motion_scores)
    
    def run_full_verification(self) -> Dict:
        """Run complete integration verification"""
        
        print(f"🧪 Integration Verification Suite")
        print("=" * 40)
        
        results = {
            'video_processing': self.verify_video_processing(),
            'model_compatibility': self.verify_model_compatibility(),
            'dataloader_integration': self.verify_dataloader_integration()
        }
        
        # Overall assessment
        all_successful = all(result.get('success', False) for result in results.values())
        
        print(f"\n📋 Integration Verification Summary")
        print("=" * 35)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if all_successful else '❌ SOME TESTS FAILED'}")
        
        if all_successful:
            print(f"🎉 Processed videos are fully integrated with training pipeline!")
        else:
            print(f"⚠️  Some integration issues found. Review test results.")
        
        results['overall_success'] = all_successful
        
        return results

def main():
    """Main verification function"""
    
    processed_dir = "/Users/<USER>/Desktop/processed videos for training"
    manifest_path = f"{processed_dir}/processed_training_manifest.csv"
    
    verifier = IntegrationVerifier(processed_dir, manifest_path)
    results = verifier.run_full_verification()
    
    return results['overall_success']

if __name__ == '__main__':
    main()
