#!/usr/bin/env python3
"""
Video Quality Validation System for ICU Lipreading Dataset
Uses reference videos as gold standard for quality assessment
"""

import os
import json
import subprocess
import pandas as pd
import numpy as np
from pathlib import Path
import cv2
import torch
import sys
from typing import Dict, List, Tuple, Optional
import yaml

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

class VideoQualityValidator:
    """Validates video quality against reference standards"""
    
    def __init__(self, reference_manifest_path: str, config_path: str = "configs/phrases26.yaml"):
        """Initialize validator with reference videos and configuration"""
        
        # Load reference manifest
        self.reference_df = pd.read_csv(reference_manifest_path)
        print(f"Loaded {len(self.reference_df)} reference videos")
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Create video processor
        self.processor = VideoProcessor(
            target_frames=self.config.get('frames', 32),
            target_size=(self.config.get('height', 96), self.config.get('width', 96)),
            grayscale=self.config.get('grayscale', True)
        )
        
        # Quality thresholds
        self.quality_thresholds = {
            'min_resolution': (200, 100),  # Minimum width x height
            'max_resolution': (1920, 1080),  # Maximum width x height
            'min_duration': 1.0,  # Minimum duration in seconds
            'max_duration': 10.0,  # Maximum duration in seconds
            'min_fps': 15.0,  # Minimum frame rate
            'max_fps': 120.0,  # Maximum frame rate
            'min_file_size': 50 * 1024,  # Minimum file size in bytes (50KB)
            'max_file_size': 50 * 1024 * 1024,  # Maximum file size in bytes (50MB)
        }
        
        print(f"Quality validator initialized with {len(self.reference_df['phrase'].unique())} reference phrases")
    
    def get_video_technical_info(self, video_path: str) -> Optional[Dict]:
        """Extract technical information from video file"""
        
        try:
            # Use ffprobe to get video information
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(video_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                return None
            
            data = json.loads(result.stdout)
            
            # Find video stream
            video_stream = None
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if not video_stream:
                return None
            
            format_info = data.get('format', {})
            
            # Calculate duration and frame count using ffmpeg
            duration = self._get_video_duration(video_path)
            frame_count = self._get_frame_count(video_path)
            
            return {
                'width': video_stream.get('width'),
                'height': video_stream.get('height'),
                'codec': video_stream.get('codec_name'),
                'fps': eval(video_stream.get('r_frame_rate', '0/1')) if video_stream.get('r_frame_rate') else 0,
                'duration': duration,
                'frame_count': frame_count,
                'size_bytes': int(format_info.get('size', 0)),
                'pixel_format': video_stream.get('pix_fmt'),
                'has_audio': any(s.get('codec_type') == 'audio' for s in data.get('streams', []))
            }
            
        except Exception as e:
            print(f"Error analyzing {video_path}: {e}")
            return None
    
    def _get_video_duration(self, video_path: str) -> float:
        """Get video duration using ffmpeg"""
        try:
            cmd = [
                'ffmpeg', '-i', str(video_path), '-f', 'null', '-', 
                '-v', 'quiet', '-stats'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            # Parse duration from stderr
            for line in result.stderr.split('\n'):
                if 'time=' in line:
                    time_part = line.split('time=')[1].split()[0]
                    # Convert HH:MM:SS.ss to seconds
                    parts = time_part.split(':')
                    if len(parts) == 3:
                        hours, minutes, seconds = parts
                        return float(hours) * 3600 + float(minutes) * 60 + float(seconds)
            
            return 0.0
            
        except Exception:
            return 0.0
    
    def _get_frame_count(self, video_path: str) -> int:
        """Get frame count using ffmpeg"""
        try:
            cmd = [
                'ffmpeg', '-i', str(video_path), '-f', 'null', '-', 
                '-v', 'quiet', '-stats'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            # Parse frame count from stderr
            for line in result.stderr.split('\n'):
                if 'frame=' in line:
                    frame_part = line.split('frame=')[1].split()[0]
                    try:
                        return int(frame_part)
                    except:
                        continue
            
            return 0
            
        except Exception:
            return 0
    
    def validate_technical_quality(self, video_info: Dict) -> Tuple[bool, List[str]]:
        """Validate technical quality against thresholds"""
        
        issues = []
        
        # Check resolution
        width = video_info.get('width', 0)
        height = video_info.get('height', 0)
        
        if width < self.quality_thresholds['min_resolution'][0] or height < self.quality_thresholds['min_resolution'][1]:
            issues.append(f"Resolution too low: {width}x{height}")
        
        if width > self.quality_thresholds['max_resolution'][0] or height > self.quality_thresholds['max_resolution'][1]:
            issues.append(f"Resolution too high: {width}x{height}")
        
        # Check duration
        duration = video_info.get('duration', 0)
        if duration < self.quality_thresholds['min_duration']:
            issues.append(f"Duration too short: {duration:.1f}s")
        
        if duration > self.quality_thresholds['max_duration']:
            issues.append(f"Duration too long: {duration:.1f}s")
        
        # Check frame rate
        fps = video_info.get('fps', 0)
        if fps < self.quality_thresholds['min_fps']:
            issues.append(f"Frame rate too low: {fps:.1f} fps")
        
        if fps > self.quality_thresholds['max_fps']:
            issues.append(f"Frame rate too high: {fps:.1f} fps")
        
        # Check file size
        size_bytes = video_info.get('size_bytes', 0)
        if size_bytes < self.quality_thresholds['min_file_size']:
            issues.append(f"File size too small: {size_bytes/1024:.0f} KB")
        
        if size_bytes > self.quality_thresholds['max_file_size']:
            issues.append(f"File size too large: {size_bytes/(1024*1024):.1f} MB")
        
        # Check codec
        codec = video_info.get('codec', '')
        if codec not in ['h264', 'vp9', 'vp8', 'hevc']:
            issues.append(f"Unsupported codec: {codec}")
        
        return len(issues) == 0, issues
    
    def validate_processing_compatibility(self, video_path: str) -> Tuple[bool, List[str]]:
        """Test if video can be processed by the training pipeline"""
        
        issues = []
        
        try:
            # Try to process the video
            video_tensor = self.processor.process_video(video_path)
            
            # Check expected shape
            expected_shape = (
                1 if self.config.get('grayscale', True) else 3,
                self.config.get('frames', 32),
                self.config.get('height', 96),
                self.config.get('width', 96)
            )
            
            if video_tensor.shape != expected_shape:
                issues.append(f"Processing output shape mismatch: {video_tensor.shape} vs {expected_shape}")
            
            # Check for valid data (not all zeros)
            if torch.all(video_tensor == 0):
                issues.append("Processed video is all zeros")
            
            # Check for reasonable value range
            if video_tensor.max() > 1.5 or video_tensor.min() < -1.5:
                issues.append(f"Unusual pixel value range: [{video_tensor.min():.2f}, {video_tensor.max():.2f}]")
            
        except Exception as e:
            issues.append(f"Processing failed: {str(e)}")
        
        return len(issues) == 0, issues
    
    def compare_to_reference(self, video_path: str, phrase: str) -> Tuple[float, Dict]:
        """Compare video quality to reference videos for the same phrase"""
        
        # Get reference videos for this phrase
        ref_videos = self.reference_df[self.reference_df['phrase'] == phrase]
        
        if len(ref_videos) == 0:
            return 0.0, {'error': f'No reference videos found for phrase: {phrase}'}
        
        # Get technical info for the test video
        test_info = self.get_video_technical_info(video_path)
        if not test_info:
            return 0.0, {'error': 'Could not analyze test video'}
        
        # Compare with reference videos
        similarities = []
        ref_infos = []
        
        for _, ref_row in ref_videos.iterrows():
            ref_info = self.get_video_technical_info(ref_row['video_path'])
            if ref_info:
                ref_infos.append(ref_info)
                
                # Calculate similarity score
                similarity = self._calculate_similarity(test_info, ref_info)
                similarities.append(similarity)
        
        if not similarities:
            return 0.0, {'error': 'Could not analyze reference videos'}
        
        # Return average similarity and comparison details
        avg_similarity = np.mean(similarities)
        
        comparison_details = {
            'similarity_score': avg_similarity,
            'test_video': test_info,
            'reference_videos': ref_infos,
            'individual_similarities': similarities
        }
        
        return avg_similarity, comparison_details
    
    def _calculate_similarity(self, test_info: Dict, ref_info: Dict) -> float:
        """Calculate similarity score between test and reference video"""
        
        score = 0.0
        total_weight = 0.0
        
        # Resolution similarity (weight: 0.3)
        if test_info.get('width') and ref_info.get('width'):
            width_ratio = min(test_info['width'], ref_info['width']) / max(test_info['width'], ref_info['width'])
            height_ratio = min(test_info['height'], ref_info['height']) / max(test_info['height'], ref_info['height'])
            resolution_score = (width_ratio + height_ratio) / 2
            score += resolution_score * 0.3
            total_weight += 0.3
        
        # Duration similarity (weight: 0.2)
        if test_info.get('duration') and ref_info.get('duration'):
            duration_ratio = min(test_info['duration'], ref_info['duration']) / max(test_info['duration'], ref_info['duration'])
            score += duration_ratio * 0.2
            total_weight += 0.2
        
        # Frame rate similarity (weight: 0.2)
        if test_info.get('fps') and ref_info.get('fps'):
            fps_ratio = min(test_info['fps'], ref_info['fps']) / max(test_info['fps'], ref_info['fps'])
            score += fps_ratio * 0.2
            total_weight += 0.2
        
        # Codec similarity (weight: 0.1)
        if test_info.get('codec') == ref_info.get('codec'):
            score += 0.1
            total_weight += 0.1
        
        # File size similarity (weight: 0.2)
        if test_info.get('size_bytes') and ref_info.get('size_bytes'):
            size_ratio = min(test_info['size_bytes'], ref_info['size_bytes']) / max(test_info['size_bytes'], ref_info['size_bytes'])
            score += size_ratio * 0.2
            total_weight += 0.2
        
        return score / total_weight if total_weight > 0 else 0.0
    
    def validate_video(self, video_path: str, phrase: str) -> Dict:
        """Comprehensive video validation"""
        
        result = {
            'video_path': video_path,
            'phrase': phrase,
            'timestamp': pd.Timestamp.now().isoformat(),
            'overall_quality': 'UNKNOWN',
            'technical_quality': {'passed': False, 'issues': []},
            'processing_compatibility': {'passed': False, 'issues': []},
            'reference_comparison': {'similarity_score': 0.0, 'details': {}},
            'recommendation': 'REJECT'
        }
        
        # Technical quality check
        video_info = self.get_video_technical_info(video_path)
        if video_info:
            tech_passed, tech_issues = self.validate_technical_quality(video_info)
            result['technical_quality'] = {'passed': tech_passed, 'issues': tech_issues}
        else:
            result['technical_quality'] = {'passed': False, 'issues': ['Could not analyze video file']}
        
        # Processing compatibility check
        proc_passed, proc_issues = self.validate_processing_compatibility(video_path)
        result['processing_compatibility'] = {'passed': proc_passed, 'issues': proc_issues}
        
        # Reference comparison
        similarity_score, comparison_details = self.compare_to_reference(video_path, phrase)
        result['reference_comparison'] = {
            'similarity_score': similarity_score,
            'details': comparison_details
        }
        
        # Overall assessment
        if result['technical_quality']['passed'] and result['processing_compatibility']['passed']:
            if similarity_score >= 0.8:
                result['overall_quality'] = 'EXCELLENT'
                result['recommendation'] = 'ACCEPT'
            elif similarity_score >= 0.6:
                result['overall_quality'] = 'GOOD'
                result['recommendation'] = 'ACCEPT'
            elif similarity_score >= 0.4:
                result['overall_quality'] = 'FAIR'
                result['recommendation'] = 'REVIEW'
            else:
                result['overall_quality'] = 'POOR'
                result['recommendation'] = 'REJECT'
        else:
            result['overall_quality'] = 'FAILED'
            result['recommendation'] = 'REJECT'
        
        return result

def main():
    """Test the validator with reference videos"""
    
    print("🔧 Testing Video Quality Validator")
    print("=" * 50)
    
    # Initialize validator
    validator = VideoQualityValidator('reference_videos_manifest.csv')
    
    # Test with a reference video
    test_video = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    test_phrase = "where am i"
    
    print(f"Testing with: {Path(test_video).name}")
    
    # Validate
    result = validator.validate_video(test_video, test_phrase)
    
    print(f"\n📊 Validation Results:")
    print(f"   Overall Quality: {result['overall_quality']}")
    print(f"   Recommendation: {result['recommendation']}")
    print(f"   Technical Quality: {'✅' if result['technical_quality']['passed'] else '❌'}")
    print(f"   Processing Compatible: {'✅' if result['processing_compatibility']['passed'] else '❌'}")
    print(f"   Reference Similarity: {result['reference_comparison']['similarity_score']:.2f}")
    
    if result['technical_quality']['issues']:
        print(f"\n⚠️  Technical Issues:")
        for issue in result['technical_quality']['issues']:
            print(f"     - {issue}")
    
    if result['processing_compatibility']['issues']:
        print(f"\n⚠️  Processing Issues:")
        for issue in result['processing_compatibility']['issues']:
            print(f"     - {issue}")
    
    print(f"\n✅ Validator test complete!")

if __name__ == '__main__':
    main()
