#!/usr/bin/env python3
"""
Debug script to check video dimensions and fix preprocessing issues
"""

import pandas as pd
import torch
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor
import yaml

def check_video_dimensions():
    """Check dimensions of all videos in the manifest"""
    
    print("🔍 Checking video dimensions...")
    
    # Load config
    with open('configs/phrases26.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Load manifest
    df = pd.read_csv('data/manifest.csv')

    # Make df available globally for the test function
    global df_global
    df_global = df
    
    # Create video processor
    processor = VideoProcessor(
        target_frames=config.get('frames', 32),
        target_size=(config.get('height', 96), config.get('width', 96)),
        grayscale=config.get('grayscale', True)
    )
    
    print(f"Target dimensions: {config.get('frames', 32)} frames, {config.get('height', 96)}x{config.get('width', 96)}")
    print(f"Processing {len(df)} videos...")
    
    issues = []
    
    for idx, row in df.iterrows():
        video_path = row['video_path']
        
        if not Path(video_path).exists():
            issues.append(f"❌ File not found: {video_path}")
            continue
            
        try:
            # Process video
            video_tensor = processor.process_video(video_path)
            
            expected_shape = (1 if config.get('grayscale', True) else 3, 
                            config.get('frames', 32),
                            config.get('height', 96), 
                            config.get('width', 96))
            
            if video_tensor.shape != expected_shape:
                issues.append(f"❌ Wrong shape: {video_path} -> {video_tensor.shape} (expected {expected_shape})")
            else:
                if idx < 5:  # Show first 5 successful ones
                    print(f"✅ {video_path} -> {video_tensor.shape}")
                    
        except Exception as e:
            issues.append(f"❌ Processing error: {video_path} -> {str(e)}")
    
    print(f"\n📊 Summary:")
    print(f"   Total videos: {len(df)}")
    print(f"   Issues found: {len(issues)}")
    
    if issues:
        print(f"\n🚨 Issues:")
        for issue in issues[:10]:  # Show first 10 issues
            print(f"   {issue}")
        if len(issues) > 10:
            print(f"   ... and {len(issues) - 10} more issues")
    else:
        print(f"✅ All videos have correct dimensions!")
    
    return len(issues) == 0

def test_dataloader():
    """Test the dataloader to see where the issue occurs"""
    
    print("\n🧪 Testing dataloader...")
    
    # Load config
    with open('configs/phrases26.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    config['manifest_path'] = 'data/manifest.csv'
    config['batch_size'] = 2  # Small batch for testing
    config['num_workers'] = 0  # No multiprocessing for debugging
    
    try:
        from backend.lightweight_vsr.dataset import create_dataloaders

        train_loader, val_loader, test_loader, data_info = create_dataloaders(
            config, manifest_path='data/manifest.csv'
        )
        
        print(f"✅ Dataloaders created successfully")
        print(f"   Train size: {data_info['train_size']}")
        print(f"   Val size: {data_info['val_size']}")
        print(f"   Test size: {data_info['test_size']}")
        
        # Test first batch
        print(f"\n🔍 Testing first batch...")

        # Test individual samples first
        print(f"Testing individual samples...")
        from backend.lightweight_vsr.dataset import ICUVideoDataset

        # Create a small test dataset
        test_paths = df_global['video_path'].tolist()[:5]
        test_phrases = df_global['phrase'].tolist()[:5]
        phrase_to_idx = {phrase: idx for idx, phrase in enumerate(config['phrases'])}
        test_labels = [phrase_to_idx[phrase] for phrase in test_phrases]

        test_dataset = ICUVideoDataset(
            test_paths, test_labels, phrase_to_idx, config, df_global[:5], augment=False
        )

        for i in range(min(5, len(test_dataset))):
            try:
                video, label, metadata = test_dataset[i]
                print(f"   Sample {i}: {metadata['video_path']} -> {video.shape}")
            except Exception as e:
                print(f"   Sample {i}: ERROR -> {e}")

        # Now test dataloader
        for i, (videos, labels, metadata) in enumerate(train_loader):
            print(f"   Batch {i}: videos.shape = {videos.shape}, labels.shape = {labels.shape}")
            if i >= 2:  # Test first 3 batches
                break
        
        print(f"✅ Dataloader test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Dataloader test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    
    print("🔧 Video Dimension Debug Tool")
    print("=" * 40)
    
    # Check video dimensions
    dimensions_ok = check_video_dimensions()
    
    # Test dataloader
    dataloader_ok = test_dataloader()
    
    print(f"\n📋 Results:")
    print(f"   Video dimensions: {'✅' if dimensions_ok else '❌'}")
    print(f"   Dataloader test: {'✅' if dataloader_ok else '❌'}")
    
    if dimensions_ok and dataloader_ok:
        print(f"\n🎉 All checks passed! Training should work.")
    else:
        print(f"\n🚨 Issues found. Please fix before training.")

if __name__ == '__main__':
    main()
