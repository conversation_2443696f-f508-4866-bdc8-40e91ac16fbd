#!/usr/bin/env python3
"""
LipNet architecture adapted for Perfect 10 ICU phrase classification
Implements 3D CNN + BiLSTM/BiGRU for superior lip reading performance on 10 high-accuracy phrases
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from pathlib import Path
import sys
import json
import math

# Add current directory to path
sys.path.append('.')

class LipNet3DCNN(nn.Module):
    """3D CNN backbone for LipNet - extracts spatiotemporal features from lip videos"""
    
    def __init__(self, input_channels=1):
        super(LipNet3DCNN, self).__init__()
        
        # 3D Convolutional layers for spatiotemporal feature extraction
        # Input: (batch, 1, 64, 112, 112) - enhanced dimensions
        
        # Conv3D Block 1: Extract basic lip features
        self.conv3d_1 = nn.Conv3d(input_channels, 32, kernel_size=(3, 7, 7), 
                                  stride=(1, 2, 2), padding=(1, 3, 3))
        self.bn3d_1 = nn.BatchNorm3d(32)
        self.pool3d_1 = nn.MaxPool3d(kernel_size=(1, 2, 2), stride=(1, 2, 2))
        
        # Conv3D Block 2: Extract intermediate features
        self.conv3d_2 = nn.Conv3d(32, 64, kernel_size=(3, 5, 5), 
                                  stride=(1, 1, 1), padding=(1, 2, 2))
        self.bn3d_2 = nn.BatchNorm3d(64)
        self.pool3d_2 = nn.MaxPool3d(kernel_size=(1, 2, 2), stride=(1, 2, 2))
        
        # Conv3D Block 3: Extract high-level features
        self.conv3d_3 = nn.Conv3d(64, 96, kernel_size=(3, 3, 3), 
                                  stride=(1, 1, 1), padding=(1, 1, 1))
        self.bn3d_3 = nn.BatchNorm3d(96)
        self.pool3d_3 = nn.MaxPool3d(kernel_size=(1, 2, 2), stride=(1, 2, 2))
        
        # Dropout for regularization
        self.dropout3d = nn.Dropout3d(0.2)
        
        print(f"🧠 LipNet 3D CNN initialized")
        print(f"   Input: (batch, 1, 64, 112, 112)")
        print(f"   Output features: 96 channels")
    
    def forward(self, x):
        """Forward pass through 3D CNN backbone"""
        
        # Block 1: (B, 1, 64, 112, 112) -> (B, 32, 64, 28, 28)
        x = F.relu(self.bn3d_1(self.conv3d_1(x)))
        x = self.pool3d_1(x)
        
        # Block 2: (B, 32, 64, 28, 28) -> (B, 64, 64, 14, 14)
        x = F.relu(self.bn3d_2(self.conv3d_2(x)))
        x = self.pool3d_2(x)
        
        # Block 3: (B, 64, 64, 14, 14) -> (B, 96, 64, 7, 7)
        x = F.relu(self.bn3d_3(self.conv3d_3(x)))
        x = self.pool3d_3(x)
        
        # Apply dropout
        x = self.dropout3d(x)
        
        return x

class LipNetPerfect10(nn.Module):
    """
    LipNet architecture adapted for Perfect 10 ICU phrase classification
    
    Architecture:
    - 3D CNN backbone for spatiotemporal feature extraction
    - Spatial Global Average Pooling to reduce spatial dimensions
    - Bidirectional LSTM/GRU for temporal sequence modeling
    - Classification head for 10 Perfect ICU phrases
    """
    
    def __init__(self, 
                 num_classes=10,
                 hidden_dim=256,
                 num_rnn_layers=2,
                 rnn_type='LSTM',  # 'LSTM' or 'GRU'
                 dropout=0.3,
                 pretrained_path=None):
        super(LipNetPerfect10, self).__init__()
        
        self.num_classes = num_classes
        self.hidden_dim = hidden_dim
        self.num_rnn_layers = num_rnn_layers
        self.rnn_type = rnn_type
        
        # Perfect 10 phrases mapping
        self.perfect_phrases = [
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today"
        ]
        
        # 3D CNN backbone for feature extraction
        self.cnn_backbone = LipNet3DCNN(input_channels=1)
        
        # Spatial Global Average Pooling to reduce spatial dimensions
        # Input: (B, 96, T, 7, 7) -> Output: (B, 96, T)
        self.spatial_gap = nn.AdaptiveAvgPool3d((None, 1, 1))
        
        # Bidirectional RNN for temporal modeling
        if rnn_type == 'LSTM':
            self.rnn = nn.LSTM(
                input_size=96,  # From 3D CNN output
                hidden_size=hidden_dim,
                num_layers=num_rnn_layers,
                batch_first=True,
                bidirectional=True,
                dropout=dropout if num_rnn_layers > 1 else 0
            )
        else:  # GRU
            self.rnn = nn.GRU(
                input_size=96,
                hidden_size=hidden_dim,
                num_layers=num_rnn_layers,
                batch_first=True,
                bidirectional=True,
                dropout=dropout if num_rnn_layers > 1 else 0
            )
        
        # Temporal attention mechanism (optional enhancement)
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim * 2,  # *2 for bidirectional
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # Classification head
        self.layer_norm = nn.LayerNorm(hidden_dim * 2)
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, num_classes)
        )
        
        # Initialize weights
        self._init_weights()
        
        # Load pretrained weights if provided
        if pretrained_path and Path(pretrained_path).exists():
            self.load_pretrained_weights(pretrained_path)
        
        print(f"🎯 LipNet Perfect 10 initialized")
        print(f"   Classes: {num_classes}")
        print(f"   RNN type: {rnn_type}")
        print(f"   Hidden dim: {hidden_dim}")
        print(f"   RNN layers: {num_rnn_layers}")
        print(f"   Dropout: {dropout}")
    
    def _init_weights(self):
        """Initialize model weights"""
        for m in self.modules():
            if isinstance(m, (nn.Conv3d, nn.Linear)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm3d, nn.LayerNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.LSTM, nn.GRU)):
                for name, param in m.named_parameters():
                    if 'weight' in name:
                        nn.init.orthogonal_(param)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0)
    
    def load_pretrained_weights(self, pretrained_path: str):
        """Load pretrained LipNet weights and adapt for 10-class classification"""
        
        print(f"🔄 Loading pretrained LipNet weights from: {pretrained_path}")
        
        try:
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            
            # Extract model state dict
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint
            
            # Load compatible weights (exclude classifier for different output dimensions)
            model_dict = self.state_dict()
            pretrained_dict = {}
            
            for k, v in state_dict.items():
                # Skip classifier weights (different output dimensions)
                if 'classifier' not in k and 'fc' not in k and 'linear' not in k:
                    if k in model_dict and model_dict[k].shape == v.shape:
                        pretrained_dict[k] = v
                        print(f"   Loaded: {k} {v.shape}")
                    else:
                        print(f"   Skipped: {k} (shape mismatch or not found)")
            
            # Update model dict
            model_dict.update(pretrained_dict)
            self.load_state_dict(model_dict, strict=False)
            
            print(f"✅ Transfer learning: Loaded {len(pretrained_dict)} layers")
            
        except Exception as e:
            print(f"⚠️  Failed to load pretrained weights: {e}")
    
    def forward(self, x):
        """
        Forward pass through LipNet Perfect 10
        
        Args:
            x: Input tensor of shape (batch, 1, frames, height, width)
            
        Returns:
            logits: Classification logits of shape (batch, num_classes)
        """
        batch_size = x.size(0)
        
        # 3D CNN feature extraction
        # Input: (B, 1, 64, 112, 112) -> Output: (B, 96, 64, 7, 7)
        cnn_features = self.cnn_backbone(x)
        
        # Spatial Global Average Pooling
        # Input: (B, 96, 64, 7, 7) -> Output: (B, 96, 64, 1, 1) -> (B, 96, 64)
        pooled_features = self.spatial_gap(cnn_features).squeeze(-1).squeeze(-1)
        
        # Rearrange for RNN: (B, 96, T) -> (B, T, 96)
        rnn_input = pooled_features.permute(0, 2, 1)
        
        # Bidirectional RNN for temporal modeling
        # Input: (B, T, 96) -> Output: (B, T, hidden_dim*2)
        rnn_output, _ = self.rnn(rnn_input)
        
        # Apply attention mechanism (optional enhancement)
        attended_output, _ = self.attention(rnn_output, rnn_output, rnn_output)
        
        # Temporal pooling: take mean across time dimension
        # Input: (B, T, hidden_dim*2) -> Output: (B, hidden_dim*2)
        temporal_features = torch.mean(attended_output, dim=1)
        
        # Classification head
        temporal_features = self.layer_norm(temporal_features)
        temporal_features = self.dropout(temporal_features)
        logits = self.classifier(temporal_features)
        
        return logits
    
    def get_num_parameters(self):
        """Get total number of parameters"""
        return sum(p.numel() for p in self.parameters())
    
    def freeze_backbone(self, freeze=True):
        """Freeze/unfreeze CNN backbone for fine-tuning"""
        
        for param in self.cnn_backbone.parameters():
            param.requires_grad = not freeze
        
        status = "frozen" if freeze else "unfrozen"
        print(f"🔒 CNN backbone {status}")

class LipNetPerfect10Manager:
    """Manager for LipNet Perfect 10 model operations"""
    
    def __init__(self, config: dict = None):
        """Initialize LipNet manager"""
        
        self.config = config or {}
        
        # Perfect 10 phrases info
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        self.num_classes = len(self.perfect_phrases)
        
        print(f"🎯 LipNet Perfect 10 Manager Initialized")
        print(f"   Perfect phrases: {self.num_classes}")
    
    def create_model(self, **kwargs) -> LipNetPerfect10:
        """Create LipNet Perfect 10 model"""
        
        print(f"\n🤖 Creating LipNet Perfect 10 Model")
        print("=" * 35)
        
        # Default parameters
        model_params = {
            'num_classes': self.num_classes,
            'hidden_dim': 256,
            'num_rnn_layers': 2,
            'rnn_type': 'LSTM',
            'dropout': 0.3
        }
        
        # Update with provided parameters
        model_params.update(kwargs)
        
        # Create model
        model = LipNetPerfect10(**model_params)
        
        print(f"✅ LipNet model created with {model.get_num_parameters():,} parameters")
        
        return model
    
    def save_model(self, model: LipNetPerfect10, save_path: str, 
                   epoch: int = 0, best_accuracy: float = 0.0, **kwargs):
        """Save LipNet Perfect 10 model checkpoint"""
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'num_classes': self.num_classes,
            'perfect_phrases': self.perfect_phrases,
            'phrase_to_idx': self.phrase_to_idx,
            'best_accuracy': best_accuracy,
            'model_info': {
                'name': 'LipNetPerfect10',
                'architecture': 'LipNet (3D CNN + BiLSTM)',
                'parameters': model.get_num_parameters(),
                'rnn_type': model.rnn_type,
                'hidden_dim': model.hidden_dim,
                'num_rnn_layers': model.num_rnn_layers,
                'focus': 'Perfect 10 ICU phrases with >95% target accuracy'
            }
        }
        
        # Add any additional info
        checkpoint.update(kwargs)
        
        torch.save(checkpoint, save_path)
        print(f"💾 LipNet Perfect 10 model saved: {save_path}")

def test_lipnet_perfect_10():
    """Test LipNet Perfect 10 model creation and forward pass"""
    
    print("🧪 Testing LipNet Perfect 10 Model")
    print("=" * 35)
    
    # Create model manager
    manager = LipNetPerfect10Manager()
    
    # Create model
    model = manager.create_model(
        hidden_dim=256,
        num_rnn_layers=2,
        rnn_type='LSTM',
        dropout=0.3
    )
    
    # Test forward pass with enhanced dimensions
    batch_size = 2
    frames = 64      # Enhanced: 64 frames
    height = 112     # Enhanced: 112×112
    width = 112
    
    dummy_input = torch.randn(batch_size, 1, frames, height, width)
    
    model.eval()
    with torch.no_grad():
        output = model(dummy_input)
    
    print(f"✅ Forward pass test:")
    print(f"   Input shape: {dummy_input.shape}")
    print(f"   Output shape: {output.shape}")
    print(f"   Expected classes: {manager.num_classes}")
    
    # Test model saving
    test_save_path = "test_lipnet_perfect_10.pth"
    manager.save_model(model, test_save_path, epoch=0, best_accuracy=0.0)
    
    # Clean up test file
    Path(test_save_path).unlink()
    
    print(f"\n🎉 LipNet Perfect 10 Test Successful!")
    print(f"🏆 Ready for superior lip reading on Perfect 10 phrases!")
    
    return True

def main():
    """Main function"""
    
    # Test LipNet Perfect 10 model
    success = test_lipnet_perfect_10()
    
    if success:
        print(f"\n🚀 LipNet Perfect 10 Ready!")
        print("=" * 30)
        print("✅ LipNet architecture implemented")
        print("✅ 3D CNN + BiLSTM/BiGRU backbone")
        print("✅ 10-class Perfect phrase classification")
        print("✅ Enhanced dimensions support (64 frames, 112×112)")
        print("✅ Transfer learning capability")
        print("✅ Attention mechanism included")
        
        print(f"\n🏆 LipNet Advantages:")
        print("✅ Superior spatiotemporal modeling")
        print("✅ Proven lip reading architecture")
        print("✅ Enhanced temporal attention")
        print("✅ Target >95% accuracy on Perfect 10")
    else:
        print(f"\n❌ LipNet Perfect 10 setup failed")

if __name__ == '__main__':
    main()
