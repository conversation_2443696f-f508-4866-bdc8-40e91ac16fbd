#!/usr/bin/env python3
"""
Debug video content and find correct mouth-cropping coordinates
Analyzes the actual video frames to determine proper mouth region
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import sys

def analyze_video_frame(video_path: str, frame_number: int = 10):
    """Analyze a specific frame from the video to understand layout"""
    
    print(f"\n🔍 Analyzing frame {frame_number} from: {Path(video_path).name}")
    
    cap = cv2.VideoCapture(video_path)
    
    # Get video info
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"   📊 Video: {width}×{height}, {total_frames} frames")
    
    # Read specific frame
    cap.set(cv2.CAP_PROP_POS_FRAMES, min(frame_number, total_frames-1))
    ret, frame = cap.read()
    
    if not ret:
        print(f"   ❌ Could not read frame {frame_number}")
        cap.release()
        return None
    
    cap.release()
    
    # Convert BGR to RGB for display
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    
    # Create visualization with grid overlay
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(f'Video Frame Analysis: {Path(video_path).name} - Frame {frame_number}', fontsize=14)
    
    # Original frame
    axes[0, 0].imshow(frame_rgb)
    axes[0, 0].set_title(f'Original Frame ({width}×{height})')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Add grid lines for 3×2 layout
    axes[0, 0].axvline(x=width//3, color='red', linestyle='--', alpha=0.7)
    axes[0, 0].axvline(x=2*width//3, color='red', linestyle='--', alpha=0.7)
    axes[0, 0].axhline(y=height//2, color='red', linestyle='--', alpha=0.7)
    
    # Test different cropping regions
    crop_regions = [
        {'name': 'ICU Spec (133,0,133,100)', 'x': 133, 'y': 0, 'w': 133, 'h': 100},
        {'name': 'Middle Right (267,0,133,100)', 'x': 267, 'y': 0, 'w': 133, 'h': 100},
        {'name': 'Center (133,50,134,100)', 'x': 133, 'y': 50, 'w': 134, 'h': 100},
        {'name': 'Lower Center (133,100,134,100)', 'x': 133, 'y': 100, 'w': 134, 'h': 100},
        {'name': 'Full Center (100,25,200,150)', 'x': 100, 'y': 25, 'w': 200, 'h': 150}
    ]
    
    for i, region in enumerate(crop_regions):
        if i >= 5:  # Only show first 5 regions
            break
            
        x, y, w, h = region['x'], region['y'], region['w'], region['h']
        
        # Ensure coordinates are within bounds
        x = max(0, min(x, width - 1))
        y = max(0, min(y, height - 1))
        x_end = min(x + w, width)
        y_end = min(y + h, height)
        
        # Extract region
        cropped = frame_rgb[y:y_end, x:x_end]
        
        # Plot in grid
        row = (i + 1) // 3
        col = (i + 1) % 3
        
        if row < 2 and col < 3:
            axes[row, col].imshow(cropped)
            axes[row, col].set_title(f'{region["name"]}\n{cropped.shape[1]}×{cropped.shape[0]}')
            
            # Check if region is mostly black
            gray_crop = cv2.cvtColor(cropped, cv2.COLOR_RGB2GRAY) if len(cropped.shape) == 3 else cropped
            mean_intensity = np.mean(gray_crop)
            axes[row, col].text(0.02, 0.98, f'Mean: {mean_intensity:.1f}', 
                               transform=axes[row, col].transAxes, 
                               verticalalignment='top',
                               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # Draw rectangle on original frame
        axes[0, 0].add_patch(plt.Rectangle((x, y), x_end-x, y_end-y, 
                                         fill=False, edgecolor='blue', linewidth=2, alpha=0.7))
        axes[0, 0].text(x, y-5, region['name'], color='blue', fontsize=8)
    
    # Remove empty subplots
    for i in range(len(crop_regions), 5):
        row = (i + 1) // 3
        col = (i + 1) % 3
        if row < 2 and col < 3:
            axes[row, col].axis('off')
    
    plt.tight_layout()
    
    # Save analysis
    output_path = f"frame_analysis_{Path(video_path).stem}.png"
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    print(f"   💾 Frame analysis saved: {output_path}")
    
    plt.show()
    
    return frame_rgb

def find_optimal_mouth_region(video_path: str):
    """Find the optimal mouth region by analyzing video content"""
    
    print(f"\n🎯 Finding optimal mouth region for: {Path(video_path).name}")
    
    cap = cv2.VideoCapture(video_path)
    
    # Sample a few frames
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    sample_frames = [total_frames//4, total_frames//2, 3*total_frames//4]
    
    print(f"   📊 Analyzing {len(sample_frames)} sample frames from {total_frames} total")
    
    # Test different regions and find the one with most variation (likely mouth area)
    test_regions = [
        {'name': 'Left Third', 'x': 0, 'y': 0, 'w': width//3, 'h': height},
        {'name': 'Center Third', 'x': width//3, 'y': 0, 'w': width//3, 'h': height},
        {'name': 'Right Third', 'x': 2*width//3, 'y': 0, 'w': width//3, 'h': height},
        {'name': 'Center Square', 'x': width//4, 'y': height//4, 'w': width//2, 'h': height//2},
        {'name': 'Lower Center', 'x': width//3, 'y': height//2, 'w': width//3, 'h': height//2}
    ]
    
    region_stats = []
    
    for region in test_regions:
        x, y, w, h = region['x'], region['y'], region['w'], region['h']
        
        # Ensure bounds
        x = max(0, min(x, width - 1))
        y = max(0, min(y, height - 1))
        x_end = min(x + w, width)
        y_end = min(y + h, height)
        
        frame_intensities = []
        
        for frame_idx in sample_frames:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Extract region and convert to grayscale
                region_crop = frame[y:y_end, x:x_end]
                gray_crop = cv2.cvtColor(region_crop, cv2.COLOR_BGR2GRAY)
                mean_intensity = np.mean(gray_crop)
                frame_intensities.append(mean_intensity)
        
        if frame_intensities:
            variance = np.var(frame_intensities)
            mean_val = np.mean(frame_intensities)
            
            region_stats.append({
                'name': region['name'],
                'coords': (x, y, x_end-x, y_end-y),
                'mean_intensity': mean_val,
                'variance': variance,
                'score': variance * mean_val  # Higher score = more variation and brighter
            })
    
    cap.release()
    
    # Sort by score (variance * mean intensity)
    region_stats.sort(key=lambda x: x['score'], reverse=True)
    
    print(f"   📊 Region analysis results:")
    for i, stat in enumerate(region_stats):
        print(f"   {i+1}. {stat['name']}: coords{stat['coords']}, mean={stat['mean_intensity']:.1f}, var={stat['variance']:.1f}, score={stat['score']:.1f}")
    
    best_region = region_stats[0]
    print(f"   🏆 Best region: {best_region['name']} at {best_region['coords']}")
    
    return best_region

def main():
    """Main function to debug video content"""
    
    print("🔍 Video Content Debug and Mouth Region Analysis")
    print("=" * 55)
    
    # Test video path
    video_folder = "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on"
    test_video = Path(video_folder) / "1.webm"
    
    if not test_video.exists():
        print(f"❌ Test video not found: {test_video}")
        return
    
    # Analyze frame content
    frame = analyze_video_frame(str(test_video), frame_number=10)
    
    if frame is not None:
        # Find optimal mouth region
        best_region = find_optimal_mouth_region(str(test_video))
        
        print(f"\n🎯 Recommended mouth-cropping coordinates:")
        print(f"   Best region: {best_region['coords']}")
        print(f"   Format: (x, y, width, height)")
        
        # Test a few more videos
        for i in range(2, 4):
            test_video_i = Path(video_folder) / f"{i}.webm"
            if test_video_i.exists():
                best_region_i = find_optimal_mouth_region(str(test_video_i))
                print(f"   Video {i}: {best_region_i['coords']}")

if __name__ == '__main__':
    main()
