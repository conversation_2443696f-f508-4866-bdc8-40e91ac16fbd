#!/usr/bin/env python3
"""
Integration test for mouth-cropped videos with training pipeline
"""

import torch
import pandas as pd
import numpy as np
from pathlib import Path
import sys
import yaml

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor
from backend.lightweight_vsr.dataset import create_dataloaders
from backend.lightweight_vsr.model import Mobile3DTiny

def test_mouth_cropped_video_processing():
    """Test processing of mouth-cropped videos"""
    
    print("🧪 Testing Mouth-Cropped Video Processing")
    print("=" * 50)
    
    # Test with original processor (no mouth crop)
    print("📹 Testing original video processing...")
    original_processor = VideoProcessor(target_frames=32, target_size=(96, 96), grayscale=True)
    
    original_video = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    
    if Path(original_video).exists():
        try:
            orig_tensor = original_processor.process_video(original_video)
            print(f"   ✅ Original video processed: {orig_tensor.shape}")
            print(f"   📊 Value range: [{orig_tensor.min():.3f}, {orig_tensor.max():.3f}]")
        except Exception as e:
            print(f"   ❌ Original processing failed: {e}")
            orig_tensor = None
    else:
        print(f"   ⚠️  Original video not found")
        orig_tensor = None
    
    # Test with mouth-cropped video
    print(f"\n📹 Testing mouth-cropped video processing...")
    cropped_video = "mouth_cropped_videos/where_am_i__useruser01__18to39__male__not_specified__20250809T053449_mouth_cropped.webm"
    
    if Path(cropped_video).exists():
        try:
            crop_tensor = original_processor.process_video(cropped_video)
            print(f"   ✅ Cropped video processed: {crop_tensor.shape}")
            print(f"   📊 Value range: [{crop_tensor.min():.3f}, {crop_tensor.max():.3f}]")
        except Exception as e:
            print(f"   ❌ Cropped processing failed: {e}")
            crop_tensor = None
    else:
        print(f"   ❌ Cropped video not found")
        crop_tensor = None
    
    # Compare results
    if orig_tensor is not None and crop_tensor is not None:
        print(f"\n📊 Comparison:")
        print(f"   Original shape: {orig_tensor.shape}")
        print(f"   Cropped shape: {crop_tensor.shape}")
        print(f"   Shapes match: {orig_tensor.shape == crop_tensor.shape}")
        
        # Calculate motion scores
        orig_motion = calculate_motion_score(orig_tensor)
        crop_motion = calculate_motion_score(crop_tensor)
        
        print(f"   Original motion: {orig_motion:.4f}")
        print(f"   Cropped motion: {crop_motion:.4f}")
        print(f"   Motion ratio: {crop_motion/orig_motion:.2f}x" if orig_motion > 0 else "   Motion ratio: N/A")
    
    return crop_tensor is not None

def calculate_motion_score(video_tensor):
    """Calculate motion score for video tensor"""
    
    if video_tensor.shape[1] < 2:
        return 0.0
    
    motion_scores = []
    for i in range(1, video_tensor.shape[1]):
        diff = torch.abs(video_tensor[0, i] - video_tensor[0, i-1]).mean()
        motion_scores.append(diff.item())
    
    return np.mean(motion_scores)

def test_mouth_cropped_dataset():
    """Test creating dataset with mouth-cropped videos"""
    
    print(f"\n🗂️  Testing Mouth-Cropped Dataset Creation")
    print("=" * 50)
    
    # Create manifest for mouth-cropped videos
    cropped_dir = Path("mouth_cropped_videos")
    cropped_videos = list(cropped_dir.glob("*_mouth_cropped.webm"))
    
    if not cropped_videos:
        print("❌ No mouth-cropped videos found")
        return False
    
    print(f"📊 Found {len(cropped_videos)} mouth-cropped videos")
    
    # Create a test manifest
    manifest_data = []
    for video_path in cropped_videos[:20]:  # Test with first 20 videos
        # Extract phrase from filename
        filename = video_path.stem.replace("_mouth_cropped", "")
        phrase = filename.split("__")[0].replace("_", " ")
        
        manifest_data.append({
            'video_path': str(video_path),
            'phrase': phrase,
            'speaker_id': 'useruser01',
            'age_group': '18to39',
            'gender': 'male',
            'ethnicity': 'not_specified'
        })
    
    # Save test manifest
    test_manifest_path = "test_mouth_cropped_manifest.csv"
    test_df = pd.DataFrame(manifest_data)
    test_df.to_csv(test_manifest_path, index=False)
    
    print(f"💾 Test manifest created: {test_manifest_path}")
    print(f"📊 Phrases in test set: {test_df['phrase'].nunique()}")
    
    # Test dataloader creation
    try:
        # Load config
        with open('configs/phrases26.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Update config for testing
        config['batch_size'] = 2
        config['num_workers'] = 0
        config['augmentation'] = {'enabled': False}  # Disable augmentation for testing
        
        # Create dataloaders
        train_loader, val_loader, test_loader, data_info = create_dataloaders(
            config, manifest_path=test_manifest_path
        )
        
        print(f"✅ Dataloaders created successfully")
        print(f"   Train samples: {data_info['train_size']}")
        print(f"   Val samples: {data_info['val_size']}")
        print(f"   Test samples: {data_info['test_size']}")
        
        # Test first batch
        print(f"\n🔍 Testing first batch...")
        for i, (videos, labels, metadata) in enumerate(train_loader):
            print(f"   Batch {i}: videos.shape = {videos.shape}, labels.shape = {labels.shape}")
            
            # Verify tensor properties
            print(f"   Video value range: [{videos.min():.3f}, {videos.max():.3f}]")
            print(f"   Labels: {labels.tolist()}")
            
            if i >= 1:  # Test first 2 batches
                break
        
        print(f"✅ Dataset test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Dataset test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_compatibility():
    """Test model compatibility with mouth-cropped videos"""
    
    print(f"\n🤖 Testing Model Compatibility")
    print("=" * 50)
    
    try:
        # Create model
        model = Mobile3DTiny(num_classes=26)
        model.eval()
        
        print(f"✅ Model created: {model.get_num_parameters():,} parameters")
        
        # Create test input (simulating mouth-cropped video)
        test_input = torch.randn(2, 1, 32, 96, 96)  # Batch of 2 videos
        
        print(f"📥 Test input shape: {test_input.shape}")
        
        # Forward pass
        with torch.no_grad():
            output = model(test_input)
        
        print(f"📤 Model output shape: {output.shape}")
        print(f"✅ Model forward pass successful!")
        
        # Test with actual mouth-cropped video
        cropped_video = "mouth_cropped_videos/where_am_i__useruser01__18to39__male__not_specified__20250809T053449_mouth_cropped.webm"
        
        if Path(cropped_video).exists():
            processor = VideoProcessor(target_frames=32, target_size=(96, 96), grayscale=True)
            video_tensor = processor.process_video(cropped_video)
            
            # Add batch dimension
            video_batch = video_tensor.unsqueeze(0)
            
            print(f"📹 Real video shape: {video_batch.shape}")
            
            with torch.no_grad():
                real_output = model(video_batch)
            
            print(f"📤 Real video output: {real_output.shape}")
            print(f"📊 Output probabilities: {torch.softmax(real_output, dim=1)[0][:5].tolist()}")  # First 5 classes
            print(f"✅ Real video test successful!")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_pipeline_integration():
    """Test complete training pipeline with mouth-cropped videos"""
    
    print(f"\n🎯 Testing Complete Training Pipeline Integration")
    print("=" * 50)
    
    try:
        # Use the test manifest created earlier
        test_manifest_path = "test_mouth_cropped_manifest.csv"
        
        if not Path(test_manifest_path).exists():
            print("❌ Test manifest not found. Run dataset test first.")
            return False
        
        # Load config
        with open('configs/phrases26.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Update config for quick test
        config['batch_size'] = 2
        config['num_workers'] = 0
        config['augmentation'] = {'enabled': False}
        
        # Create dataloaders
        train_loader, val_loader, test_loader, data_info = create_dataloaders(
            config, manifest_path=test_manifest_path
        )
        
        # Create model
        model = Mobile3DTiny(num_classes=26)
        criterion = torch.nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        print(f"✅ Training components initialized")
        
        # Test training step
        model.train()
        
        for i, (videos, labels, metadata) in enumerate(train_loader):
            print(f"🔄 Testing training step {i+1}...")
            
            # Forward pass
            outputs = model(videos)
            loss = criterion(outputs, labels)
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            print(f"   Loss: {loss.item():.4f}")
            print(f"   Output shape: {outputs.shape}")
            
            if i >= 1:  # Test 2 training steps
                break
        
        print(f"✅ Training pipeline test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Training pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main integration test function"""
    
    print("🧪 Mouth-Cropped Video Integration Test Suite")
    print("=" * 60)
    
    # Run all tests
    tests = [
        ("Video Processing", test_mouth_cropped_video_processing),
        ("Dataset Creation", test_mouth_cropped_dataset),
        ("Model Compatibility", test_model_compatibility),
        ("Training Pipeline", test_training_pipeline_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n📋 Integration Test Summary")
    print("=" * 40)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"🎉 All tests passed! Mouth-cropped videos are fully integrated.")
        print(f"✅ Ready for training with improved mouth-focused data!")
    else:
        print(f"⚠️  Some tests failed. Please review and fix issues before training.")
    
    return passed == total

if __name__ == '__main__':
    main()
