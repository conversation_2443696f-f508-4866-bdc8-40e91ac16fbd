#!/usr/bin/env python3
"""
Analyzer for test 2.webm and test 3.webm videos
Specifically targets the missing videos from previous analysis
"""

import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import sys
import json
import os
import glob

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class MissingVideoAnalyzer:
    """Analyzer specifically for test 2.webm and test 3.webm"""
    
    def __init__(self, model_path: str):
        """Initialize the analyzer"""
        
        self.model_path = Path(model_path)
        self.device = torch.device('cpu')
        
        # Perfect 10 phrases (in order)
        self.perfect_phrases = [
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today"
        ]
        
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        self.model = None
        self.video_processor = None
        
        print(f"🎯 Missing Video Analyzer for test 2.webm and test 3.webm")
    
    def find_test_videos(self) -> list:
        """Find test 2.webm and test 3.webm on desktop"""
        
        print(f"\n🔍 Searching for test videos...")
        
        # Possible desktop locations
        possible_paths = [
            "/Users/<USER>/Desktop/test 2.webm",
            "/Users/<USER>/Desktop/test 3.webm",
            "~/Desktop/test 2.webm", 
            "~/Desktop/test 3.webm",
            "./test 2.webm",
            "./test 3.webm"
        ]
        
        # Also search current directory and common locations
        search_patterns = [
            "test 2.webm",
            "test 3.webm", 
            "**/test 2.webm",
            "**/test 3.webm"
        ]
        
        found_videos = []
        
        # Check explicit paths
        for path in possible_paths:
            expanded_path = os.path.expanduser(path)
            if os.path.exists(expanded_path):
                found_videos.append(expanded_path)
                print(f"   ✅ Found: {expanded_path}")
        
        # Search with glob patterns
        for pattern in search_patterns:
            matches = glob.glob(pattern, recursive=True)
            for match in matches:
                abs_path = os.path.abspath(match)
                if abs_path not in found_videos:
                    found_videos.append(abs_path)
                    print(f"   ✅ Found: {abs_path}")
        
        if not found_videos:
            print(f"   ⚠️  No test 2.webm or test 3.webm found")
            
            # List what IS available
            print(f"\n📁 Available files in current directory:")
            for file in os.listdir('.'):
                if file.endswith('.webm'):
                    print(f"   📹 {file}")
        
        return found_videos
    
    def load_model(self) -> bool:
        """Load the Perfect 10 model"""
        
        print(f"\n🤖 Loading Perfect 10 Model")
        print("=" * 30)
        
        if not self.model_path.exists():
            print(f"❌ Model not found: {self.model_path}")
            return False
        
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device)
            self.model = Perfect10Mobile3DTiny(num_classes=10)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            best_val_accuracy = checkpoint.get('best_val_accuracy', 0.0)
            epoch = checkpoint.get('epoch', 0)
            
            print(f"✅ Model loaded successfully")
            print(f"   Training epoch: {epoch}")
            print(f"   Best validation accuracy: {best_val_accuracy:.1%}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            return False
    
    def initialize_video_processor(self):
        """Initialize video processor"""
        
        self.video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True
        )
        print(f"✅ Video processor initialized")
    
    def analyze_video(self, video_path: str) -> dict:
        """Analyze a single video"""
        
        video_name = Path(video_path).name
        print(f"\n🎬 Analyzing: {video_name}")
        
        try:
            # Process video
            video_tensor = self.video_processor.process_video(video_path)
            print(f"   ✅ Video processed: {video_tensor.shape}")
            
            # Get prediction
            video_batch = video_tensor.unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                outputs = self.model(video_batch)
                probabilities = F.softmax(outputs, dim=1)
                
                # Get top-3 predictions
                top3_probs, top3_indices = torch.topk(probabilities, 3, dim=1)
                
                top3_probs = top3_probs[0].cpu().numpy()
                top3_indices = top3_indices[0].cpu().numpy()
                top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
                
                # Determine confidence level
                top_confidence = float(top3_probs[0])
                if top_confidence >= 0.7:
                    confidence_level = "Very High"
                elif top_confidence >= 0.5:
                    confidence_level = "Moderate"
                elif top_confidence >= 0.3:
                    confidence_level = "Low"
                else:
                    confidence_level = "Very Low"
                
                result = {
                    'success': True,
                    'video_name': video_name,
                    'top_prediction': top3_phrases[0],
                    'top_confidence': top_confidence,
                    'confidence_level': confidence_level,
                    'top3_phrases': top3_phrases,
                    'top3_probabilities': top3_probs.tolist()
                }
                
                print(f"   🏆 Prediction: {top3_phrases[0].title()} ({top_confidence:.1%})")
                return result
                
        except Exception as e:
            print(f"   ❌ Analysis failed: {e}")
            return {
                'success': False,
                'video_name': video_name,
                'error': str(e)
            }
    
    def display_results(self, results: list):
        """Display results for the missing videos"""
        
        print(f"\n🎯 Missing Videos Analysis Results")
        print("=" * 40)
        
        successful_results = [r for r in results if r['success']]
        
        if not successful_results:
            print(f"❌ No videos were successfully analyzed")
            return
        
        # Summary table
        print(f"\n📊 **RESULTS TABLE**")
        print(f"{'Video':<15} {'Top Prediction':<25} {'Confidence':<12} {'Level':<12}")
        print(f"{'-'*15} {'-'*25} {'-'*12} {'-'*12}")
        
        for result in successful_results:
            print(f"{result['video_name']:<15} {result['top_prediction'].title():<25} {result['top_confidence']:.1%}{'':>7} {result['confidence_level']:<12}")
        
        # Detailed results
        print(f"\n📋 **DETAILED RESULTS**")
        for result in successful_results:
            print(f"\n🎬 **{result['video_name'].upper()}**")
            print(f"   🏆 Top: {result['top_prediction'].title()} ({result['top_confidence']:.1%}) - {result['confidence_level']}")
            print(f"   🥈 2nd: {result['top3_phrases'][1].title()} ({result['top3_probabilities'][1]:.1%})")
            print(f"   🥉 3rd: {result['top3_phrases'][2].title()} ({result['top3_probabilities'][2]:.1%})")

def main():
    """Main function to analyze missing videos"""
    
    print("🔍 Perfect 10 Missing Video Analyzer")
    print("=" * 40)
    print("Searching for test 2.webm and test 3.webm")
    
    # Configuration
    model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
    
    # Initialize analyzer
    analyzer = MissingVideoAnalyzer(model_path)
    
    # Find videos
    found_videos = analyzer.find_test_videos()
    
    if not found_videos:
        print(f"\n❌ Could not find test 2.webm or test 3.webm")
        print(f"📁 Please ensure these files are accessible")
        return
    
    # Load model
    if not analyzer.load_model():
        return
    
    # Initialize video processor
    analyzer.initialize_video_processor()
    
    # Analyze found videos
    results = []
    for video_path in found_videos:
        result = analyzer.analyze_video(video_path)
        results.append(result)
    
    # Display results
    analyzer.display_results(results)
    
    # Save results
    if results:
        output_file = "missing_videos_analysis.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Results saved: {output_file}")
    
    print(f"\n🎉 Missing Videos Analysis Complete!")

if __name__ == '__main__':
    main()
