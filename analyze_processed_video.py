#!/usr/bin/env python3
"""
Analyze processed video using Perfect 10 ICU lipreading classifier
Tests individual processed video to verify prediction results
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
from pathlib import Path
import sys
import json

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny

class ProcessedVideoAnalyzer:
    """Analyzer for processed video files using Perfect 10 model"""
    
    def __init__(self, model_path: str):
        """Initialize the analyzer"""
        
        self.model_path = Path(model_path)
        self.device = torch.device('cpu')  # Use CPU for consistency
        
        # Perfect 10 phrases (in order)
        self.perfect_phrases = [
            "am i getting better",
            "i feel anxious", 
            "i m confused",
            "i need to move",
            "i need to sit up",
            "i want to phone my family",
            "what happened to me",
            "what time is my wife coming",
            "where am i",
            "who is with me today"
        ]
        
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        self.model = None
        
        print(f"🎯 Processed Video Analyzer for Perfect 10 Classifier")
        print(f"   Model: {self.model_path}")
        print(f"   Device: {self.device}")
        print(f"   Perfect phrases: {len(self.perfect_phrases)}")
    
    def load_model(self) -> bool:
        """Load the trained Perfect 10 model"""
        
        print(f"\n🤖 Loading Perfect 10 Model")
        print("=" * 30)
        
        if not self.model_path.exists():
            print(f"❌ Model not found: {self.model_path}")
            return False
        
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device)
            self.model = Perfect10Mobile3DTiny(num_classes=10)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            best_val_accuracy = checkpoint.get('best_val_accuracy', 0.0)
            epoch = checkpoint.get('epoch', 0)
            
            print(f"✅ Perfect 10 model loaded successfully")
            print(f"   Parameters: {self.model.get_num_parameters():,}")
            print(f"   Training epoch: {epoch}")
            print(f"   Best validation accuracy: {best_val_accuracy:.1%}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def convert_mp4_to_tensor(self, video_path: str) -> torch.Tensor:
        """Convert processed MP4 back to tensor format for model input"""
        
        print(f"\n🔄 Converting MP4 to tensor: {Path(video_path).name}")
        
        cap = cv2.VideoCapture(video_path)
        
        # Get video properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"   📊 Video properties: {width}×{height}, {frame_count} frames")
        
        frames = []
        
        # Read all frames
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Convert to grayscale if needed (should already be grayscale)
            if len(frame.shape) == 3:
                frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                frame_gray = frame
            
            # Normalize to [0,1] range
            frame_normalized = frame_gray.astype(np.float32) / 255.0
            
            frames.append(frame_normalized)
        
        cap.release()
        
        print(f"   📊 Extracted {len(frames)} frames")
        
        # Ensure exactly 32 frames
        if len(frames) > 32:
            frames = frames[:32]
        elif len(frames) < 32:
            # Pad with last frame
            while len(frames) < 32:
                frames.append(frames[-1] if frames else np.zeros((height, width), dtype=np.float32))
        
        # Convert to tensor: [32, H, W] -> [1, 1, 32, H, W] (batch, channel, time, height, width)
        frames_array = np.stack(frames)
        tensor = torch.from_numpy(frames_array).unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions

        print(f"   ✅ Tensor created: {tensor.shape}")
        print(f"   📊 Value range: [{tensor.min():.3f}, {tensor.max():.3f}]")

        return tensor
    
    def analyze_processed_video(self, video_path: str) -> dict:
        """Analyze a processed video and return predictions"""
        
        video_name = Path(video_path).name
        print(f"\n🎬 Analyzing Processed Video: {video_name}")
        print("=" * 40)
        
        if not Path(video_path).exists():
            return {
                'success': False,
                'error': f'Video file not found: {video_path}'
            }
        
        try:
            # Convert MP4 to tensor
            video_tensor = self.convert_mp4_to_tensor(video_path)
            
            # Move to device
            video_batch = video_tensor.to(self.device)
            
            # Get prediction
            print(f"\n🔮 Generating Perfect 10 predictions...")
            with torch.no_grad():
                outputs = self.model(video_batch)
                probabilities = F.softmax(outputs, dim=1)
                
                # Get top-3 predictions
                top3_probs, top3_indices = torch.topk(probabilities, 3, dim=1)
                
                top3_probs = top3_probs[0].cpu().numpy()
                top3_indices = top3_indices[0].cpu().numpy()
                
                # Convert to phrases
                top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
                
                # Determine confidence level
                top_confidence = float(top3_probs[0])
                if top_confidence >= 0.7:
                    confidence_level = "Very High"
                    confidence_emoji = "🟢"
                elif top_confidence >= 0.5:
                    confidence_level = "Moderate"
                    confidence_emoji = "🟡"
                elif top_confidence >= 0.3:
                    confidence_level = "Low"
                    confidence_emoji = "🟠"
                else:
                    confidence_level = "Very Low"
                    confidence_emoji = "🔴"
                
                # Get all predictions for analysis
                all_probs = probabilities[0].cpu().numpy()
                
                return {
                    'success': True,
                    'video_path': video_path,
                    'video_name': video_name,
                    'tensor_shape': list(video_tensor.shape),
                    'top_prediction': top3_phrases[0],
                    'top_confidence': top_confidence,
                    'confidence_level': confidence_level,
                    'confidence_emoji': confidence_emoji,
                    'top3_phrases': top3_phrases,
                    'top3_probabilities': top3_probs.tolist(),
                    'all_probabilities': all_probs.tolist(),
                    'raw_outputs': outputs[0].cpu().numpy().tolist()
                }
                
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'video_path': video_path
            }
    
    def display_analysis_results(self, results: dict):
        """Display comprehensive analysis results"""
        
        if not results['success']:
            print(f"❌ Analysis failed: {results['error']}")
            return
        
        print(f"\n🎯 PERFECT 10 PREDICTION RESULTS")
        print("=" * 40)
        print(f"📹 Video: {results['video_name']}")
        print(f"🔧 Tensor: {results['tensor_shape']}")
        
        # Top prediction
        top_phrase = results['top_prediction']
        top_confidence = results['top_confidence']
        confidence_emoji = results['confidence_emoji']
        confidence_level = results['confidence_level']
        
        print(f"\n🏆 **TOP PREDICTION**")
        print(f"   Phrase: \"{top_phrase.title()}\"")
        print(f"   Confidence: {top_confidence:.1%}")
        print(f"   Level: {confidence_emoji} {confidence_level}")
        
        # Top-3 predictions
        print(f"\n📊 **TOP-3 PREDICTIONS**")
        for i, (phrase, prob) in enumerate(zip(results['top3_phrases'], results['top3_probabilities'])):
            rank_emoji = ["🥇", "🥈", "🥉"][i]
            print(f"   {rank_emoji} {phrase.title()}: {prob:.1%}")
        
        # All significant predictions (>5%)
        print(f"\n📋 **ALL SIGNIFICANT PREDICTIONS (>5%)**")
        all_probs = results['all_probabilities']
        for i, (phrase, prob) in enumerate(zip(self.perfect_phrases, all_probs)):
            if prob >= 0.05:  # Only show predictions above 5%
                print(f"   {phrase.title()}: {prob:.1%}")
        
        # Comparison with batch results
        print(f"\n🔍 **COMPARISON WITH BATCH ANALYSIS**")
        print(f"   Previous batch result: All 5 videos predicted 'I Feel Anxious'")
        print(f"   Current individual result: '{top_phrase.title()}'")
        
        if top_phrase == "i feel anxious":
            print(f"   ✅ CONSISTENT: Individual analysis matches batch result")
        else:
            print(f"   ⚠️  DIFFERENT: Individual analysis differs from batch result")
        
        # Technical details
        print(f"\n🔧 **TECHNICAL DETAILS**")
        print(f"   Processing: Corrected ICU mouth-cropping (133, 0, 133, 100)")
        print(f"   Pipeline: MP4 → Tensor → Perfect 10 Model → Prediction")
        print(f"   Model: 100% validation accuracy on Perfect 10 phrases")
        print(f"   Input format: [1, 32, 96, 96] grayscale tensor")
    
    def save_analysis_results(self, results: dict, output_path: str):
        """Save analysis results to file"""
        
        if results['success']:
            # Add metadata
            results['analysis_info'] = {
                'model_type': 'Perfect10Mobile3DTiny',
                'model_path': str(self.model_path),
                'perfect_phrases': self.perfect_phrases,
                'preprocessing': 'Corrected ICU mouth-cropping (133, 0, 133, 100)',
                'analysis_type': 'Individual processed video analysis'
            }
            
            # Save to JSON
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"💾 Analysis results saved: {output_path}")

def main():
    """Main analysis function"""
    
    print("🎬 Perfect 10 Processed Video Analysis")
    print("=" * 45)
    print("Analyzing processed_4.mp4 with corrected preprocessing")
    
    # Configuration
    model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
    video_path = "/Users/<USER>/Desktop/processed_perfect_10_videos/processed_4.mp4"
    
    # Initialize analyzer
    analyzer = ProcessedVideoAnalyzer(model_path)
    
    # Load model
    if not analyzer.load_model():
        print("❌ Failed to load Perfect 10 model")
        return
    
    # Check if video exists
    if not Path(video_path).exists():
        print(f"❌ Processed video not found: {video_path}")
        print(f"📁 Please ensure processed_4.mp4 exists in the processed videos folder")
        return
    
    # Analyze the processed video
    results = analyzer.analyze_processed_video(video_path)
    
    # Display results
    analyzer.display_analysis_results(results)
    
    # Save results
    if results['success']:
        output_path = "processed_4_analysis_results.json"
        analyzer.save_analysis_results(results, output_path)
        
        print(f"\n🎉 Individual Video Analysis Complete!")
        print("=" * 40)
        print(f"✅ processed_4.mp4 analyzed successfully")
        print(f"✅ Prediction results displayed above")
        print(f"✅ Detailed results saved to: {output_path}")
        print(f"✅ Comparison with batch results provided")
    else:
        print(f"\n❌ Analysis failed for processed_4.mp4")

if __name__ == '__main__':
    main()
