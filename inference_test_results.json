{"results": [{"video_path": "/Users/<USER>/Desktop/reference videos for training/where_am_i__useruser01__18to39__male__not_specified__20250809T053507_mouth_cropped.webm", "ground_truth": "where am i", "predicted": "where am i", "confidence": "0.8699376", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["where am i", "am i getting better", "what time is my wife coming"], "top3_probabilities": [0.8699375987052917, 0.03276543691754341, 0.028340719640254974]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_confused__useruser01__18to39__male__not_specified__20250809T054740_mouth_cropped.webm", "ground_truth": "i m confused", "predicted": "i m confused", "confidence": "0.60698164", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i m confused", "stay with me please", "what time is my wife coming"], "top3_probabilities": [0.606981635093689, 0.14485524594783783, 0.1135917603969574]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/please_explain_again__useruser01__18to39__male__not_specified__20250809T053853_mouth_cropped.webm", "ground_truth": "please explain again", "predicted": "i have a headache", "confidence": "0.28007713", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i have a headache", "i need to sit up", "i feel anxious"], "top3_probabilities": [0.28007712960243225, 0.24418845772743225, 0.12389165163040161]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_wife_coming__useruser01__18to39__male__not_specified__20250809T054337_mouth_cropped.webm", "ground_truth": "what time is my wife coming", "predicted": "what time is my wife coming", "confidence": "0.7670975", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what time is my wife coming", "i need to move", "i m confused"], "top3_probabilities": [0.7670974731445312, 0.07501619309186935, 0.03561275452375412]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_back_hurts__useruser01__18to39__male__not_specified__20250809T054702_mouth_cropped.webm", "ground_truth": "my back hurts", "predicted": "what time is my wife coming", "confidence": "0.31117004", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["what time is my wife coming", "my back hurts", "i need to move"], "top3_probabilities": [0.3111700415611267, 0.2722581624984741, 0.2080380767583847]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/who_is_with_me_today__useruser01__18to39__male__not_specified__20250809T053532_mouth_cropped.webm", "ground_truth": "who is with me today", "predicted": "who is with me today", "confidence": "0.80960447", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["who is with me today", "what happened to me", "i want to phone my family"], "top3_probabilities": [0.8096044659614563, 0.09132754802703857, 0.08660345524549484]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_help__useruser01__18to39__male__not_specified__20250809T055321_mouth_cropped.webm", "ground_truth": "i need help", "predicted": "i need to sit up", "confidence": "0.7217929", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i need help"], "top3_probabilities": [0.7217928767204285, 0.1408734768629074, 0.03629642724990845]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_husband__useruser01__18to39__male__not_specified__20250809T054008_mouth_cropped.webm", "ground_truth": "where is my husband", "predicted": "i have a headache", "confidence": "0.41777995", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i have a headache", "am i getting better", "i need to move"], "top3_probabilities": [0.41777995228767395, 0.13432979583740234, 0.12905940413475037]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_confused__useruser01__18to39__male__not_specified__20250809T054713_mouth_cropped.webm", "ground_truth": "i m confused", "predicted": "i m confused", "confidence": "0.4430343", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i m confused", "i need to move", "what time is my wife coming"], "top3_probabilities": [0.443034291267395, 0.16743344068527222, 0.1666613519191742]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_wife_coming__useruser01__18to39__male__not_specified__20250809T054309_mouth_cropped.webm", "ground_truth": "what time is my wife coming", "predicted": "what time is my wife coming", "confidence": "0.7737409", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what time is my wife coming", "i m confused", "i want to phone my family"], "top3_probabilities": [0.7737408876419067, 0.05015408992767334, 0.043047837913036346]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_uncomfortable__useruser01__18to39__male__not_specified__20250809T054932_mouth_cropped.webm", "ground_truth": "i m uncomfortable", "predicted": "i want to phone my family", "confidence": "0.30704755", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i want to phone my family", "what happened to me", "am i getting better"], "top3_probabilities": [0.3070475459098816, 0.25062695145606995, 0.09406311810016632]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_back_hurts__useruser01__18to39__male__not_specified__20250809T054639_mouth_cropped.webm", "ground_truth": "my back hurts", "predicted": "my back hurts", "confidence": "0.48905152", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["my back hurts", "i need to move", "what time is my wife coming"], "top3_probabilities": [0.4890515208244324, 0.2480417788028717, 0.06923109292984009]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/stay_with_me_please__useruser01__18to39__male__not_specified__20250809T054546_mouth_cropped.webm", "ground_truth": "stay with me please", "predicted": "stay with me please", "confidence": "0.50761575", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["stay with me please", "i want to phone my family", "i need to move"], "top3_probabilities": [0.5076157450675964, 0.1398390233516693, 0.1328902542591095]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_feel_anxious__useruser01__18to39__male__not_specified__20250809T054432_mouth_cropped.webm", "ground_truth": "i feel anxious", "predicted": "i feel anxious", "confidence": "0.8238719", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i feel anxious", "i need to move", "my back hurts"], "top3_probabilities": [0.823871910572052, 0.09611630439758301, 0.03238508105278015]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T054212_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i want to see my husband", "confidence": "0.4899027", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to see my husband", "i need to move", "i feel anxious"], "top3_probabilities": [0.48990270495414734, 0.1483583152294159, 0.09887347370386124]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_in_pain__useruser01__18to39__male__not_specified__20250809T054816_mouth_cropped.webm", "ground_truth": "i m in pain", "predicted": "i need to move", "confidence": "0.30889627", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "what time is my wife coming", "i m in pain"], "top3_probabilities": [0.3088962733745575, 0.191082164645195, 0.11233776062726974]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_am_i__useruser01__18to39__male__not_specified__20250809T053519_mouth_cropped.webm", "ground_truth": "where am i", "predicted": "where am i", "confidence": "0.91482145", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["where am i", "am i getting better", "what time is my wife coming"], "top3_probabilities": [0.914821445941925, 0.018310775980353355, 0.014355245046317577]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T055429_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i need to sit up", "confidence": "0.6919932", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to sit up", "i need to lie down", "i need help"], "top3_probabilities": [0.6919931769371033, 0.20314976572990417, 0.028611553832888603]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_husband_coming__useruser01__18to39__male__not_specified__20250809T054420_mouth_cropped.webm", "ground_truth": "what time is my husband coming", "predicted": "i feel anxious", "confidence": "0.65754694", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i feel anxious", "i need to move", "i m in pain"], "top3_probabilities": [0.6575469374656677, 0.27867817878723145, 0.015965892001986504]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/who_is_with_me_today__useruser01__18to39__male__not_specified__20250809T053648_mouth_cropped.webm", "ground_truth": "who is with me today", "predicted": "who is with me today", "confidence": "0.58688813", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["who is with me today", "what happened to me", "i want to phone my family"], "top3_probabilities": [0.5868881344795227, 0.21369268000125885, 0.16981449723243713]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_wife__useruser01__18to39__male__not_specified__20250809T053917_mouth_cropped.webm", "ground_truth": "where is my wife", "predicted": "where is my wife", "confidence": "0.38657743", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["where is my wife", "what happened to me", "am i getting better"], "top3_probabilities": [0.38657742738723755, 0.14027418196201324, 0.13867729902267456]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_feel_anxious__useruser01__18to39__male__not_specified__20250809T054501_mouth_cropped.webm", "ground_truth": "i feel anxious", "predicted": "i feel anxious", "confidence": "0.93429285", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i feel anxious", "i need to move", "i have a headache"], "top3_probabilities": [0.9342928528785706, 0.05650033429265022, 0.002418883377686143]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_husband__useruser01__18to39__male__not_specified__20250809T053953_mouth_cropped.webm", "ground_truth": "where is my husband", "predicted": "am i getting better", "confidence": "0.21027191", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["am i getting better", "i need to move", "i want to see my husband"], "top3_probabilities": [0.2102719098329544, 0.14232030510902405, 0.12649740278720856]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_happened_to_me__useruser01__18to39__male__not_specified__20250809T053739_mouth_cropped.webm", "ground_truth": "what happened to me", "predicted": "what happened to me", "confidence": "0.56735206", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what happened to me", "i want to phone my family", "am i getting better"], "top3_probabilities": [0.5673520565032959, 0.14688988029956818, 0.14596542716026306]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_am_i__useruser01__18to39__male__not_specified__20250809T053449_mouth_cropped.webm", "ground_truth": "where am i", "predicted": "where am i", "confidence": "0.38695368", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["where am i", "what time is my wife coming", "i m confused"], "top3_probabilities": [0.3869536817073822, 0.2916584014892578, 0.07205218076705933]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_uncomfortable__useruser01__18to39__male__not_specified__20250809T054919_mouth_cropped.webm", "ground_truth": "i m uncomfortable", "predicted": "i need to move", "confidence": "0.67627", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "i want to see my husband", "i feel anxious"], "top3_probabilities": [0.6762700080871582, 0.10620217025279999, 0.02824079431593418]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_a_medication__useruser01__18to39__male__not_specified__20250809T054944_mouth_cropped.webm", "ground_truth": "i need a medication", "predicted": "i need a medication", "confidence": "0.28495455", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need a medication", "i need to move", "i have a headache"], "top3_probabilities": [0.2849545478820801, 0.15935243666172028, 0.12136480957269669]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_help__useruser01__18to39__male__not_specified__20250809T055309_mouth_cropped.webm", "ground_truth": "i need help", "predicted": "i need to sit up", "confidence": "0.67455524", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.674555242061615, 0.1674496829509735, 0.06207618489861488]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_chest_hurts__useruser01__18to39__male__not_specified__20250809T054627_mouth_cropped.webm", "ground_truth": "my chest hurts", "predicted": "i need to move", "confidence": "0.38538766", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "my back hurts", "i feel anxious"], "top3_probabilities": [0.385387659072876, 0.23100358247756958, 0.17617900669574738]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/am_i_getting_better__useruser01__18to39__male__not_specified__20250809T053750_mouth_cropped.webm", "ground_truth": "am i getting better", "predicted": "am i getting better", "confidence": "0.6828697", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["am i getting better", "what happened to me", "i want to phone my family"], "top3_probabilities": [0.6828696727752686, 0.15872915089130402, 0.08726376295089722]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_lie_down__useruser01__18to39__male__not_specified__20250809T055137_mouth_cropped.webm", "ground_truth": "i need to lie down", "predicted": "i need to sit up", "confidence": "0.7548903", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.7548903226852417, 0.17355060577392578, 0.05517638474702835]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_use_the_toilet__useruser01__18to39__male__not_specified__20250809T055215_mouth_cropped.webm", "ground_truth": "i need to use the toilet", "predicted": "what happened to me", "confidence": "0.51312757", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["what happened to me", "i want to phone my family", "who is with me today"], "top3_probabilities": [0.5131275653839111, 0.25397393107414246, 0.09317394345998764]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_happened_to_me__useruser01__18to39__male__not_specified__20250809T053714_mouth_cropped.webm", "ground_truth": "what happened to me", "predicted": "what happened to me", "confidence": "0.6759133", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what happened to me", "i want to phone my family", "am i getting better"], "top3_probabilities": [0.6759132742881775, 0.13056081533432007, 0.09678913652896881]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_back_hurts__useruser01__18to39__male__not_specified__20250809T054651_mouth_cropped.webm", "ground_truth": "my back hurts", "predicted": "my back hurts", "confidence": "0.30678132", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["my back hurts", "i feel anxious", "i need to move"], "top3_probabilities": [0.3067813217639923, 0.25058385729789734, 0.20969341695308685]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T054229_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i want to see my husband", "confidence": "0.43945524", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to see my husband", "am i getting better", "i have a headache"], "top3_probabilities": [0.4394552409648895, 0.22379176318645477, 0.06170312687754631]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_wife__useruser01__18to39__male__not_specified__20250809T053929_mouth_cropped.webm", "ground_truth": "where is my wife", "predicted": "i need to move", "confidence": "0.4776573", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "where is my wife", "am i getting better"], "top3_probabilities": [0.477657288312912, 0.14560046792030334, 0.05808715894818306]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_chest_hurts__useruser01__18to39__male__not_specified__20250809T054614_mouth_cropped.webm", "ground_truth": "my chest hurts", "predicted": "i need to move", "confidence": "0.5875398", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "i feel anxious", "my back hurts"], "top3_probabilities": [0.587539792060852, 0.2016194611787796, 0.11584624648094177]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/stay_with_me_please__useruser01__18to39__male__not_specified__20250809T054530_mouth_cropped.webm", "ground_truth": "stay with me please", "predicted": "stay with me please", "confidence": "0.52849764", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["stay with me please", "i need to move", "i want to phone my family"], "top3_probabilities": [0.5284976363182068, 0.15627562999725342, 0.0942031666636467]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_phone_my_family__useruser01__18to39__male__not_specified__20250809T054040_mouth_cropped.webm", "ground_truth": "i want to phone my family", "predicted": "i want to phone my family", "confidence": "0.7024428", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to phone my family", "what happened to me", "who is with me today"], "top3_probabilities": [0.7024428248405457, 0.11573490500450134, 0.07630077749490738]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_move__useruser01__18to39__male__not_specified__20250809T055035_mouth_cropped.webm", "ground_truth": "i need to move", "predicted": "i need to move", "confidence": "0.46901777", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to move", "i need to sit up", "i have a headache"], "top3_probabilities": [0.46901777386665344, 0.3805984854698181, 0.0536850169301033]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_feel_anxious__useruser01__18to39__male__not_specified__20250809T054444_mouth_cropped.webm", "ground_truth": "i feel anxious", "predicted": "i feel anxious", "confidence": "0.92998594", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i feel anxious", "i need to move", "my back hurts"], "top3_probabilities": [0.9299859404563904, 0.04779499024152756, 0.006979442201554775]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_confused__useruser01__18to39__male__not_specified__20250809T054728_mouth_cropped.webm", "ground_truth": "i m confused", "predicted": "i m confused", "confidence": "0.4848037", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i m confused", "i need to move", "what time is my wife coming"], "top3_probabilities": [0.484803706407547, 0.19596557319164276, 0.164536252617836]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_uncomfortable__useruser01__18to39__male__not_specified__20250809T054907_mouth_cropped.webm", "ground_truth": "i m uncomfortable", "predicted": "i need to move", "confidence": "0.4106929", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "i want to see my husband", "am i getting better"], "top3_probabilities": [0.41069290041923523, 0.14315955340862274, 0.11925183981657028]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T055442_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i need to sit up", "confidence": "0.64961845", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to sit up", "i need to lie down", "i need help"], "top3_probabilities": [0.6496184468269348, 0.21131779253482819, 0.03766892850399017]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_happened_to_me__useruser01__18to39__male__not_specified__20250809T053727_mouth_cropped.webm", "ground_truth": "what happened to me", "predicted": "what happened to me", "confidence": "0.836742", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what happened to me", "who is with me today", "i want to phone my family"], "top3_probabilities": [0.8367419838905334, 0.10704272240400314, 0.048772457987070084]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_a_medication__useruser01__18to39__male__not_specified__20250809T054957_mouth_cropped.webm", "ground_truth": "i need a medication", "predicted": "i need to move", "confidence": "0.4915948", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i need to sit up", "i need a medication"], "top3_probabilities": [0.4915947914123535, 0.13998070359230042, 0.13349223136901855]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/please_explain_again__useruser01__18to39__male__not_specified__20250809T053826_mouth_cropped.webm", "ground_truth": "please explain again", "predicted": "am i getting better", "confidence": "0.22280514", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["am i getting better", "please explain again", "i have a headache"], "top3_probabilities": [0.22280514240264893, 0.187675341963768, 0.1779322773218155]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_move__useruser01__18to39__male__not_specified__20250809T055024_mouth_cropped.webm", "ground_truth": "i need to move", "predicted": "i need to move", "confidence": "0.983806", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to move", "stay with me please", "what time is my wife coming"], "top3_probabilities": [0.9838060140609741, 0.004076619166880846, 0.002521972870454192]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_move__useruser01__18to39__male__not_specified__20250809T055047_mouth_cropped.webm", "ground_truth": "i need to move", "predicted": "i need to move", "confidence": "0.9943399", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to move", "i feel anxious", "i want to see my wife"], "top3_probabilities": [0.9943398833274841, 0.002818046836182475, 0.0007689057383686304]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_have_a_headache__useruser01__18to39__male__not_specified__20250809T054855_mouth_cropped.webm", "ground_truth": "i have a headache", "predicted": "i have a headache", "confidence": "0.66873175", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i have a headache", "i feel anxious", "i need to sit up"], "top3_probabilities": [0.6687317490577698, 0.28875604271888733, 0.02741747349500656]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_sit_up__useruser01__18to39__male__not_specified__20250809T055124_mouth_cropped.webm", "ground_truth": "i need to sit up", "predicted": "i need to sit up", "confidence": "0.9566141", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.956614077091217, 0.03459342569112778, 0.003252702997997403]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_wife__useruser01__18to39__male__not_specified__20250809T053905_mouth_cropped.webm", "ground_truth": "where is my wife", "predicted": "i want to phone my family", "confidence": "0.22987853", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i want to phone my family", "where is my wife", "what happened to me"], "top3_probabilities": [0.2298785299062729, 0.22478625178337097, 0.20482945442199707]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_husband__useruser01__18to39__male__not_specified__20250809T053941_mouth_cropped.webm", "ground_truth": "where is my husband", "predicted": "i have a headache", "confidence": "0.36747056", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i have a headache", "i need to move", "i want to see my husband"], "top3_probabilities": [0.36747056245803833, 0.21151575446128845, 0.14184395968914032]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/who_is_with_me_today__useruser01__18to39__male__not_specified__20250809T053701_mouth_cropped.webm", "ground_truth": "who is with me today", "predicted": "who is with me today", "confidence": "0.6365033", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["who is with me today", "what happened to me", "i want to phone my family"], "top3_probabilities": [0.636503279209137, 0.21757876873016357, 0.13385796546936035]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_in_pain__useruser01__18to39__male__not_specified__20250809T054752_mouth_cropped.webm", "ground_truth": "i m in pain", "predicted": "i need to move", "confidence": "0.38429984", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "what time is my wife coming", "i m in pain"], "top3_probabilities": [0.3842998445034027, 0.1436978280544281, 0.10755223780870438]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_have_a_headache__useruser01__18to39__male__not_specified__20250809T054828_mouth_cropped.webm", "ground_truth": "i have a headache", "predicted": "i have a headache", "confidence": "0.55690706", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i have a headache", "i feel anxious", "i need to sit up"], "top3_probabilities": [0.556907057762146, 0.24895316362380981, 0.05535614490509033]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/am_i_getting_better__useruser01__18to39__male__not_specified__20250809T053802_mouth_cropped.webm", "ground_truth": "am i getting better", "predicted": "am i getting better", "confidence": "0.8143023", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["am i getting better", "what happened to me", "where is my wife"], "top3_probabilities": [0.8143023252487183, 0.05639267712831497, 0.037933725863695145]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_use_the_toilet__useruser01__18to39__male__not_specified__20250809T055229_mouth_cropped.webm", "ground_truth": "i need to use the toilet", "predicted": "what happened to me", "confidence": "0.54679394", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["what happened to me", "who is with me today", "i want to phone my family"], "top3_probabilities": [0.5467939376831055, 0.17189766466617584, 0.16951927542686462]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_wife__useruser01__18to39__male__not_specified__20250809T054142_mouth_cropped.webm", "ground_truth": "i want to see my wife", "predicted": "i need to move", "confidence": "0.3064324", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "what time is my wife coming", "i want to phone my family"], "top3_probabilities": [0.3064323961734772, 0.22759005427360535, 0.11906012892723083]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_wife_coming__useruser01__18to39__male__not_specified__20250809T054323_mouth_cropped.webm", "ground_truth": "what time is my wife coming", "predicted": "what time is my wife coming", "confidence": "0.71533614", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what time is my wife coming", "i want to phone my family", "i m confused"], "top3_probabilities": [0.7153361439704895, 0.12746156752109528, 0.048214372247457504]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_husband_coming__useruser01__18to39__male__not_specified__20250809T054407_mouth_cropped.webm", "ground_truth": "what time is my husband coming", "predicted": "i feel anxious", "confidence": "0.59204906", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i feel anxious", "i need to move", "my back hurts"], "top3_probabilities": [0.5920490622520447, 0.24781811237335205, 0.06539825350046158]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_a_medication__useruser01__18to39__male__not_specified__20250809T055010_mouth_cropped.webm", "ground_truth": "i need a medication", "predicted": "i need to move", "confidence": "0.18623589", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i need a medication", "i have a headache"], "top3_probabilities": [0.18623588979244232, 0.1766945868730545, 0.16850516200065613]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_phone_my_family__useruser01__18to39__male__not_specified__20250809T054052_mouth_cropped.webm", "ground_truth": "i want to phone my family", "predicted": "i want to phone my family", "confidence": "0.6205641", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to phone my family", "who is with me today", "what happened to me"], "top3_probabilities": [0.6205641031265259, 0.28831759095191956, 0.07778474688529968]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_phone_my_family__useruser01__18to39__male__not_specified__20250809T054104_mouth_cropped.webm", "ground_truth": "i want to phone my family", "predicted": "i want to phone my family", "confidence": "0.67149526", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to phone my family", "what time is my wife coming", "who is with me today"], "top3_probabilities": [0.671495258808136, 0.09383036196231842, 0.07405605167150497]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_wife__useruser01__18to39__male__not_specified__20250809T054117_mouth_cropped.webm", "ground_truth": "i want to see my wife", "predicted": "i need to move", "confidence": "0.7378653", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i want to see my wife", "i feel anxious"], "top3_probabilities": [0.7378653287887573, 0.09592337161302567, 0.03228429704904556]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_chest_hurts__useruser01__18to39__male__not_specified__20250809T054600_mouth_cropped.webm", "ground_truth": "my chest hurts", "predicted": "my back hurts", "confidence": "0.31651604", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["my back hurts", "i m confused", "i need to move"], "top3_probabilities": [0.31651604175567627, 0.17982180416584015, 0.16804467141628265]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_have_a_headache__useruser01__18to39__male__not_specified__20250809T054840_mouth_cropped.webm", "ground_truth": "i have a headache", "predicted": "i need to sit up", "confidence": "0.5827606", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i feel anxious", "i have a headache"], "top3_probabilities": [0.5827605724334717, 0.2795541286468506, 0.10421536862850189]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_help__useruser01__18to39__male__not_specified__20250809T055258_mouth_cropped.webm", "ground_truth": "i need help", "predicted": "i need to sit up", "confidence": "0.3267644", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to sit up", "i have a headache", "i need to lie down"], "top3_probabilities": [0.32676440477371216, 0.3172002136707306, 0.16146019101142883]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/am_i_getting_better__useruser01__18to39__male__not_specified__20250809T053814_mouth_cropped.webm", "ground_truth": "am i getting better", "predicted": "am i getting better", "confidence": "0.7369805", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["am i getting better", "i have a headache", "i need a medication"], "top3_probabilities": [0.7369804978370667, 0.09745246917009354, 0.02850150689482689]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/please_explain_again__useruser01__18to39__male__not_specified__20250809T053841_mouth_cropped.webm", "ground_truth": "please explain again", "predicted": "i have a headache", "confidence": "0.28110585", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i have a headache", "please explain again", "i need a medication"], "top3_probabilities": [0.2811058461666107, 0.19172167778015137, 0.10026735812425613]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_husband_coming__useruser01__18to39__male__not_specified__20250809T054355_mouth_cropped.webm", "ground_truth": "what time is my husband coming", "predicted": "i feel anxious", "confidence": "0.35305572", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i feel anxious", "i need to move", "i want to see my husband"], "top3_probabilities": [0.3530557155609131, 0.3399451673030853, 0.056786131113767624]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/stay_with_me_please__useruser01__18to39__male__not_specified__20250809T054517_mouth_cropped.webm", "ground_truth": "stay with me please", "predicted": "i need to move", "confidence": "0.45904854", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "stay with me please", "what time is my wife coming"], "top3_probabilities": [0.4590485394001007, 0.2908822000026703, 0.06994014978408813]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_lie_down__useruser01__18to39__male__not_specified__20250809T055202_mouth_cropped.webm", "ground_truth": "i need to lie down", "predicted": "i need to lie down", "confidence": "0.6845724", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to lie down", "i need to sit up", "i have a headache"], "top3_probabilities": [0.6845723986625671, 0.1715145707130432, 0.07600844651460648]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_wife__useruser01__18to39__male__not_specified__20250809T054129_mouth_cropped.webm", "ground_truth": "i want to see my wife", "predicted": "i need to move", "confidence": "0.7519342", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i want to see my wife", "what time is my wife coming"], "top3_probabilities": [0.7519341707229614, 0.1204538568854332, 0.03228698670864105]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_lie_down__useruser01__18to39__male__not_specified__20250809T055150_mouth_cropped.webm", "ground_truth": "i need to lie down", "predicted": "i need to sit up", "confidence": "0.51834035", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.5183403491973877, 0.33544373512268066, 0.11814546585083008]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_use_the_toilet__useruser01__18to39__male__not_specified__20250809T055242_mouth_cropped.webm", "ground_truth": "i need to use the toilet", "predicted": "what happened to me", "confidence": "0.7049178", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["what happened to me", "i want to phone my family", "who is with me today"], "top3_probabilities": [0.7049177885055542, 0.1199895441532135, 0.10379872471094131]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_in_pain__useruser01__18to39__male__not_specified__20250809T054804_mouth_cropped.webm", "ground_truth": "i m in pain", "predicted": "i need to move", "confidence": "0.6850302", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i feel anxious", "i m in pain"], "top3_probabilities": [0.6850302219390869, 0.17091724276542664, 0.07450912147760391]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_sit_up__useruser01__18to39__male__not_specified__20250809T055059_mouth_cropped.webm", "ground_truth": "i need to sit up", "predicted": "i need to sit up", "confidence": "0.9731818", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.9731817841529846, 0.009566663764417171, 0.006457418203353882]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T055416_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i need to sit up", "confidence": "0.2607337", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i want to see my husband", "i need to lie down"], "top3_probabilities": [0.2607336938381195, 0.2598628103733063, 0.2021656036376953]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_sit_up__useruser01__18to39__male__not_specified__20250809T055112_mouth_cropped.webm", "ground_truth": "i need to sit up", "predicted": "i need to sit up", "confidence": "0.9890155", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.989015519618988, 0.005468032788485289, 0.0023164714220911264]}], "total_videos": 80, "correct_predictions": 41, "top1_accuracy": 0.5125, "top3_accuracy": 0.7375, "detailed_results": [{"video_path": "/Users/<USER>/Desktop/reference videos for training/where_am_i__useruser01__18to39__male__not_specified__20250809T053507_mouth_cropped.webm", "ground_truth": "where am i", "predicted": "where am i", "confidence": "0.8699376", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["where am i", "am i getting better", "what time is my wife coming"], "top3_probabilities": [0.8699375987052917, 0.03276543691754341, 0.028340719640254974]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_confused__useruser01__18to39__male__not_specified__20250809T054740_mouth_cropped.webm", "ground_truth": "i m confused", "predicted": "i m confused", "confidence": "0.60698164", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i m confused", "stay with me please", "what time is my wife coming"], "top3_probabilities": [0.606981635093689, 0.14485524594783783, 0.1135917603969574]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/please_explain_again__useruser01__18to39__male__not_specified__20250809T053853_mouth_cropped.webm", "ground_truth": "please explain again", "predicted": "i have a headache", "confidence": "0.28007713", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i have a headache", "i need to sit up", "i feel anxious"], "top3_probabilities": [0.28007712960243225, 0.24418845772743225, 0.12389165163040161]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_wife_coming__useruser01__18to39__male__not_specified__20250809T054337_mouth_cropped.webm", "ground_truth": "what time is my wife coming", "predicted": "what time is my wife coming", "confidence": "0.7670975", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what time is my wife coming", "i need to move", "i m confused"], "top3_probabilities": [0.7670974731445312, 0.07501619309186935, 0.03561275452375412]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_back_hurts__useruser01__18to39__male__not_specified__20250809T054702_mouth_cropped.webm", "ground_truth": "my back hurts", "predicted": "what time is my wife coming", "confidence": "0.31117004", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["what time is my wife coming", "my back hurts", "i need to move"], "top3_probabilities": [0.3111700415611267, 0.2722581624984741, 0.2080380767583847]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/who_is_with_me_today__useruser01__18to39__male__not_specified__20250809T053532_mouth_cropped.webm", "ground_truth": "who is with me today", "predicted": "who is with me today", "confidence": "0.80960447", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["who is with me today", "what happened to me", "i want to phone my family"], "top3_probabilities": [0.8096044659614563, 0.09132754802703857, 0.08660345524549484]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_help__useruser01__18to39__male__not_specified__20250809T055321_mouth_cropped.webm", "ground_truth": "i need help", "predicted": "i need to sit up", "confidence": "0.7217929", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i need help"], "top3_probabilities": [0.7217928767204285, 0.1408734768629074, 0.03629642724990845]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_husband__useruser01__18to39__male__not_specified__20250809T054008_mouth_cropped.webm", "ground_truth": "where is my husband", "predicted": "i have a headache", "confidence": "0.41777995", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i have a headache", "am i getting better", "i need to move"], "top3_probabilities": [0.41777995228767395, 0.13432979583740234, 0.12905940413475037]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_confused__useruser01__18to39__male__not_specified__20250809T054713_mouth_cropped.webm", "ground_truth": "i m confused", "predicted": "i m confused", "confidence": "0.4430343", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i m confused", "i need to move", "what time is my wife coming"], "top3_probabilities": [0.443034291267395, 0.16743344068527222, 0.1666613519191742]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_wife_coming__useruser01__18to39__male__not_specified__20250809T054309_mouth_cropped.webm", "ground_truth": "what time is my wife coming", "predicted": "what time is my wife coming", "confidence": "0.7737409", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what time is my wife coming", "i m confused", "i want to phone my family"], "top3_probabilities": [0.7737408876419067, 0.05015408992767334, 0.043047837913036346]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_uncomfortable__useruser01__18to39__male__not_specified__20250809T054932_mouth_cropped.webm", "ground_truth": "i m uncomfortable", "predicted": "i want to phone my family", "confidence": "0.30704755", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i want to phone my family", "what happened to me", "am i getting better"], "top3_probabilities": [0.3070475459098816, 0.25062695145606995, 0.09406311810016632]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_back_hurts__useruser01__18to39__male__not_specified__20250809T054639_mouth_cropped.webm", "ground_truth": "my back hurts", "predicted": "my back hurts", "confidence": "0.48905152", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["my back hurts", "i need to move", "what time is my wife coming"], "top3_probabilities": [0.4890515208244324, 0.2480417788028717, 0.06923109292984009]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/stay_with_me_please__useruser01__18to39__male__not_specified__20250809T054546_mouth_cropped.webm", "ground_truth": "stay with me please", "predicted": "stay with me please", "confidence": "0.50761575", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["stay with me please", "i want to phone my family", "i need to move"], "top3_probabilities": [0.5076157450675964, 0.1398390233516693, 0.1328902542591095]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_feel_anxious__useruser01__18to39__male__not_specified__20250809T054432_mouth_cropped.webm", "ground_truth": "i feel anxious", "predicted": "i feel anxious", "confidence": "0.8238719", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i feel anxious", "i need to move", "my back hurts"], "top3_probabilities": [0.823871910572052, 0.09611630439758301, 0.03238508105278015]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T054212_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i want to see my husband", "confidence": "0.4899027", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to see my husband", "i need to move", "i feel anxious"], "top3_probabilities": [0.48990270495414734, 0.1483583152294159, 0.09887347370386124]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_in_pain__useruser01__18to39__male__not_specified__20250809T054816_mouth_cropped.webm", "ground_truth": "i m in pain", "predicted": "i need to move", "confidence": "0.30889627", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "what time is my wife coming", "i m in pain"], "top3_probabilities": [0.3088962733745575, 0.191082164645195, 0.11233776062726974]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_am_i__useruser01__18to39__male__not_specified__20250809T053519_mouth_cropped.webm", "ground_truth": "where am i", "predicted": "where am i", "confidence": "0.91482145", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["where am i", "am i getting better", "what time is my wife coming"], "top3_probabilities": [0.914821445941925, 0.018310775980353355, 0.014355245046317577]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T055429_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i need to sit up", "confidence": "0.6919932", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to sit up", "i need to lie down", "i need help"], "top3_probabilities": [0.6919931769371033, 0.20314976572990417, 0.028611553832888603]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_husband_coming__useruser01__18to39__male__not_specified__20250809T054420_mouth_cropped.webm", "ground_truth": "what time is my husband coming", "predicted": "i feel anxious", "confidence": "0.65754694", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i feel anxious", "i need to move", "i m in pain"], "top3_probabilities": [0.6575469374656677, 0.27867817878723145, 0.015965892001986504]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/who_is_with_me_today__useruser01__18to39__male__not_specified__20250809T053648_mouth_cropped.webm", "ground_truth": "who is with me today", "predicted": "who is with me today", "confidence": "0.58688813", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["who is with me today", "what happened to me", "i want to phone my family"], "top3_probabilities": [0.5868881344795227, 0.21369268000125885, 0.16981449723243713]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_wife__useruser01__18to39__male__not_specified__20250809T053917_mouth_cropped.webm", "ground_truth": "where is my wife", "predicted": "where is my wife", "confidence": "0.38657743", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["where is my wife", "what happened to me", "am i getting better"], "top3_probabilities": [0.38657742738723755, 0.14027418196201324, 0.13867729902267456]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_feel_anxious__useruser01__18to39__male__not_specified__20250809T054501_mouth_cropped.webm", "ground_truth": "i feel anxious", "predicted": "i feel anxious", "confidence": "0.93429285", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i feel anxious", "i need to move", "i have a headache"], "top3_probabilities": [0.9342928528785706, 0.05650033429265022, 0.002418883377686143]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_husband__useruser01__18to39__male__not_specified__20250809T053953_mouth_cropped.webm", "ground_truth": "where is my husband", "predicted": "am i getting better", "confidence": "0.21027191", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["am i getting better", "i need to move", "i want to see my husband"], "top3_probabilities": [0.2102719098329544, 0.14232030510902405, 0.12649740278720856]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_happened_to_me__useruser01__18to39__male__not_specified__20250809T053739_mouth_cropped.webm", "ground_truth": "what happened to me", "predicted": "what happened to me", "confidence": "0.56735206", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what happened to me", "i want to phone my family", "am i getting better"], "top3_probabilities": [0.5673520565032959, 0.14688988029956818, 0.14596542716026306]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_am_i__useruser01__18to39__male__not_specified__20250809T053449_mouth_cropped.webm", "ground_truth": "where am i", "predicted": "where am i", "confidence": "0.38695368", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["where am i", "what time is my wife coming", "i m confused"], "top3_probabilities": [0.3869536817073822, 0.2916584014892578, 0.07205218076705933]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_uncomfortable__useruser01__18to39__male__not_specified__20250809T054919_mouth_cropped.webm", "ground_truth": "i m uncomfortable", "predicted": "i need to move", "confidence": "0.67627", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "i want to see my husband", "i feel anxious"], "top3_probabilities": [0.6762700080871582, 0.10620217025279999, 0.02824079431593418]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_a_medication__useruser01__18to39__male__not_specified__20250809T054944_mouth_cropped.webm", "ground_truth": "i need a medication", "predicted": "i need a medication", "confidence": "0.28495455", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need a medication", "i need to move", "i have a headache"], "top3_probabilities": [0.2849545478820801, 0.15935243666172028, 0.12136480957269669]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_help__useruser01__18to39__male__not_specified__20250809T055309_mouth_cropped.webm", "ground_truth": "i need help", "predicted": "i need to sit up", "confidence": "0.67455524", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.674555242061615, 0.1674496829509735, 0.06207618489861488]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_chest_hurts__useruser01__18to39__male__not_specified__20250809T054627_mouth_cropped.webm", "ground_truth": "my chest hurts", "predicted": "i need to move", "confidence": "0.38538766", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "my back hurts", "i feel anxious"], "top3_probabilities": [0.385387659072876, 0.23100358247756958, 0.17617900669574738]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/am_i_getting_better__useruser01__18to39__male__not_specified__20250809T053750_mouth_cropped.webm", "ground_truth": "am i getting better", "predicted": "am i getting better", "confidence": "0.6828697", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["am i getting better", "what happened to me", "i want to phone my family"], "top3_probabilities": [0.6828696727752686, 0.15872915089130402, 0.08726376295089722]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_lie_down__useruser01__18to39__male__not_specified__20250809T055137_mouth_cropped.webm", "ground_truth": "i need to lie down", "predicted": "i need to sit up", "confidence": "0.7548903", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.7548903226852417, 0.17355060577392578, 0.05517638474702835]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_use_the_toilet__useruser01__18to39__male__not_specified__20250809T055215_mouth_cropped.webm", "ground_truth": "i need to use the toilet", "predicted": "what happened to me", "confidence": "0.51312757", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["what happened to me", "i want to phone my family", "who is with me today"], "top3_probabilities": [0.5131275653839111, 0.25397393107414246, 0.09317394345998764]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_happened_to_me__useruser01__18to39__male__not_specified__20250809T053714_mouth_cropped.webm", "ground_truth": "what happened to me", "predicted": "what happened to me", "confidence": "0.6759133", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what happened to me", "i want to phone my family", "am i getting better"], "top3_probabilities": [0.6759132742881775, 0.13056081533432007, 0.09678913652896881]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_back_hurts__useruser01__18to39__male__not_specified__20250809T054651_mouth_cropped.webm", "ground_truth": "my back hurts", "predicted": "my back hurts", "confidence": "0.30678132", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["my back hurts", "i feel anxious", "i need to move"], "top3_probabilities": [0.3067813217639923, 0.25058385729789734, 0.20969341695308685]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T054229_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i want to see my husband", "confidence": "0.43945524", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to see my husband", "am i getting better", "i have a headache"], "top3_probabilities": [0.4394552409648895, 0.22379176318645477, 0.06170312687754631]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_wife__useruser01__18to39__male__not_specified__20250809T053929_mouth_cropped.webm", "ground_truth": "where is my wife", "predicted": "i need to move", "confidence": "0.4776573", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "where is my wife", "am i getting better"], "top3_probabilities": [0.477657288312912, 0.14560046792030334, 0.05808715894818306]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_chest_hurts__useruser01__18to39__male__not_specified__20250809T054614_mouth_cropped.webm", "ground_truth": "my chest hurts", "predicted": "i need to move", "confidence": "0.5875398", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "i feel anxious", "my back hurts"], "top3_probabilities": [0.587539792060852, 0.2016194611787796, 0.11584624648094177]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/stay_with_me_please__useruser01__18to39__male__not_specified__20250809T054530_mouth_cropped.webm", "ground_truth": "stay with me please", "predicted": "stay with me please", "confidence": "0.52849764", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["stay with me please", "i need to move", "i want to phone my family"], "top3_probabilities": [0.5284976363182068, 0.15627562999725342, 0.0942031666636467]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_phone_my_family__useruser01__18to39__male__not_specified__20250809T054040_mouth_cropped.webm", "ground_truth": "i want to phone my family", "predicted": "i want to phone my family", "confidence": "0.7024428", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to phone my family", "what happened to me", "who is with me today"], "top3_probabilities": [0.7024428248405457, 0.11573490500450134, 0.07630077749490738]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_move__useruser01__18to39__male__not_specified__20250809T055035_mouth_cropped.webm", "ground_truth": "i need to move", "predicted": "i need to move", "confidence": "0.46901777", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to move", "i need to sit up", "i have a headache"], "top3_probabilities": [0.46901777386665344, 0.3805984854698181, 0.0536850169301033]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_feel_anxious__useruser01__18to39__male__not_specified__20250809T054444_mouth_cropped.webm", "ground_truth": "i feel anxious", "predicted": "i feel anxious", "confidence": "0.92998594", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i feel anxious", "i need to move", "my back hurts"], "top3_probabilities": [0.9299859404563904, 0.04779499024152756, 0.006979442201554775]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_confused__useruser01__18to39__male__not_specified__20250809T054728_mouth_cropped.webm", "ground_truth": "i m confused", "predicted": "i m confused", "confidence": "0.4848037", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i m confused", "i need to move", "what time is my wife coming"], "top3_probabilities": [0.484803706407547, 0.19596557319164276, 0.164536252617836]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_uncomfortable__useruser01__18to39__male__not_specified__20250809T054907_mouth_cropped.webm", "ground_truth": "i m uncomfortable", "predicted": "i need to move", "confidence": "0.4106929", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "i want to see my husband", "am i getting better"], "top3_probabilities": [0.41069290041923523, 0.14315955340862274, 0.11925183981657028]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T055442_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i need to sit up", "confidence": "0.64961845", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to sit up", "i need to lie down", "i need help"], "top3_probabilities": [0.6496184468269348, 0.21131779253482819, 0.03766892850399017]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_happened_to_me__useruser01__18to39__male__not_specified__20250809T053727_mouth_cropped.webm", "ground_truth": "what happened to me", "predicted": "what happened to me", "confidence": "0.836742", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what happened to me", "who is with me today", "i want to phone my family"], "top3_probabilities": [0.8367419838905334, 0.10704272240400314, 0.048772457987070084]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_a_medication__useruser01__18to39__male__not_specified__20250809T054957_mouth_cropped.webm", "ground_truth": "i need a medication", "predicted": "i need to move", "confidence": "0.4915948", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i need to sit up", "i need a medication"], "top3_probabilities": [0.4915947914123535, 0.13998070359230042, 0.13349223136901855]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/please_explain_again__useruser01__18to39__male__not_specified__20250809T053826_mouth_cropped.webm", "ground_truth": "please explain again", "predicted": "am i getting better", "confidence": "0.22280514", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["am i getting better", "please explain again", "i have a headache"], "top3_probabilities": [0.22280514240264893, 0.187675341963768, 0.1779322773218155]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_move__useruser01__18to39__male__not_specified__20250809T055024_mouth_cropped.webm", "ground_truth": "i need to move", "predicted": "i need to move", "confidence": "0.983806", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to move", "stay with me please", "what time is my wife coming"], "top3_probabilities": [0.9838060140609741, 0.004076619166880846, 0.002521972870454192]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_move__useruser01__18to39__male__not_specified__20250809T055047_mouth_cropped.webm", "ground_truth": "i need to move", "predicted": "i need to move", "confidence": "0.9943399", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to move", "i feel anxious", "i want to see my wife"], "top3_probabilities": [0.9943398833274841, 0.002818046836182475, 0.0007689057383686304]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_have_a_headache__useruser01__18to39__male__not_specified__20250809T054855_mouth_cropped.webm", "ground_truth": "i have a headache", "predicted": "i have a headache", "confidence": "0.66873175", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i have a headache", "i feel anxious", "i need to sit up"], "top3_probabilities": [0.6687317490577698, 0.28875604271888733, 0.02741747349500656]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_sit_up__useruser01__18to39__male__not_specified__20250809T055124_mouth_cropped.webm", "ground_truth": "i need to sit up", "predicted": "i need to sit up", "confidence": "0.9566141", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.956614077091217, 0.03459342569112778, 0.003252702997997403]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_wife__useruser01__18to39__male__not_specified__20250809T053905_mouth_cropped.webm", "ground_truth": "where is my wife", "predicted": "i want to phone my family", "confidence": "0.22987853", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i want to phone my family", "where is my wife", "what happened to me"], "top3_probabilities": [0.2298785299062729, 0.22478625178337097, 0.20482945442199707]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/where_is_my_husband__useruser01__18to39__male__not_specified__20250809T053941_mouth_cropped.webm", "ground_truth": "where is my husband", "predicted": "i have a headache", "confidence": "0.36747056", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i have a headache", "i need to move", "i want to see my husband"], "top3_probabilities": [0.36747056245803833, 0.21151575446128845, 0.14184395968914032]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/who_is_with_me_today__useruser01__18to39__male__not_specified__20250809T053701_mouth_cropped.webm", "ground_truth": "who is with me today", "predicted": "who is with me today", "confidence": "0.6365033", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["who is with me today", "what happened to me", "i want to phone my family"], "top3_probabilities": [0.636503279209137, 0.21757876873016357, 0.13385796546936035]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_in_pain__useruser01__18to39__male__not_specified__20250809T054752_mouth_cropped.webm", "ground_truth": "i m in pain", "predicted": "i need to move", "confidence": "0.38429984", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "what time is my wife coming", "i m in pain"], "top3_probabilities": [0.3842998445034027, 0.1436978280544281, 0.10755223780870438]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_have_a_headache__useruser01__18to39__male__not_specified__20250809T054828_mouth_cropped.webm", "ground_truth": "i have a headache", "predicted": "i have a headache", "confidence": "0.55690706", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i have a headache", "i feel anxious", "i need to sit up"], "top3_probabilities": [0.556907057762146, 0.24895316362380981, 0.05535614490509033]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/am_i_getting_better__useruser01__18to39__male__not_specified__20250809T053802_mouth_cropped.webm", "ground_truth": "am i getting better", "predicted": "am i getting better", "confidence": "0.8143023", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["am i getting better", "what happened to me", "where is my wife"], "top3_probabilities": [0.8143023252487183, 0.05639267712831497, 0.037933725863695145]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_use_the_toilet__useruser01__18to39__male__not_specified__20250809T055229_mouth_cropped.webm", "ground_truth": "i need to use the toilet", "predicted": "what happened to me", "confidence": "0.54679394", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["what happened to me", "who is with me today", "i want to phone my family"], "top3_probabilities": [0.5467939376831055, 0.17189766466617584, 0.16951927542686462]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_wife__useruser01__18to39__male__not_specified__20250809T054142_mouth_cropped.webm", "ground_truth": "i want to see my wife", "predicted": "i need to move", "confidence": "0.3064324", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to move", "what time is my wife coming", "i want to phone my family"], "top3_probabilities": [0.3064323961734772, 0.22759005427360535, 0.11906012892723083]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_wife_coming__useruser01__18to39__male__not_specified__20250809T054323_mouth_cropped.webm", "ground_truth": "what time is my wife coming", "predicted": "what time is my wife coming", "confidence": "0.71533614", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["what time is my wife coming", "i want to phone my family", "i m confused"], "top3_probabilities": [0.7153361439704895, 0.12746156752109528, 0.048214372247457504]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_husband_coming__useruser01__18to39__male__not_specified__20250809T054407_mouth_cropped.webm", "ground_truth": "what time is my husband coming", "predicted": "i feel anxious", "confidence": "0.59204906", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i feel anxious", "i need to move", "my back hurts"], "top3_probabilities": [0.5920490622520447, 0.24781811237335205, 0.06539825350046158]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_a_medication__useruser01__18to39__male__not_specified__20250809T055010_mouth_cropped.webm", "ground_truth": "i need a medication", "predicted": "i need to move", "confidence": "0.18623589", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i need a medication", "i have a headache"], "top3_probabilities": [0.18623588979244232, 0.1766945868730545, 0.16850516200065613]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_phone_my_family__useruser01__18to39__male__not_specified__20250809T054052_mouth_cropped.webm", "ground_truth": "i want to phone my family", "predicted": "i want to phone my family", "confidence": "0.6205641", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to phone my family", "who is with me today", "what happened to me"], "top3_probabilities": [0.6205641031265259, 0.28831759095191956, 0.07778474688529968]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_phone_my_family__useruser01__18to39__male__not_specified__20250809T054104_mouth_cropped.webm", "ground_truth": "i want to phone my family", "predicted": "i want to phone my family", "confidence": "0.67149526", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i want to phone my family", "what time is my wife coming", "who is with me today"], "top3_probabilities": [0.671495258808136, 0.09383036196231842, 0.07405605167150497]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_wife__useruser01__18to39__male__not_specified__20250809T054117_mouth_cropped.webm", "ground_truth": "i want to see my wife", "predicted": "i need to move", "confidence": "0.7378653", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i want to see my wife", "i feel anxious"], "top3_probabilities": [0.7378653287887573, 0.09592337161302567, 0.03228429704904556]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/my_chest_hurts__useruser01__18to39__male__not_specified__20250809T054600_mouth_cropped.webm", "ground_truth": "my chest hurts", "predicted": "my back hurts", "confidence": "0.31651604", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["my back hurts", "i m confused", "i need to move"], "top3_probabilities": [0.31651604175567627, 0.17982180416584015, 0.16804467141628265]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_have_a_headache__useruser01__18to39__male__not_specified__20250809T054840_mouth_cropped.webm", "ground_truth": "i have a headache", "predicted": "i need to sit up", "confidence": "0.5827606", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i feel anxious", "i have a headache"], "top3_probabilities": [0.5827605724334717, 0.2795541286468506, 0.10421536862850189]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_help__useruser01__18to39__male__not_specified__20250809T055258_mouth_cropped.webm", "ground_truth": "i need help", "predicted": "i need to sit up", "confidence": "0.3267644", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i need to sit up", "i have a headache", "i need to lie down"], "top3_probabilities": [0.32676440477371216, 0.3172002136707306, 0.16146019101142883]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/am_i_getting_better__useruser01__18to39__male__not_specified__20250809T053814_mouth_cropped.webm", "ground_truth": "am i getting better", "predicted": "am i getting better", "confidence": "0.7369805", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["am i getting better", "i have a headache", "i need a medication"], "top3_probabilities": [0.7369804978370667, 0.09745246917009354, 0.02850150689482689]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/please_explain_again__useruser01__18to39__male__not_specified__20250809T053841_mouth_cropped.webm", "ground_truth": "please explain again", "predicted": "i have a headache", "confidence": "0.28110585", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i have a headache", "please explain again", "i need a medication"], "top3_probabilities": [0.2811058461666107, 0.19172167778015137, 0.10026735812425613]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/what_time_is_my_husband_coming__useruser01__18to39__male__not_specified__20250809T054355_mouth_cropped.webm", "ground_truth": "what time is my husband coming", "predicted": "i feel anxious", "confidence": "0.35305572", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["i feel anxious", "i need to move", "i want to see my husband"], "top3_probabilities": [0.3530557155609131, 0.3399451673030853, 0.056786131113767624]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/stay_with_me_please__useruser01__18to39__male__not_specified__20250809T054517_mouth_cropped.webm", "ground_truth": "stay with me please", "predicted": "i need to move", "confidence": "0.45904854", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "stay with me please", "what time is my wife coming"], "top3_probabilities": [0.4590485394001007, 0.2908822000026703, 0.06994014978408813]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_lie_down__useruser01__18to39__male__not_specified__20250809T055202_mouth_cropped.webm", "ground_truth": "i need to lie down", "predicted": "i need to lie down", "confidence": "0.6845724", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to lie down", "i need to sit up", "i have a headache"], "top3_probabilities": [0.6845723986625671, 0.1715145707130432, 0.07600844651460648]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_wife__useruser01__18to39__male__not_specified__20250809T054129_mouth_cropped.webm", "ground_truth": "i want to see my wife", "predicted": "i need to move", "confidence": "0.7519342", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i want to see my wife", "what time is my wife coming"], "top3_probabilities": [0.7519341707229614, 0.1204538568854332, 0.03228698670864105]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_lie_down__useruser01__18to39__male__not_specified__20250809T055150_mouth_cropped.webm", "ground_truth": "i need to lie down", "predicted": "i need to sit up", "confidence": "0.51834035", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.5183403491973877, 0.33544373512268066, 0.11814546585083008]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_use_the_toilet__useruser01__18to39__male__not_specified__20250809T055242_mouth_cropped.webm", "ground_truth": "i need to use the toilet", "predicted": "what happened to me", "confidence": "0.7049178", "is_correct": false, "is_top3_correct": false, "top3_phrases": ["what happened to me", "i want to phone my family", "who is with me today"], "top3_probabilities": [0.7049177885055542, 0.1199895441532135, 0.10379872471094131]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_m_in_pain__useruser01__18to39__male__not_specified__20250809T054804_mouth_cropped.webm", "ground_truth": "i m in pain", "predicted": "i need to move", "confidence": "0.6850302", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to move", "i feel anxious", "i m in pain"], "top3_probabilities": [0.6850302219390869, 0.17091724276542664, 0.07450912147760391]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_sit_up__useruser01__18to39__male__not_specified__20250809T055059_mouth_cropped.webm", "ground_truth": "i need to sit up", "predicted": "i need to sit up", "confidence": "0.9731818", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.9731817841529846, 0.009566663764417171, 0.006457418203353882]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_want_to_see_my_husband__useruser01__18to39__male__not_specified__20250809T055416_mouth_cropped.webm", "ground_truth": "i want to see my husband", "predicted": "i need to sit up", "confidence": "0.2607337", "is_correct": false, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i want to see my husband", "i need to lie down"], "top3_probabilities": [0.2607336938381195, 0.2598628103733063, 0.2021656036376953]}, {"video_path": "/Users/<USER>/Desktop/reference videos for training/i_need_to_sit_up__useruser01__18to39__male__not_specified__20250809T055112_mouth_cropped.webm", "ground_truth": "i need to sit up", "predicted": "i need to sit up", "confidence": "0.9890155", "is_correct": true, "is_top3_correct": true, "top3_phrases": ["i need to sit up", "i need to lie down", "i have a headache"], "top3_probabilities": [0.989015519618988, 0.005468032788485289, 0.0023164714220911264]}]}