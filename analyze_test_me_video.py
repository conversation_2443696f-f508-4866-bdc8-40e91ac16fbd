#!/usr/bin/env python3
"""
Analyze 'test me.mov' using validated LipNet Perfect 10 preprocessing pipeline
Classify against 10 Perfect ICU phrases with exact validation specifications
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
from pathlib import Path
import sys
import time
from typing import Dict, List, Optional, Tuple

# Add current directory to path
sys.path.append('.')

from lipnet_perfect_10 import LipNetPerfect10, LipNetPerfect10Manager
from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class TestMeVideoAnalyzer:
    """Analyzer for 'test me.mov' using validated preprocessing pipeline"""
    
    def __init__(self):
        """Initialize the test video analyzer"""
        
        self.device = torch.device('cpu')
        
        # Perfect 10 phrases (exact classification labels)
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        
        # Model paths (priority order)
        self.lipnet_rescue_path = "checkpoints/lipnet_perfect_10_rescue/best_lipnet_perfect_10_rescue_model.pth"
        self.mobile3d_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
        
        # Models
        self.active_model = None
        self.active_model_name = None
        
        # Validated preprocessing specifications (exact same as dataset validation)
        self.target_shape = (1, 64, 112, 112)
        self.target_frames = 64
        self.target_size = (112, 112)
        self.target_fps = 25.0
        self.expected_value_range = (-4.0, 3.0)
        
        # Enhanced video processor (identical to validation pipeline)
        self.video_processor = VideoProcessor(
            target_frames=self.target_frames,
            target_size=self.target_size,
            grayscale=True,
            fps=self.target_fps,
            mouth_crop=None,  # Will be set based on video type
            use_dataset_normalization=True
        )
        
        print(f"🎬 Test Me Video Analyzer Initialized")
        print(f"   Target video: test me.mov")
        print(f"   Validated preprocessing: [1, 64, 112, 112], 25 FPS, z-score normalization")
        print(f"   Perfect phrases: {len(self.perfect_phrases)}")
    
    def find_test_video(self) -> Optional[str]:
        """Find 'test me.mov' video file on desktop"""
        
        print(f"\n🔍 Locating Test Video")
        print("=" * 25)
        
        # Primary target
        primary_path = "/Users/<USER>/Desktop/test me.mov"
        
        if Path(primary_path).exists():
            print(f"✅ Found: {primary_path}")
            return primary_path
        
        # Search for similar filenames
        desktop_path = Path("/Users/<USER>/Desktop")
        if not desktop_path.exists():
            print(f"❌ Desktop path not found: {desktop_path}")
            return None
        
        print(f"🔍 Searching desktop for similar files...")
        
        # Search patterns
        search_patterns = [
            "*test me*",
            "*test_me*",
            "*testme*",
            "*Test Me*",
            "*TEST ME*"
        ]
        
        found_files = []
        for pattern in search_patterns:
            matches = list(desktop_path.glob(pattern))
            found_files.extend(matches)
        
        # Filter for video files
        video_extensions = ['.mov', '.mp4', '.avi', '.webm', '.mkv']
        video_files = [f for f in found_files if f.suffix.lower() in video_extensions]
        
        if video_files:
            print(f"📁 Found similar video files:")
            for i, video_file in enumerate(video_files, 1):
                print(f"   {i}. {video_file.name}")
            
            # Use first match
            selected_video = str(video_files[0])
            print(f"✅ Using: {selected_video}")
            return selected_video
        
        # List all video files on desktop for reference
        all_videos = []
        for ext in video_extensions:
            all_videos.extend(list(desktop_path.glob(f"*{ext}")))
        
        if all_videos:
            print(f"📁 Available video files on desktop:")
            for video in all_videos[:10]:  # Show first 10
                print(f"   - {video.name}")
            if len(all_videos) > 10:
                print(f"   ... and {len(all_videos) - 10} more")
        else:
            print(f"❌ No video files found on desktop")
        
        return None
    
    def load_best_available_model(self) -> bool:
        """Load best available model in priority order"""
        
        print(f"\n🤖 Loading Best Available Model")
        print("=" * 35)
        
        # Priority 1: LipNet Perfect 10 Rescue
        if Path(self.lipnet_rescue_path).exists():
            try:
                print(f"🧠 Loading LipNet Perfect 10 Rescue...")
                checkpoint = torch.load(self.lipnet_rescue_path, map_location=self.device)
                
                manager = LipNetPerfect10Manager()
                self.active_model = manager.create_model(
                    hidden_dim=256,
                    num_rnn_layers=2,
                    rnn_type='LSTM',
                    dropout=0.3
                )
                
                self.active_model.load_state_dict(checkpoint['model_state_dict'])
                self.active_model.to(self.device)
                self.active_model.eval()
                self.active_model_name = "LipNet Perfect 10 Rescue"
                
                accuracy = checkpoint.get('best_val_accuracy', 0.0)
                print(f"   ✅ LipNet Rescue loaded: {self.active_model.get_num_parameters():,} params, {accuracy:.1%} accuracy")
                return True
                
            except Exception as e:
                print(f"   ❌ LipNet Rescue loading failed: {e}")
        else:
            print(f"🧠 LipNet Rescue model not found: {self.lipnet_rescue_path}")
        
        # Priority 2: Enhanced Mobile3DTiny
        if Path(self.mobile3d_path).exists():
            try:
                print(f"📱 Loading Enhanced Mobile3DTiny...")
                checkpoint = torch.load(self.mobile3d_path, map_location=self.device)
                
                self.active_model = Perfect10Mobile3DTiny(num_classes=10)
                self.active_model.load_state_dict(checkpoint['model_state_dict'])
                self.active_model.to(self.device)
                self.active_model.eval()
                self.active_model_name = "Enhanced Mobile3DTiny"
                
                accuracy = checkpoint.get('best_val_accuracy', 0.0)
                print(f"   ✅ Mobile3DTiny loaded: {self.active_model.get_num_parameters():,} params, {accuracy:.1%} accuracy")
                return True
                
            except Exception as e:
                print(f"   ❌ Mobile3DTiny loading failed: {e}")
        else:
            print(f"📱 Mobile3DTiny model not found: {self.mobile3d_path}")
        
        print(f"\n❌ No models available for classification")
        print(f"💡 Training status:")
        print(f"   LipNet Rescue: Train with validated dataset")
        print(f"   Mobile3DTiny: python enhanced_perfect_10_training.py")
        
        return False
    
    def apply_validated_preprocessing(self, video_path: str) -> Dict:
        """Apply exact validated preprocessing pipeline"""
        
        print(f"\n🔧 Applying Validated Preprocessing Pipeline")
        print("=" * 45)
        
        video_name = Path(video_path).name
        preprocessing_result = {
            'success': False,
            'video_name': video_name,
            'video_path': video_path,
            'tensor_shape': None,
            'value_range': None,
            'processing_time_ms': 0,
            'validation_passed': False,
            'error': None
        }
        
        try:
            start_time = time.time()
            
            # Detect if video is pre-cropped (same logic as validation)
            is_pre_cropped = "_mouth_cropped" in video_name or "processed_" in video_name
            
            # Set mouth cropping based on video type (identical to validation)
            if is_pre_cropped:
                self.video_processor.mouth_crop = None
                print(f"🔧 Pre-cropped video detected, no additional mouth-cropping")
            else:
                self.video_processor.mouth_crop = (133, 0, 133, 100)
                print(f"🔧 Applying ICU mouth-cropping: (133, 0, 133, 100)")
            
            # Process video with validated pipeline
            video_tensor = self.video_processor.process_video(video_path)
            
            # Validate tensor shape (exact same validation as dataset)
            actual_shape = video_tensor.shape
            if actual_shape != self.target_shape:
                raise ValueError(f"Shape mismatch: {actual_shape} != {self.target_shape}")
            
            # Validate frame count
            if video_tensor.shape[1] != self.target_frames:
                current_frames = video_tensor.shape[1]
                if current_frames > self.target_frames:
                    # Center crop to target frames (same as validation)
                    start_idx = (current_frames - self.target_frames) // 2
                    video_tensor = video_tensor[:, start_idx:start_idx + self.target_frames, :, :]
                elif current_frames < self.target_frames:
                    # Pad with last frame (same as validation)
                    padding_needed = self.target_frames - current_frames
                    last_frame = video_tensor[:, -1:, :, :].repeat(1, padding_needed, 1, 1)
                    video_tensor = torch.cat([video_tensor, last_frame], dim=1)
            
            # Validate data integrity (same as validation)
            if torch.isnan(video_tensor).any() or torch.isinf(video_tensor).any():
                raise ValueError("Video contains NaN or infinite values")
            
            # Validate value range
            min_val = float(video_tensor.min())
            max_val = float(video_tensor.max())
            
            # Final shape validation
            assert video_tensor.shape == self.target_shape, f"Final shape validation failed: {video_tensor.shape}"
            
            processing_time = (time.time() - start_time) * 1000
            
            # Validation checks
            shape_valid = video_tensor.shape == self.target_shape
            range_reasonable = (self.expected_value_range[0] - 1 <= min_val <= self.expected_value_range[1] + 1 and
                              self.expected_value_range[0] - 1 <= max_val <= self.expected_value_range[1] + 1)
            
            validation_passed = shape_valid and range_reasonable
            
            preprocessing_result.update({
                'success': True,
                'tensor_shape': list(video_tensor.shape),
                'value_range': [min_val, max_val],
                'processing_time_ms': processing_time,
                'validation_passed': validation_passed,
                'video_tensor': video_tensor
            })
            
            print(f"✅ Preprocessing completed successfully")
            print(f"   Tensor shape: {video_tensor.shape}")
            print(f"   Value range: [{min_val:.3f}, {max_val:.3f}]")
            print(f"   Processing time: {processing_time:.0f}ms")
            print(f"   Validation: {'✅ PASSED' if validation_passed else '⚠️ WARNING'}")
            
            return preprocessing_result
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            error_msg = str(e)
            
            preprocessing_result.update({
                'error': error_msg,
                'processing_time_ms': processing_time
            })
            
            print(f"❌ Preprocessing failed: {error_msg}")
            print(f"💡 Suggestions:")
            print(f"   - Check video file integrity")
            print(f"   - Verify video format compatibility")
            print(f"   - Ensure sufficient video duration")
            
            return preprocessing_result
    
    def create_temporal_crops(self, video_tensor: torch.Tensor) -> List[torch.Tensor]:
        """Create temporal crops for Test-Time Augmentation"""
        
        C, T, H, W = video_tensor.shape
        target_frames = 64
        
        if T <= target_frames:
            return [video_tensor]
        
        crops = []
        
        # Crop 1: Beginning
        crop1 = video_tensor[:, :target_frames, :, :]
        crops.append(crop1)
        
        # Crop 2: Middle
        middle = T // 2
        start_middle = max(0, middle - target_frames // 2)
        end_middle = min(T, start_middle + target_frames)
        crop2 = video_tensor[:, start_middle:end_middle, :, :]
        
        if crop2.shape[1] < target_frames:
            padding_needed = target_frames - crop2.shape[1]
            last_frame = crop2[:, -1:, :, :].repeat(1, padding_needed, 1, 1)
            crop2 = torch.cat([crop2, last_frame], dim=1)
        
        crops.append(crop2)
        
        # Crop 3: End
        crop3 = video_tensor[:, -target_frames:, :, :]
        crops.append(crop3)
        
        return crops
    
    def classify_video(self, preprocessing_result: Dict) -> Dict:
        """Classify video against 10 Perfect ICU phrases"""
        
        print(f"\n🎯 Classifying Against Perfect 10 ICU Phrases")
        print("=" * 45)
        
        if not preprocessing_result['success']:
            return {
                'success': False,
                'error': 'Preprocessing failed',
                'preprocessing_error': preprocessing_result['error']
            }
        
        if not self.active_model:
            return {
                'success': False,
                'error': 'No model loaded for classification'
            }
        
        try:
            video_tensor = preprocessing_result['video_tensor']
            start_time = time.time()
            
            # Apply Test-Time Augmentation if video is longer than 64 frames
            original_frames = video_tensor.shape[1]
            if original_frames > 64:
                temporal_crops = self.create_temporal_crops(video_tensor)
                print(f"🔄 TTA: {len(temporal_crops)} temporal crops")
                
                all_logits = []
                for crop in temporal_crops:
                    crop_batch = crop.unsqueeze(0).to(self.device)
                    with torch.no_grad():
                        logits = self.active_model(crop_batch)
                        all_logits.append(logits[0])
                
                final_logits = torch.stack(all_logits).mean(dim=0)
                probabilities = F.softmax(final_logits, dim=0)
                tta_used = True
                
            else:
                print(f"🔄 Single inference (video ≤64 frames)")
                video_batch = video_tensor.unsqueeze(0).to(self.device)
                with torch.no_grad():
                    logits = self.active_model(video_batch)
                    final_logits = logits[0]
                    probabilities = F.softmax(final_logits, dim=0)
                tta_used = False
            
            inference_time = (time.time() - start_time) * 1000
            
            # Get predictions
            top3_probs, top3_indices = torch.topk(probabilities, 3)
            top3_probs = top3_probs.cpu().numpy()
            top3_indices = top3_indices.cpu().numpy()
            top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
            
            # Determine confidence level
            top_confidence = float(top3_probs[0])
            if top_confidence >= 0.8:
                confidence_level = "Very High"
                confidence_emoji = "🟢"
            elif top_confidence >= 0.6:
                confidence_level = "High"
                confidence_emoji = "🟡"
            elif top_confidence >= 0.4:
                confidence_level = "Moderate"
                confidence_emoji = "🟠"
            else:
                confidence_level = "Low"
                confidence_emoji = "🔴"
            
            return {
                'success': True,
                'model_used': self.active_model_name,
                'tta_used': tta_used,
                'inference_time_ms': inference_time,
                'top_prediction': top3_phrases[0],
                'top_confidence': top_confidence,
                'confidence_level': confidence_level,
                'confidence_emoji': confidence_emoji,
                'top3_phrases': top3_phrases,
                'top3_probabilities': top3_probs.tolist(),
                'all_probabilities': probabilities.cpu().numpy().tolist()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def display_results(self, video_path: str, preprocessing_result: Dict, classification_result: Dict):
        """Display formatted analysis results"""
        
        print(f"\n" + "="*60)
        print(f"🎬 TEST ME VIDEO ANALYSIS RESULTS")
        print(f"="*60)
        
        video_name = Path(video_path).name
        
        # Preprocessing results
        if preprocessing_result['success']:
            tensor_shape = preprocessing_result['tensor_shape']
            value_range = preprocessing_result['value_range']
            validation_status = "PASSED" if preprocessing_result['validation_passed'] else "FAILED"
        else:
            tensor_shape = "Failed"
            value_range = "Failed"
            validation_status = "FAILED"
        
        # Classification results
        if classification_result['success']:
            model_used = classification_result['model_used']
            tta_status = "Yes" if classification_result['tta_used'] else "No"
            top_prediction = classification_result['top_prediction']
            top_confidence = classification_result['top_confidence']
            confidence_level = classification_result['confidence_level']
            confidence_emoji = classification_result['confidence_emoji']
            top3_phrases = classification_result['top3_phrases']
            top3_probs = classification_result['top3_probabilities']
            inference_time = classification_result['inference_time_ms']
        else:
            model_used = "Failed to load"
            tta_status = "N/A"
            top_prediction = "Classification failed"
            top_confidence = 0.0
            confidence_level = "N/A"
            confidence_emoji = "❌"
            top3_phrases = ["Failed", "Failed", "Failed"]
            top3_probs = [0.0, 0.0, 0.0]
            inference_time = 0.0
        
        # Display formatted results
        print(f"🎬 Video: {video_name}")
        print(f"🔧 Preprocessing: {tensor_shape}, range {value_range}, TTA: {tta_status}")
        print(f"🤖 Model: {model_used}")
        
        print(f"\n🏆 CLASSIFICATION RESULT: \"{top_prediction.title()}\" ({top_confidence:.1%} confidence)")
        print(f"Confidence Level: {confidence_emoji} {confidence_level}")
        
        print(f"\n📊 TOP-3 PREDICTIONS:")
        rank_emojis = ["🥇", "🥈", "🥉"]
        for i, (phrase, prob) in enumerate(zip(top3_phrases, top3_probs)):
            print(f"{rank_emojis[i]} {phrase.title()}: {prob:.1%}")
        
        processing_time = preprocessing_result.get('processing_time_ms', 0)
        total_time = processing_time + inference_time
        print(f"\n⏱️ Processing Time: {total_time:.0f}ms")
        print(f"✅ Preprocessing Validation: {validation_status}")
        
        # Error reporting
        if not preprocessing_result['success']:
            print(f"\n❌ Preprocessing Error: {preprocessing_result['error']}")
        
        if not classification_result['success']:
            print(f"\n❌ Classification Error: {classification_result['error']}")

def main():
    """Main analysis function"""
    
    print("🎬 Test Me Video Analysis with Validated LipNet Pipeline")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = TestMeVideoAnalyzer()
    
    # Find test video
    video_path = analyzer.find_test_video()
    if not video_path:
        print("❌ Cannot proceed without test video")
        return
    
    # Load best available model
    if not analyzer.load_best_available_model():
        print("❌ Cannot proceed without trained model")
        return
    
    # Apply validated preprocessing
    preprocessing_result = analyzer.apply_validated_preprocessing(video_path)
    
    # Classify video
    classification_result = analyzer.classify_video(preprocessing_result)
    
    # Display results
    analyzer.display_results(video_path, preprocessing_result, classification_result)
    
    print(f"\n🎉 Test Me Video Analysis Complete!")

if __name__ == '__main__':
    main()
