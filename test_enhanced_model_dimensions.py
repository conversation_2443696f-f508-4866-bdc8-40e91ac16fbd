#!/usr/bin/env python3
"""
Test if the current model architecture can handle enhanced dimensions
Tests 64 frames and 112×112 resolution compatibility
"""

import torch
import sys
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.model import Mobile3DTiny

def test_base_model_dimensions():
    """Test if base Mobile3DTiny can handle enhanced dimensions"""
    
    print("🧪 Testing Base Mobile3DTiny with Enhanced Dimensions")
    print("=" * 55)
    
    # Test configurations
    test_configs = [
        {
            'name': 'Current (32 frames, 96×96)',
            'batch_size': 2,
            'frames': 32,
            'height': 96,
            'width': 96
        },
        {
            'name': 'Enhanced (64 frames, 112×112)',
            'batch_size': 2,
            'frames': 64,
            'height': 112,
            'width': 112
        }
    ]
    
    # Create base model
    base_model = Mobile3DTiny(num_classes=26)
    base_model.eval()
    
    print(f"📊 Base model parameters: {sum(p.numel() for p in base_model.parameters()):,}")
    
    for config in test_configs:
        print(f"\n🔍 Testing: {config['name']}")
        
        try:
            # Create test input
            dummy_input = torch.randn(
                config['batch_size'], 1, 
                config['frames'], 
                config['height'], 
                config['width']
            )
            
            print(f"   Input shape: {dummy_input.shape}")
            
            # Forward pass
            with torch.no_grad():
                output = base_model(dummy_input)
            
            print(f"   Output shape: {output.shape}")
            print(f"   ✅ SUCCESS: Model handles {config['name']}")
            
        except Exception as e:
            print(f"   ❌ FAILED: {e}")

def test_perfect_10_model_dimensions():
    """Test if Perfect10Mobile3DTiny can handle enhanced dimensions"""
    
    print(f"\n🧪 Testing Perfect10Mobile3DTiny with Enhanced Dimensions")
    print("=" * 60)
    
    # Test configurations
    test_configs = [
        {
            'name': 'Current (32 frames, 96×96)',
            'batch_size': 2,
            'frames': 32,
            'height': 96,
            'width': 96
        },
        {
            'name': 'Enhanced (64 frames, 112×112)',
            'batch_size': 2,
            'frames': 64,
            'height': 112,
            'width': 112
        }
    ]
    
    # Create Perfect 10 model
    perfect_model = Perfect10Mobile3DTiny(num_classes=10)
    perfect_model.eval()
    
    print(f"📊 Perfect 10 model parameters: {sum(p.numel() for p in perfect_model.parameters()):,}")
    
    for config in test_configs:
        print(f"\n🔍 Testing: {config['name']}")
        
        try:
            # Create test input
            dummy_input = torch.randn(
                config['batch_size'], 1, 
                config['frames'], 
                config['height'], 
                config['width']
            )
            
            print(f"   Input shape: {dummy_input.shape}")
            
            # Forward pass
            with torch.no_grad():
                output = perfect_model(dummy_input)
            
            print(f"   Output shape: {output.shape}")
            print(f"   Expected: [{config['batch_size']}, 10]")
            
            if output.shape == torch.Size([config['batch_size'], 10]):
                print(f"   ✅ SUCCESS: Perfect output shape")
            else:
                print(f"   ⚠️  WARNING: Unexpected output shape")
            
        except Exception as e:
            print(f"   ❌ FAILED: {e}")
            import traceback
            traceback.print_exc()

def analyze_model_capacity():
    """Analyze model capacity for enhanced dimensions"""
    
    print(f"\n📊 Model Capacity Analysis")
    print("=" * 30)
    
    # Create models
    base_model = Mobile3DTiny(num_classes=26)
    perfect_model = Perfect10Mobile3DTiny(num_classes=10)
    
    # Count parameters
    base_params = sum(p.numel() for p in base_model.parameters())
    perfect_params = sum(p.numel() for p in perfect_model.parameters())
    
    print(f"📈 Parameter Counts:")
    print(f"   Base Mobile3DTiny (26 classes): {base_params:,}")
    print(f"   Perfect10Mobile3DTiny (10 classes): {perfect_params:,}")
    print(f"   Difference: {perfect_params - base_params:,}")
    
    # Analyze memory requirements
    configs = [
        {'name': 'Current', 'frames': 32, 'size': 96},
        {'name': 'Enhanced', 'frames': 64, 'size': 112}
    ]
    
    print(f"\n💾 Memory Requirements (per sample):")
    for config in configs:
        # Input tensor size in MB
        input_size = 1 * config['frames'] * config['size'] * config['size'] * 4 / (1024 * 1024)  # 4 bytes per float32
        print(f"   {config['name']}: {input_size:.2f} MB input tensor")
    
    # Calculate capacity increase
    current_pixels = 32 * 96 * 96
    enhanced_pixels = 64 * 112 * 112
    capacity_increase = enhanced_pixels / current_pixels
    
    print(f"\n📈 Capacity Analysis:")
    print(f"   Current total pixels: {current_pixels:,}")
    print(f"   Enhanced total pixels: {enhanced_pixels:,}")
    print(f"   Capacity increase: {capacity_increase:.2f}x")

def test_transfer_learning_compatibility():
    """Test if enhanced model can load baseline weights"""
    
    print(f"\n🔄 Testing Transfer Learning Compatibility")
    print("=" * 45)
    
    # Check if baseline model exists
    baseline_path = "checkpoints/reference_training/best_model.pth"
    
    if not Path(baseline_path).exists():
        print(f"⚠️  Baseline model not found: {baseline_path}")
        print(f"   Cannot test transfer learning compatibility")
        return
    
    try:
        # Load baseline checkpoint
        checkpoint = torch.load(baseline_path, map_location='cpu')
        
        print(f"✅ Baseline checkpoint loaded")
        print(f"   Keys: {list(checkpoint.keys())}")
        
        # Create Perfect 10 model
        perfect_model = Perfect10Mobile3DTiny(num_classes=10)
        
        # Try to load backbone weights (should work regardless of input dimensions)
        if 'model_state_dict' in checkpoint:
            baseline_state = checkpoint['model_state_dict']
            
            # Filter backbone weights (exclude classifier)
            backbone_weights = {k: v for k, v in baseline_state.items() 
                              if not k.startswith('classifier')}
            
            print(f"   Backbone weights: {len(backbone_weights)} layers")
            
            # Load backbone weights
            missing_keys, unexpected_keys = perfect_model.load_state_dict(backbone_weights, strict=False)
            
            print(f"   Missing keys: {len(missing_keys)}")
            print(f"   Unexpected keys: {len(unexpected_keys)}")
            
            if len(missing_keys) <= 2:  # Only classifier should be missing
                print(f"   ✅ Transfer learning compatibility: GOOD")
            else:
                print(f"   ⚠️  Transfer learning compatibility: NEEDS ATTENTION")
        
    except Exception as e:
        print(f"❌ Transfer learning test failed: {e}")

def main():
    """Main testing function"""
    
    print("🧪 Enhanced Model Dimensions Compatibility Test")
    print("=" * 50)
    
    # Test base model
    test_base_model_dimensions()
    
    # Test Perfect 10 model
    test_perfect_10_model_dimensions()
    
    # Analyze model capacity
    analyze_model_capacity()
    
    # Test transfer learning
    test_transfer_learning_compatibility()
    
    print(f"\n🎉 Model Dimension Testing Complete!")
    print("=" * 40)
    print(f"✅ Compatibility tests completed")
    print(f"✅ Model capacity analyzed")
    print(f"✅ Transfer learning compatibility checked")

if __name__ == '__main__':
    main()
