#!/usr/bin/env python3
"""
Simple mouth-cropping fix for Perfect 10 videos
Tests different cropping regions to find the correct mouth area
"""

import cv2
import numpy as np
import torch
from pathlib import Path
import sys
import os

def test_crop_regions(video_path: str):
    """Test different cropping regions to find the mouth area"""
    
    print(f"\n🔍 Testing crop regions for: {Path(video_path).name}")
    
    cap = cv2.VideoCapture(video_path)
    
    # Get video info
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"   📊 Video: {width}×{height}, {total_frames} frames")
    
    # Read middle frame
    cap.set(cv2.CAP_PROP_POS_FRAMES, max(0, total_frames//2))
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        print(f"   ❌ Could not read frame")
        return None
    
    # Test different crop regions based on typical mouth positions
    crop_tests = [
        {'name': 'Original ICU (133,0,133,100)', 'x': 133, 'y': 0, 'w': 133, 'h': 100},
        {'name': 'Center Lower (100,100,200,100)', 'x': 100, 'y': 100, 'w': 200, 'h': 100},
        {'name': 'Right Lower (200,100,200,100)', 'x': 200, 'y': 100, 'w': 200, 'h': 100},
        {'name': 'Full Center (100,50,200,100)', 'x': 100, 'y': 50, 'w': 200, 'h': 100},
        {'name': 'Left Center (50,50,150,100)', 'x': 50, 'y': 50, 'w': 150, 'h': 100},
        {'name': 'Right Center (250,50,150,100)', 'x': 250, 'y': 50, 'w': 150, 'h': 100}
    ]
    
    best_crop = None
    best_score = 0
    
    for crop in crop_tests:
        x, y, w, h = crop['x'], crop['y'], crop['w'], crop['h']
        
        # Ensure coordinates are within bounds
        x = max(0, min(x, width - 1))
        y = max(0, min(y, height - 1))
        x_end = min(x + w, width)
        y_end = min(y + h, height)
        
        # Extract region
        region = frame[y:y_end, x:x_end]
        
        if region.size > 0:
            # Convert to grayscale and calculate statistics
            gray_region = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
            mean_intensity = np.mean(gray_region)
            std_intensity = np.std(gray_region)
            
            # Score based on mean intensity and variation (mouth area should have both)
            score = mean_intensity * std_intensity
            
            print(f"   {crop['name']}: mean={mean_intensity:.1f}, std={std_intensity:.1f}, score={score:.1f}")
            
            if score > best_score and mean_intensity > 10:  # Avoid completely black regions
                best_score = score
                best_crop = {
                    'name': crop['name'],
                    'coords': (x, y, x_end - x, y_end - y),
                    'score': score
                }
        else:
            print(f"   {crop['name']}: EMPTY REGION")
    
    if best_crop:
        print(f"   🏆 Best crop: {best_crop['name']} at {best_crop['coords']} (score: {best_crop['score']:.1f})")
        return best_crop['coords']
    else:
        print(f"   ❌ No suitable crop region found")
        return None

def process_with_adaptive_cropping(input_folder: str, output_folder: str):
    """Process videos with adaptive mouth-cropping"""
    
    print(f"\n🎬 Processing with Adaptive Mouth-Cropping")
    print("=" * 45)
    
    input_path = Path(input_folder)
    output_path = Path(output_folder)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Test first video to find best crop region
    test_video = input_path / "1.webm"
    if not test_video.exists():
        print(f"❌ Test video not found: {test_video}")
        return
    
    # Find optimal crop coordinates
    optimal_coords = test_crop_regions(str(test_video))
    
    if optimal_coords is None:
        print(f"❌ Could not determine optimal crop coordinates")
        return
    
    crop_x, crop_y, crop_w, crop_h = optimal_coords
    print(f"\n🎯 Using crop coordinates: ({crop_x}, {crop_y}, {crop_w}, {crop_h})")
    
    # Process all videos with the optimal coordinates
    for i in range(1, 6):
        video_file = input_path / f"{i}.webm"
        output_file = output_path / f"processed_{i}.mp4"
        
        if video_file.exists():
            success = process_single_video_adaptive(str(video_file), str(output_file), optimal_coords)
            if success:
                print(f"   ✅ Processed: {video_file.name} → {output_file.name}")
            else:
                print(f"   ❌ Failed: {video_file.name}")
        else:
            print(f"   ⚠️  Not found: {video_file.name}")

def process_single_video_adaptive(input_path: str, output_path: str, crop_coords: tuple) -> bool:
    """Process single video with adaptive cropping coordinates"""
    
    try:
        crop_x, crop_y, crop_w, crop_h = crop_coords
        
        cap = cv2.VideoCapture(input_path)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Sample 32 frames evenly
        if total_frames <= 32:
            frame_indices = list(range(total_frames))
        else:
            frame_indices = np.linspace(0, total_frames - 1, 32, dtype=int)
        
        processed_frames = []
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            
            if ret:
                # Apply adaptive cropping
                height, width = frame.shape[:2]
                
                # Ensure coordinates are within bounds
                x1 = max(0, min(crop_x, width - 1))
                y1 = max(0, min(crop_y, height - 1))
                x2 = min(x1 + crop_w, width)
                y2 = min(y1 + crop_h, height)
                
                # Extract mouth region
                mouth_region = frame[y1:y2, x1:x2]
                
                if mouth_region.size > 0:
                    # Convert to grayscale
                    if len(mouth_region.shape) == 3:
                        mouth_gray = cv2.cvtColor(mouth_region, cv2.COLOR_BGR2GRAY)
                    else:
                        mouth_gray = mouth_region
                    
                    # Resize to 96×96
                    mouth_resized = cv2.resize(mouth_gray, (96, 96))
                    
                    # Normalize to [0,1]
                    mouth_normalized = mouth_resized.astype(np.float32) / 255.0
                    
                    processed_frames.append(mouth_normalized)
                else:
                    # Create black frame if crop failed
                    black_frame = np.zeros((96, 96), dtype=np.float32)
                    processed_frames.append(black_frame)
            else:
                # Create black frame if read failed
                black_frame = np.zeros((96, 96), dtype=np.float32)
                processed_frames.append(black_frame)
        
        cap.release()
        
        # Ensure exactly 32 frames
        while len(processed_frames) < 32:
            if processed_frames:
                processed_frames.append(processed_frames[-1])
            else:
                processed_frames.append(np.zeros((96, 96), dtype=np.float32))
        
        processed_frames = processed_frames[:32]
        
        # Convert to video file
        frames_uint8 = [(frame * 255).astype(np.uint8) for frame in processed_frames]
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, 10.0, (96, 96), isColor=False)
        
        for frame in frames_uint8:
            out.write(frame)
        
        out.release()
        
        # Verify the output has content
        test_frame = frames_uint8[0] if frames_uint8 else None
        if test_frame is not None and np.mean(test_frame) > 5:  # Not completely black
            return True
        else:
            print(f"   ⚠️  Output appears to be black/empty")
            return False
        
    except Exception as e:
        print(f"   ❌ Processing error: {e}")
        return False

def main():
    """Main function for adaptive mouth-cropping"""
    
    print("🔧 Adaptive Mouth-Cropping for Perfect 10 Videos")
    print("=" * 50)
    
    input_folder = "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on"
    output_folder = "/Users/<USER>/Desktop/processed_perfect_10_videos"
    
    # Process with adaptive cropping
    process_with_adaptive_cropping(input_folder, output_folder)
    
    # Open output folder
    try:
        os.system(f'open "{output_folder}"')
        print(f"\n✅ Output folder opened for inspection")
    except Exception as e:
        print(f"\n⚠️  Could not auto-open folder: {e}")
    
    print(f"\n🎉 Adaptive Processing Complete!")
    print("=" * 35)
    print("✅ Videos processed with optimal mouth-cropping")
    print("✅ Ready for visual inspection")

if __name__ == '__main__':
    main()
