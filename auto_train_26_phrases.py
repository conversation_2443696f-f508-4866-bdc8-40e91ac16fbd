#!/usr/bin/env python3
"""
Complete automation script for 26-phrase ICU classifier
- Preprocess to LipNet spec (Python 3.9 friendly, no mediapipe needed)
- (Optional) Build reference embeddings with your LipNet model
- (Optional) Train a classical classifier head

Usage examples:
  # Preprocess ONLY (recommended first run)
  python auto_train_26_phrases.py --mode preprocess --input "~/Desktop/icu-26-phrases" --output "data"

  # Build reference DB (requires a LipNet embedding model on disk)
  python auto_train_26_phrases.py --mode refdb

  # Train classifier (requires embeddings/model available)
  python auto_train_26_phrases.py --mode train

  # Run all steps
  python auto_train_26_phrases.py --mode all
"""

import os
import sys
import csv
import json
import pickle
import argparse
import subprocess
import logging
from pathlib import Path

import cv2
import numpy as np

# -----------------------------
# Config / constants
# -----------------------------
TARGET_FPS = 25
TARGET_W, TARGET_H = 140, 46     # LipNet frame size (W,H)
TARGET_T = 75                    # frames per clip

# Logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger("icu26")

# 26 ICU phrases
ICU_26_PHRASES = [
    "Where am I?", "Who is with me today?", "What happened to me?", "Am I getting better?",
    "Please explain again.", "Where is my wife?", "Where is my husband?",
    "I want to phone my family.", "I want to see my wife.", "I want to see my husband.",
    "What time is my wife coming?", "What time is my husband coming?", "I feel anxious.",
    "Stay with me, please.", "My chest hurts.", "My back hurts.", "I'm confused.",
    "I'm in pain.", "I have a headache.", "I'm uncomfortable.", "I need a medication.",
    "I need to lie down.", "I need to use the toilet.", "I need to sit up.",
    "I need help.", "I need to move."
]

# For filename → phrase matching (loose keywords)
PHRASE_KEYWORDS = [
    ["where", "am", "i"],
    ["who", "is", "with", "me"],
    ["what", "happened"],
    ["am", "i", "getting", "better"],
    ["please", "explain"],
    ["where", "is", "my", "wife"],
    ["where", "is", "my", "husband"],
    ["phone", "my", "family"],
    ["see", "my", "wife"],
    ["see", "my", "husband"],
    ["time", "wife", "coming"],
    ["time", "husband", "coming"],
    ["feel", "anxious"],
    ["stay", "with", "me"],
    ["chest", "hurts"],
    ["back", "hurts"],
    ["confused"],
    ["in", "pain"],
    ["headache"],
    ["uncomfortable"],
    ["need", "medication"],
    ["lie", "down"],
    ["use", "toilet"],
    ["sit", "up"],
    ["need", "help"],
    ["need", "move"],
]

def slug_phrase(p: str) -> str:
    return "".join(ch.lower() if ch.isalnum() else "_" for ch in p).strip("_")


class ICU26PhraseTrainer:
    def __init__(self, input_dir="~/Desktop/icu-26-phrases/", data_dir="data", demographic="male_under_50"):
        self.input_dir = os.path.expanduser(input_dir)
        self.data_dir = data_dir
        self.demographic = demographic
        self.lipnet_model = None
        Path(self.data_dir).mkdir(parents=True, exist_ok=True)
        self.manifest_path = Path(self.data_dir) / "manifest.csv"

    # -----------------------------
    # Step 1: Preprocess videos
    # -----------------------------
    def step1_preprocess_videos(self) -> bool:
        logger.info("🎬 Step 1: Preprocessing videos → LipNet spec")
        if not os.path.exists(self.input_dir):
            raise FileNotFoundError(f"Input directory not found: {self.input_dir}")

        # Recursive search, include WEBM + uppercase variants
        exts = ('*.mp4','*.MP4','*.mov','*.MOV','*.avi','*.AVI','*.mkv','*.MKV','*.webm','*.WEBM')
        video_files = []
        for ext in exts:
            video_files.extend(Path(self.input_dir).rglob(ext))
        video_files = sorted(video_files)
        if not video_files:
            raise FileNotFoundError(f"No video files found in {self.input_dir}")

        logger.info(f"Input dir (expanded): {self.input_dir}")
        logger.info(f"Found {len(video_files)} files (recursive search)")

        processed = 0
        for vf in video_files:
            phrase_idx = self._match_filename_to_phrase(vf.name)
            if phrase_idx is None:
                logger.warning(f"Could not match phrase for: {vf.name}")
                continue
            ok = self._process_single_video(vf, phrase_idx)
            if ok:
                processed += 1

        logger.info(f"✅ Preprocessed {processed} / {len(video_files)} files")
        return processed > 0

    def _match_filename_to_phrase(self, filename: str):
        fn = filename.lower().replace("-", " ").replace("_", " ")
        for idx, words in enumerate(PHRASE_KEYWORDS):
            if all(w in fn for w in words):
                return idx
        return None

    def _read_video_frames_any(self, video_path: Path):
        """
        Read frames as grayscale using OpenCV; if that fails (e.g., WEBM/VP9),
        fall back to imageio (bundled ffmpeg via imageio-ffmpeg).
        Returns: (frames_list, fps_src)
        """
        frames = []
        fps_src = None

        # Try OpenCV first
        cap = cv2.VideoCapture(str(video_path))
        if cap.isOpened():
            fps_src = cap.get(cv2.CAP_PROP_FPS) or None
            while True:
                ret, fr = cap.read()
                if not ret:
                    break
                gray = cv2.cvtColor(fr, cv2.COLOR_BGR2GRAY)
                frames.append(gray)
            cap.release()

        # Fallback to imageio if no frames
        if not frames:
            try:
                import imageio
            except ImportError:
                raise RuntimeError(
                    "imageio is required to read this format. Install with: pip install imageio imageio-ffmpeg"
                )
            rdr = imageio.get_reader(str(video_path))
            meta = {}
            try:
                meta = rdr.get_meta_data()
            except Exception:
                pass
            fps_src = meta.get("fps", fps_src) or TARGET_FPS
            for fr in rdr:  # RGB frames
                gray = cv2.cvtColor(fr, cv2.COLOR_RGB2GRAY)
                frames.append(gray)
            rdr.close()

        return frames, (fps_src or TARGET_FPS)

    def _mouth_box_from_face(self, gray: np.ndarray):
        """Face → lower-face ROI as mouth proxy (no mediapipe; works on Python 3.9)."""
        h, w = gray.shape
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + "haarcascade_frontalface_default.xml")
        faces = face_cascade.detectMultiScale(
            gray, scaleFactor=1.1, minNeighbors=5, flags=cv2.CASCADE_SCALE_IMAGE, minSize=(60, 60)
        )
        if len(faces) == 0:
            # Fallback: central lower region
            bw = int(0.5 * w); bh = int(0.5 * h)
            x1 = (w - bw) // 2; y1 = int(h * 0.45)
            x2 = x1 + bw; y2 = min(h - 1, y1 + bh)
            return x1, y1, x2, y2
        # pick largest face
        x, y, fw, fh = max(faces, key=lambda f: f[2] * f[3])
        mx1 = x + int(0.15 * fw); mx2 = x + int(0.85 * fw)
        my1 = y + int(0.55 * fh); my2 = y + int(0.98 * fh)
        mx1 = max(0, mx1); my1 = max(0, my1)
        mx2 = min(w - 1, mx2); my2 = min(h - 1, my2)
        if mx2 <= mx1 or my2 <= my1:
            return 0, int(h * 0.45), w - 1, min(h - 1, int(h * 0.95))
        return mx1, my1, mx2, my2

    def _process_single_video(self, video_path: Path, phrase_idx: int) -> bool:
        try:
            phrase = ICU_26_PHRASES[phrase_idx]
            phrase_dir = slug_phrase(phrase)
            out_dir = Path(self.data_dir) / phrase_dir
            out_dir.mkdir(parents=True, exist_ok=True)
            out_mp4 = out_dir / f"{video_path.stem}_processed.mp4"

            # Skip if already done
            if out_mp4.exists():
                logger.info(f"Skip (exists): {out_mp4.name}")
                return True

            # Read frames (OpenCV → imageio fallback)
            raw, fps_src = self._read_video_frames_any(video_path)
            if not raw:
                logger.error(f"No frames: {video_path}")
                return False

            # Resample to approx TARGET_FPS
            duration_s = len(raw) / max(fps_src, 1e-6)
            want = max(1, int(round(duration_s * TARGET_FPS)))
            idxs = np.linspace(0, len(raw) - 1, num=want, dtype=int)
            seq = [raw[i] for i in idxs]

            # Detect ROI on mid frame
            mid = seq[len(seq) // 2]
            x1, y1, x2, y2 = self._mouth_box_from_face(mid)

            # Crop + resize each frame
            frames = []
            for fr in seq:
                crop = fr[y1:y2, x1:x2]
                resized = cv2.resize(crop, (TARGET_W, TARGET_H), interpolation=cv2.INTER_AREA)
                frames.append(resized)

            # Pad/trim to exactly TARGET_T
            if len(frames) > TARGET_T:
                frames = frames[:TARGET_T]
            while len(frames) < TARGET_T:
                frames.append(frames[-1])

            # Write grayscale mp4 at TARGET_FPS
            fourcc = cv2.VideoWriter_fourcc(*"mp4v")
            writer = cv2.VideoWriter(str(out_mp4), fourcc, TARGET_FPS, (TARGET_W, TARGET_H), False)
            for fr in frames:
                writer.write(fr)
            writer.release()

            # Write manifest row
            self._write_manifest({
                "input_path": str(video_path),
                "output_path": str(out_mp4),
                "phrase": phrase,
                "phrase_idx": phrase_idx,
                "fps": TARGET_FPS,
                "n_frames": TARGET_T,
                "w": TARGET_W,
                "h": TARGET_H,
                "demographic": self.demographic
            })

            logger.info(f"Processed: {video_path.name} -> {phrase_dir}")
            return True

        except Exception as e:
            logger.error(f"Error processing {video_path}: {e}")
            return False

    def _write_manifest(self, row: dict):
        header = ["input_path","output_path","phrase","phrase_idx","fps","n_frames","w","h","demographic"]
        exists = self.manifest_path.exists()
        with open(self.manifest_path, "a", newline="") as f:
            w = csv.DictWriter(f, fieldnames=header)
            if not exists:
                w.writeheader()
            w.writerow(row)

    # -----------------------------
    # Step 2: Reference DB (embeddings)
    # -----------------------------
    def step2_create_reference_database(self) -> bool:
        logger.info("🧠 Step 2: Creating reference database (embeddings)")
        # Lazy import TF so preprocess can run without it
        try:
            from tensorflow.keras.models import load_model
        except Exception:
            logger.error("TensorFlow not available. Install tensorflow(-macos) to build embeddings.")
            return False

        # Load LipNet embedding model
        self.lipnet_model = self._load_lipnet_model(load_model)
        ref_dir = Path("data/reference/icu_26_phrases")
        ref_dir.mkdir(parents=True, exist_ok=True)

        embeddings_dict = {}
        processed_phrases = 0

        for phrase_idx, phrase in enumerate(ICU_26_PHRASES):
            phrase_dir = slug_phrase(phrase)
            phrase_path = Path(self.data_dir) / phrase_dir
            if not phrase_path.exists():
                logger.warning(f"No data for phrase: {phrase}")
                continue

            video_files = list(phrase_path.glob("*.mp4"))
            if not video_files:
                logger.warning(f"No videos for phrase: {phrase}")
                continue

            phrase_embeddings = []
            for video_file in video_files:
                try:
                    frames = self._extract_frames(video_file)
                    emb = self._get_lipnet_embeddings(frames)
                    phrase_embeddings.append({
                        "embedding": emb,
                        "video_path": str(video_file),
                        "demographic": self.demographic
                    })
                except Exception as e:
                    logger.error(f"Error processing {video_file}: {e}")

            if phrase_embeddings:
                embeddings_dict[phrase] = phrase_embeddings
                processed_phrases += 1
                logger.info(f"Embeddings: {phrase} ({len(phrase_embeddings)} videos)")

        database = {
            "phrases_with_embeddings": processed_phrases,
            "total_phrases": len(ICU_26_PHRASES),
            "demographic": self.demographic,
            "embedding_dimension": (len(phrase_embeddings[0]["embedding"])
                                    if processed_phrases > 0 else 0)
        }
        with open(ref_dir / "reference_database.json", "w") as f:
            json.dump(database, f, indent=2)
        with open(ref_dir / "reference_embeddings.pkl", "wb") as f:
            pickle.dump(embeddings_dict, f)

        logger.info(f"✅ Reference DB created: {processed_phrases}/{len(ICU_26_PHRASES)} phrases")
        return processed_phrases > 0

    def _load_lipnet_model(self, load_model_fn):
        try:
            model_path = "models/lipnet_embedding_model.h5"
            if not Path(model_path).exists():
                logger.info("Embedding model missing. Running download_model.py ...")
                if Path("download_model.py").exists():
                    subprocess.run([sys.executable, "download_model.py"], check=False)
            model = load_model_fn(model_path)
            logger.info("LipNet embedding model loaded")
            return model
        except Exception as e:
            logger.error(f"Failed to load LipNet model: {e}")
            raise

    def _extract_frames(self, video_path: Path, max_frames=TARGET_T) -> np.ndarray:
        frames = []
        cap = cv2.VideoCapture(str(video_path))
        while len(frames) < max_frames and cap.isOpened():
            ret, fr = cap.read()
            if not ret:
                break
            if fr.ndim == 3:
                gray = cv2.cvtColor(fr, cv2.COLOR_BGR2GRAY)
            else:
                gray = fr
            resized = cv2.resize(gray, (TARGET_W, TARGET_H))
            frames.append(resized / 255.0)
        cap.release()
        while len(frames) < max_frames:
            frames.append(frames[-1] if frames else np.zeros((TARGET_H, TARGET_W), dtype=np.float32))
        return np.asarray(frames, dtype=np.float32)

    def _get_lipnet_embeddings(self, frames: np.ndarray) -> np.ndarray:
        # Expect shape (T,H,W) -> (1,T,H,W,1)
        x = frames.reshape(1, TARGET_T, TARGET_H, TARGET_W, 1)
        emb = self.lipnet_model.predict(x, verbose=0)
        return emb.flatten()

    # -----------------------------
    # Step 3: Train classifier
    # -----------------------------
    def step3_train_classifier(self) -> bool:
        logger.info("🎯 Step 3: Training classifier on embeddings")
        # Lazy imports so preprocess runs without sklearn
        try:
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.metrics import classification_report, accuracy_score
            from sklearn.preprocessing import StandardScaler
            from sklearn.pipeline import Pipeline
        except Exception:
            logger.error("scikit-learn not available. Install scikit-learn to train the classifier.")
            return False

        X, y = self._load_training_data()
        if len(X) == 0:
            raise ValueError("No training data found")

        logger.info(f"Loaded {len(X)} samples across {len(set(y))} classes")

        clf = RandomForestClassifier(
            n_estimators=200, max_depth=20, min_samples_split=2,
            class_weight="balanced", random_state=42
        )
        pipe = Pipeline([("scaler", StandardScaler()), ("clf", clf)])
        pipe.fit(X, y)

        Path("models").mkdir(exist_ok=True)
        with open("models/icu_26_classifier.pkl", "wb") as f:
            pickle.dump(pipe, f)

        y_pred = pipe.predict(X)
        acc = accuracy_score(y, y_pred)
        logger.info(f"✅ Train-set accuracy: {acc:.4f}")
        logger.info("\n" + classification_report(y, y_pred, target_names=ICU_26_PHRASES))
        return True

    def _load_training_data(self):
        X, y = [], []
        for phrase_idx, phrase in enumerate(ICU_26_PHRASES):
            phrase_dir = slug_phrase(phrase)
            phrase_path = Path(self.data_dir) / phrase_dir
            if not phrase_path.exists():
                continue
            for vf in phrase_path.glob("*.mp4"):
                try:
                    frames = self._extract_frames(vf)
                    emb = self._get_lipnet_embeddings(frames)
                    X.append(emb); y.append(phrase_idx)
                except Exception as e:
                    logger.error(f"Error processing {vf}: {e}")
        return np.array(X), np.array(y)

    # -----------------------------
    # Orchestrator
    # -----------------------------
    def run(self, mode: str) -> bool:
        logger.info(f"🚀 Mode: {mode}")
        try:
            if mode == "preprocess":
                return self.step1_preprocess_videos()
            elif mode == "refdb":
                return self.step2_create_reference_database()
            elif mode == "train":
                return self.step3_train_classifier()
            elif mode == "all":
                ok = self.step1_preprocess_videos()
                if not ok: raise RuntimeError("Preprocess failed")
                ok = self.step2_create_reference_database()
                if not ok: raise RuntimeError("RefDB failed")
                ok = self.step3_train_classifier()
                if not ok: raise RuntimeError("Train failed")
                return True
            else:
                raise ValueError(f"Unknown mode: {mode}")
        except Exception as e:
            logger.error(f"❌ Pipeline failed: {e}")
            return False


def parse_args():
    p = argparse.ArgumentParser(description="ICU 26-phrase pipeline")
    p.add_argument("--mode", default="preprocess", choices=["preprocess", "refdb", "train", "all"])
    p.add_argument("--input", dest="input_dir", default="~/Desktop/icu-26-phrases", help="Folder with raw reference videos")
    p.add_argument("--output", dest="data_dir", default="data", help="Output data folder")
    p.add_argument("--demographic", default="male_under_50", help="Tag saved in manifest")
    return p.parse_args()


def main():
    args = parse_args()
    trainer = ICU26PhraseTrainer(input_dir=args.input_dir, data_dir=args.output, demographic=args.demographic)
    success = trainer.run(args.mode)
    if success:
        print("\n✅ Completed successfully.")
    else:
        print("\n❌ Failed. Check logs."); sys.exit(1)


if __name__ == "__main__":
    main()
