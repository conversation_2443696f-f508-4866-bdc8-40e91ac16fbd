#!/usr/bin/env python3
"""
Start VSR training immediately with optimized settings
"""

import subprocess
import sys
import os
from pathlib import Path
import time

def start_training():
    """Start training with optimal settings for overnight run"""
    
    print("🚀 STARTING LIGHTWEIGHT VSR TRAINING")
    print("="*50)
    
    # Check environment
    venv_path = Path(".venv_vsr")
    if not venv_path.exists():
        print("❌ Virtual environment not found. Please run setup first.")
        return False
    
    # Check data
    manifest_path = Path("data/manifest.csv")
    if not manifest_path.exists():
        print("❌ Manifest not found. Please create manifest first.")
        return False
    
    config_path = Path("configs/phrases26.yaml")
    if not config_path.exists():
        print("❌ Config not found.")
        return False
    
    print("✅ Environment ready")
    print("✅ Data ready (80 videos, 26 phrases)")
    print("✅ Config ready")
    
    # Create output directory
    output_dir = Path("artifacts/vsr_26p_v1")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Training command with optimized settings
    cmd = [
        "python", "train_vsr.py",
        "--manifest", "data/manifest.csv",
        "--out_dir", "artifacts/vsr_26p_v1", 
        "--config", "configs/phrases26.yaml",
        "--epochs", "40",
        "--batch_size", "4",  # Small batch for stability
        "--val_split", "0.1",
        "--test_split", "0.1", 
        "--num_workers", "0",  # No multiprocessing for stability
        "--amp", "0"  # No mixed precision for CPU
    ]
    
    print(f"\n🎯 Starting training with command:")
    print(f"   {' '.join(cmd)}")
    print(f"\n📊 Expected results:")
    print(f"   - Training time: ~2-3 hours")
    print(f"   - Target accuracy: ≥90% macro-F1")
    print(f"   - Model size: ~2M parameters")
    print(f"   - Output: {output_dir}")
    
    print(f"\n⏰ Training will run overnight...")
    print(f"   Check progress in: {output_dir}/tensorboard/")
    print(f"   Final results in: {output_dir}/metrics.json")
    
    # Start training
    try:
        # Activate virtual environment and run training
        env = os.environ.copy()
        env['PATH'] = f"{venv_path}/bin:{env['PATH']}"
        
        process = subprocess.Popen(
            cmd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print(f"\n🔥 TRAINING STARTED (PID: {process.pid})")
        print(f"📈 Monitoring first few epochs...")
        
        # Monitor first few lines of output
        line_count = 0
        for line in process.stdout:
            print(line.rstrip())
            line_count += 1
            
            # Show first 50 lines then detach
            if line_count >= 50:
                print(f"\n✅ Training is running successfully!")
                print(f"🌙 Process will continue overnight...")
                print(f"📊 Check results tomorrow morning in: {output_dir}")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ Error starting training: {e}")
        return False


def check_training_status():
    """Check if training is already running"""
    
    try:
        # Check for training process
        result = subprocess.run(
            ["pgrep", "-f", "train_vsr.py"], 
            capture_output=True, 
            text=True
        )
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"✅ Training already running (PIDs: {', '.join(pids)})")
            return True
        else:
            print("ℹ️  No training process found")
            return False
            
    except Exception:
        return False


def main():
    """Main function"""
    
    print("🧪 VSR Training Launcher")
    print("="*30)
    
    # Check if training is already running
    if check_training_status():
        print("\n🎯 Training is already in progress!")
        print("📊 Check artifacts/vsr_26p_v1/ for results")
        return
    
    # Start training
    success = start_training()
    
    if success:
        print("\n🎉 TRAINING LAUNCHED SUCCESSFULLY!")
        print("\n📋 Next steps:")
        print("   1. Let training run overnight")
        print("   2. Check results tomorrow morning")
        print("   3. Validate against reference videos")
        print("   4. Deploy to production")
    else:
        print("\n❌ Failed to start training")
        print("   Please check the error messages above")


if __name__ == '__main__':
    main()
