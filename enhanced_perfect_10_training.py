#!/usr/bin/env python3
"""
Enhanced Perfect 10 training with all improvements for >90% accuracy
Includes 64 frames, 112×112, z-score normalization, enhanced augmentations, and class balancing
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import pandas as pd
import numpy as np
from pathlib import Path
import yaml
import sys
from tqdm import tqdm
import json
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor
from enhanced_video_augmentation import EnhancedVideoAugmentation, WeightedPhrasesSampler

class EnhancedPerfect10Dataset(Dataset):
    """Enhanced dataset with 64 frames, 112×112, and comprehensive augmentations"""
    
    def __init__(self, manifest_df: pd.DataFrame, video_processor: VideoProcessor, 
                 augmentation: EnhancedVideoAugmentation = None, is_training: bool = True):
        
        self.manifest_df = manifest_df
        self.video_processor = video_processor
        self.augmentation = augmentation
        self.is_training = is_training
        
        # Perfect 10 phrases mapping
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        
        print(f"📊 Enhanced Perfect 10 Dataset: {len(manifest_df)} samples")
        print(f"   Mode: {'Training' if is_training else 'Validation/Test'}")
        print(f"   Augmentation: {'Enabled' if augmentation and is_training else 'Disabled'}")
    
    def __len__(self):
        return len(self.manifest_df)
    
    def __getitem__(self, idx):
        row = self.manifest_df.iloc[idx]
        video_path = row['video_path']
        phrase = row['phrase']
        
        # Get class index
        class_idx = self.phrase_to_idx[phrase]
        
        try:
            # Process video with enhanced pipeline
            video_tensor = self.video_processor.process_video(video_path)
            
            # Apply augmentation during training
            if self.augmentation and self.is_training:
                video_tensor = self.augmentation(video_tensor)
            
            return video_tensor, class_idx
            
        except Exception as e:
            print(f"⚠️  Failed to load {Path(video_path).name}: {e}")
            # Return dummy data
            if self.video_processor.target_frames == 64:
                dummy_tensor = torch.zeros(1, 64, 112, 112)
            else:
                dummy_tensor = torch.zeros(1, 32, 96, 96)
            return dummy_tensor, class_idx

class EnhancedPerfect10Trainer:
    """Enhanced trainer with all improvements for >90% accuracy"""
    
    def __init__(self, config_path: str):
        """Initialize enhanced trainer"""
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize components
        self.model = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        
        # Training tracking
        self.training_history = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': [],
            'learning_rates': []
        }
        
        self.best_val_accuracy = 0.0
        self.best_model_path = None
        
        print(f"🎯 Enhanced Perfect 10 Trainer Initialized")
        print(f"   Device: {self.device}")
        print(f"   Config: {config_path}")
    
    def setup_data_loaders(self):
        """Setup enhanced data loaders with class balancing"""
        
        print(f"\n📊 Setting up Enhanced Data Loaders")
        print("=" * 40)
        
        # Load manifest
        manifest_path = self.config['data']['manifest_path']
        manifest_df = pd.read_csv(manifest_path)
        
        print(f"📋 Loaded manifest: {len(manifest_df)} videos")
        
        # Enhanced video processor
        video_processor = VideoProcessor(
            target_frames=self.config['data']['frames'],
            target_size=(self.config['data']['height'], self.config['data']['width']),
            grayscale=self.config['data']['grayscale'],
            fps=self.config['data'].get('fps', 25.0),
            use_dataset_normalization=self.config['data'].get('use_dataset_normalization', True)
        )
        
        # Enhanced augmentation for training
        augmentation = EnhancedVideoAugmentation(
            temporal_jitter=self.config['data']['augmentation']['temporal_jitter'],
            time_warping=self.config['data']['augmentation']['time_warping'],
            temporal_probability=self.config['data']['augmentation']['temporal_probability'],
            random_crops=self.config['data']['augmentation']['random_crops'],
            random_translations=self.config['data']['augmentation']['random_translations'],
            photometric_jitter=self.config['data']['augmentation']['photometric_jitter'],
            gaussian_blur_sigma=tuple(self.config['data']['augmentation']['gaussian_blur_sigma']),
            gaussian_blur_prob=self.config['data']['augmentation']['gaussian_blur_prob'],
            horizontal_flip=self.config['data']['augmentation']['horizontal_flip']
        )
        
        # Split data
        train_ratio = self.config['data']['train_ratio']
        val_ratio = self.config['data']['val_ratio']
        
        # Simple random split for small dataset
        np.random.seed(self.config['data']['random_seed'])
        indices = np.random.permutation(len(manifest_df))
        
        train_size = int(len(manifest_df) * train_ratio)
        val_size = int(len(manifest_df) * val_ratio)
        
        train_indices = indices[:train_size]
        val_indices = indices[train_size:train_size + val_size]
        test_indices = indices[train_size + val_size:]
        
        train_df = manifest_df.iloc[train_indices].reset_index(drop=True)
        val_df = manifest_df.iloc[val_indices].reset_index(drop=True)
        test_df = manifest_df.iloc[test_indices].reset_index(drop=True)
        
        print(f"📊 Data splits: Train={len(train_df)}, Val={len(val_df)}, Test={len(test_df)}")
        
        # Create datasets
        train_dataset = EnhancedPerfect10Dataset(train_df, video_processor, augmentation, is_training=True)
        val_dataset = EnhancedPerfect10Dataset(val_df, video_processor, None, is_training=False)
        test_dataset = EnhancedPerfect10Dataset(test_df, video_processor, None, is_training=False)
        
        # Enhanced class balancing for training
        if self.config['training']['class_balancing']['enabled']:
            train_labels = [train_dataset.phrase_to_idx[row['phrase']] for _, row in train_df.iterrows()]
            weighted_sampler = WeightedPhrasesSampler(train_labels, num_classes=10)
            train_sampler = weighted_sampler.get_sampler()
            shuffle = False
        else:
            train_sampler = None
            shuffle = True
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.config['training']['batch_size'],
            sampler=train_sampler,
            shuffle=shuffle,
            num_workers=self.config['hardware']['num_workers'],
            pin_memory=self.config['hardware']['pin_memory']
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=False,
            num_workers=self.config['hardware']['num_workers'],
            pin_memory=self.config['hardware']['pin_memory']
        )
        
        self.test_loader = DataLoader(
            test_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=False,
            num_workers=self.config['hardware']['num_workers'],
            pin_memory=self.config['hardware']['pin_memory']
        )
        
        print(f"✅ Enhanced data loaders created")
        print(f"   Class balancing: {'Enabled' if train_sampler else 'Disabled'}")
    
    def setup_model(self):
        """Setup enhanced model with transfer learning"""
        
        print(f"\n🤖 Setting up Enhanced Model")
        print("=" * 30)
        
        # Create Perfect 10 model with transfer learning
        baseline_path = self.config['model']['pretrained_baseline_path']
        self.model = Perfect10Mobile3DTiny(
            num_classes=self.config['model']['num_classes'],
            pretrained_26_class_path=baseline_path if Path(baseline_path).exists() else None
        )
        
        self.model.to(self.device)
        
        print(f"✅ Model setup complete")
        print(f"   Parameters: {self.model.get_num_parameters():,}")
        print(f"   Transfer learning: {self.config['model']['transfer_learning']}")
    
    def setup_training_components(self):
        """Setup optimizer, scheduler, and loss function"""
        
        print(f"\n⚙️  Setting up Training Components")
        print("=" * 35)
        
        # Optimizer
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config['training']['learning_rate'],
            weight_decay=self.config['training']['weight_decay']
        )
        
        # Scheduler
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=self.config['training']['num_epochs']
        )
        
        # Loss function with label smoothing
        self.criterion = nn.CrossEntropyLoss(
            label_smoothing=self.config['regularization'].get('label_smoothing', 0.0)
        )
        
        print(f"✅ Training components ready")
        print(f"   Optimizer: AdamW (lr={self.config['training']['learning_rate']})")
        print(f"   Scheduler: CosineAnnealingLR")
        print(f"   Loss: CrossEntropyLoss (label_smoothing={self.config['regularization'].get('label_smoothing', 0.0)})")
    
    def train_epoch(self, epoch: int) -> Tuple[float, float]:
        """Train one epoch"""
        
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        pbar = tqdm(self.train_loader, desc=f"Epoch {epoch+1}")
        
        for batch_idx, (videos, labels) in enumerate(pbar):
            videos, labels = videos.to(self.device), labels.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(videos)
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            if self.config['training'].get('grad_clip_norm'):
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config['training']['grad_clip_norm']
                )
            
            self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'Acc': f"{100.*correct/total:.2f}%"
            })
        
        epoch_loss = total_loss / len(self.train_loader)
        epoch_acc = correct / total
        
        return epoch_loss, epoch_acc
    
    def validate(self) -> Tuple[float, float]:
        """Validate model"""
        
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for videos, labels in self.val_loader:
                videos, labels = videos.to(self.device), labels.to(self.device)
                
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                
                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()
        
        val_loss = total_loss / len(self.val_loader)
        val_acc = correct / total
        
        return val_loss, val_acc
    
    def save_checkpoint(self, epoch: int, val_accuracy: float, is_best: bool = False):
        """Save model checkpoint"""
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_accuracy': self.best_val_accuracy,
            'training_history': self.training_history,
            'config': self.config
        }
        
        # Save regular checkpoint
        checkpoint_dir = Path(self.config['checkpoints']['save_dir'])
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        checkpoint_path = checkpoint_dir / f"checkpoint_epoch_{epoch+1}.pth"
        torch.save(checkpoint, checkpoint_path)
        
        # Save best model
        if is_best:
            best_path = checkpoint_dir / "best_enhanced_perfect_10_model.pth"
            torch.save(checkpoint, best_path)
            self.best_model_path = best_path
            print(f"💾 New best model saved: {val_accuracy:.1%} accuracy")
    
    def train(self):
        """Execute enhanced training"""
        
        print(f"\n🚀 Starting Enhanced Perfect 10 Training")
        print("=" * 45)
        print(f"Target: >90% accuracy with enhanced preprocessing")
        
        for epoch in range(self.config['training']['num_epochs']):
            # Train epoch
            train_loss, train_acc = self.train_epoch(epoch)
            
            # Validate
            val_loss, val_acc = self.validate()
            
            # Update scheduler
            self.scheduler.step()
            
            # Track history
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            self.training_history['learning_rates'].append(self.optimizer.param_groups[0]['lr'])
            
            # Check for best model
            is_best = val_acc > self.best_val_accuracy
            if is_best:
                self.best_val_accuracy = val_acc
            
            # Save checkpoint
            if (epoch + 1) % self.config['training'].get('save_every_n_epochs', 10) == 0 or is_best:
                self.save_checkpoint(epoch, val_acc, is_best)
            
            # Print progress
            print(f"Epoch {epoch+1:3d}: Train Loss={train_loss:.4f}, Train Acc={train_acc:.1%}, "
                  f"Val Loss={val_loss:.4f}, Val Acc={val_acc:.1%}, LR={self.optimizer.param_groups[0]['lr']:.6f}")
            
            # Early stopping check
            if val_acc >= 0.90:  # Target achieved
                print(f"🎉 Target accuracy achieved: {val_acc:.1%}")
                self.save_checkpoint(epoch, val_acc, True)
                break
        
        print(f"\n🎉 Enhanced Training Complete!")
        print(f"   Best validation accuracy: {self.best_val_accuracy:.1%}")
        print(f"   Best model saved: {self.best_model_path}")

def main():
    """Main training function"""
    
    print("🎯 Enhanced Perfect 10 Training for >90% Accuracy")
    print("=" * 55)
    
    # Configuration
    config_path = "configs/perfect_10_training.yaml"
    
    if not Path(config_path).exists():
        print(f"❌ Config not found: {config_path}")
        return
    
    # Create trainer
    trainer = EnhancedPerfect10Trainer(config_path)
    
    # Setup components
    trainer.setup_data_loaders()
    trainer.setup_model()
    trainer.setup_training_components()
    
    # Execute training
    trainer.train()
    
    print(f"\n🚀 Enhanced Perfect 10 Training Ready!")

if __name__ == '__main__':
    main()
