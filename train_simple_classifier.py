#!/usr/bin/env python3
"""
Simplified training script for ICU lipreading with small dataset
"""

import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from pathlib import Path
import yaml
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import sys
import os

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor


class SimpleICUDataset(Dataset):
    """Simplified dataset for small data training"""
    
    def __init__(self, video_paths, labels, processor):
        self.video_paths = video_paths
        self.labels = labels
        self.processor = processor
        
    def __len__(self):
        return len(self.video_paths)
    
    def __getitem__(self, idx):
        video_path = self.video_paths[idx]
        label = self.labels[idx]
        
        try:
            # Process video
            video = self.processor.process_video(video_path)
            
            # Ensure correct shape (1, 32, 96, 96)
            if video.shape != (1, 32, 96, 96):
                print(f"Warning: Video shape {video.shape} for {video_path}")
                video = torch.zeros(1, 32, 96, 96)
                
        except Exception as e:
            print(f"Error processing {video_path}: {e}")
            video = torch.zeros(1, 32, 96, 96)
            
        return video, label


class SimpleCNN(nn.Module):
    """Simplified CNN for small dataset"""
    
    def __init__(self, num_classes):
        super().__init__()
        
        # Simple 3D CNN
        self.conv3d = nn.Sequential(
            nn.Conv3d(1, 16, kernel_size=(3, 3, 3), padding=1),
            nn.ReLU(),
            nn.MaxPool3d((2, 2, 2)),
            
            nn.Conv3d(16, 32, kernel_size=(3, 3, 3), padding=1),
            nn.ReLU(),
            nn.MaxPool3d((2, 2, 2)),
            
            nn.Conv3d(32, 64, kernel_size=(3, 3, 3), padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool3d((1, 1, 1))
        )
        
        # Classifier
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, num_classes)
        )
        
    def forward(self, x):
        # x shape: (batch, 1, 32, 96, 96)
        x = self.conv3d(x)
        x = x.view(x.size(0), -1)  # Flatten
        x = self.classifier(x)
        return x


def load_data():
    """Load and prepare data"""

    # Load manifest
    df = pd.read_csv('data/manifest.csv')
    print(f"Loaded {len(df)} videos")

    # Get phrase distribution
    phrase_counts = df['phrase'].value_counts()
    print(f"\nPhrase distribution:")
    for phrase, count in phrase_counts.items():
        print(f"  {phrase}: {count}")

    # For this small dataset, let's focus on top phrases with most data
    # Take top 8 phrases to have enough samples per class
    top_phrases = phrase_counts.head(8).index.tolist()
    df_filtered = df[df['phrase'].isin(top_phrases)].copy()

    print(f"\nUsing top {len(top_phrases)} phrases:")
    for phrase in top_phrases:
        count = phrase_counts[phrase]
        print(f"  {phrase}: {count} videos")

    print(f"Total videos after filtering: {len(df_filtered)}")

    # Create label mapping
    phrase_to_idx = {phrase: idx for idx, phrase in enumerate(sorted(top_phrases))}
    df_filtered['label'] = df_filtered['phrase'].map(phrase_to_idx)

    return df_filtered, phrase_to_idx


def create_splits(df, test_size=0.25, val_size=0.25):
    """Create train/val/test splits"""

    # For small dataset, use simple random split without stratification
    # First split: train+val vs test
    train_val_df, test_df = train_test_split(
        df, test_size=test_size, random_state=42
    )

    # Second split: train vs val
    if len(train_val_df) > 1:
        train_df, val_df = train_test_split(
            train_val_df, test_size=val_size/(1-test_size), random_state=42
        )
    else:
        train_df = train_val_df
        val_df = train_val_df.copy()

    print(f"\nData splits:")
    print(f"  Train: {len(train_df)} videos")
    print(f"  Val: {len(val_df)} videos")
    print(f"  Test: {len(test_df)} videos")

    return train_df, val_df, test_df


def train_model():
    """Main training function"""
    
    print("🚀 Starting simplified ICU lipreading training")
    print("=" * 50)
    
    # Load data
    df, phrase_to_idx = load_data()
    num_classes = len(phrase_to_idx)
    
    if num_classes < 2:
        print("❌ Need at least 2 classes for training")
        return
    
    # Create splits
    train_df, val_df, test_df = create_splits(df)
    
    # Create video processor
    processor = VideoProcessor(
        target_frames=32,
        target_size=(96, 96),
        grayscale=True
    )
    
    # Create datasets
    train_dataset = SimpleICUDataset(
        train_df['video_path'].tolist(),
        train_df['label'].tolist(),
        processor
    )
    
    val_dataset = SimpleICUDataset(
        val_df['video_path'].tolist(),
        val_df['label'].tolist(),
        processor
    )
    
    test_dataset = SimpleICUDataset(
        test_df['video_path'].tolist(),
        test_df['label'].tolist(),
        processor
    )
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True, num_workers=0)
    val_loader = DataLoader(val_dataset, batch_size=2, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=2, shuffle=False, num_workers=0)
    
    # Create model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleCNN(num_classes).to(device)
    
    # Count parameters
    param_count = sum(p.numel() for p in model.parameters())
    print(f"\nModel parameters: {param_count:,}")
    
    # Training setup
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    # Training loop
    epochs = 20
    best_val_acc = 0.0
    
    print(f"\nStarting training for {epochs} epochs...")
    
    for epoch in range(epochs):
        # Train
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for videos, labels in train_loader:
            videos, labels = videos.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(videos)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = outputs.max(1)
            train_total += labels.size(0)
            train_correct += predicted.eq(labels).sum().item()
        
        train_acc = 100. * train_correct / train_total if train_total > 0 else 0
        
        # Validate
        model.eval()
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for videos, labels in val_loader:
                videos, labels = videos.to(device), labels.to(device)
                outputs = model(videos)
                _, predicted = outputs.max(1)
                val_total += labels.size(0)
                val_correct += predicted.eq(labels).sum().item()
        
        val_acc = 100. * val_correct / val_total if val_total > 0 else 0
        
        print(f"Epoch {epoch+1:2d}/{epochs}: Train Acc: {train_acc:5.1f}%, Val Acc: {val_acc:5.1f}%")
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), 'artifacts/simple_model_best.pth')
    
    # Test evaluation
    model.load_state_dict(torch.load('artifacts/simple_model_best.pth'))
    model.eval()
    
    test_predictions = []
    test_labels = []
    
    with torch.no_grad():
        for videos, labels in test_loader:
            videos, labels = videos.to(device), labels.to(device)
            outputs = model(videos)
            _, predicted = outputs.max(1)
            
            test_predictions.extend(predicted.cpu().numpy())
            test_labels.extend(labels.cpu().numpy())
    
    # Calculate test accuracy
    test_acc = accuracy_score(test_labels, test_predictions) * 100
    
    print(f"\n🎯 Final Results:")
    print(f"   Best Val Accuracy: {best_val_acc:.1f}%")
    print(f"   Test Accuracy: {test_acc:.1f}%")
    print(f"   Model saved to: artifacts/simple_model_best.pth")
    
    # Print classification report
    idx_to_phrase = {idx: phrase for phrase, idx in phrase_to_idx.items()}
    target_names = [idx_to_phrase[i] for i in range(num_classes)]
    
    print(f"\nClassification Report:")
    print(classification_report(test_labels, test_predictions, target_names=target_names))


if __name__ == '__main__':
    # Create artifacts directory
    Path('artifacts').mkdir(exist_ok=True)
    
    train_model()
