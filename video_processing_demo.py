#!/usr/bin/env python3
"""
Demonstration of video processing pipeline
Shows how reference videos are cropped, resized, and preprocessed
"""

import cv2
import numpy as np
import torch
import matplotlib.pyplot as plt
from pathlib import Path
import sys
import yaml

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

def analyze_original_video(video_path):
    """Analyze the original video properties"""
    
    print(f"🎥 Analyzing original video: {Path(video_path).name}")
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print("❌ Could not open video")
        return None
    
    # Get video properties
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = frame_count / fps if fps > 0 else 0
    
    print(f"📊 Original Video Properties:")
    print(f"   Resolution: {width}x{height}")
    print(f"   Frame rate: {fps:.2f} fps")
    print(f"   Frame count: {frame_count}")
    print(f"   Duration: {duration:.2f} seconds")
    
    # Read first few frames to show original content
    frames = []
    for i in range(min(5, frame_count)):
        ret, frame = cap.read()
        if ret:
            frames.append(frame)
        else:
            break
    
    cap.release()
    
    return {
        'width': width,
        'height': height,
        'fps': fps,
        'frame_count': frame_count,
        'duration': duration,
        'sample_frames': frames
    }

def demonstrate_processing_pipeline(video_path):
    """Demonstrate the complete processing pipeline"""
    
    print(f"\n🔧 Demonstrating Processing Pipeline")
    print("=" * 50)
    
    # Load configuration
    with open('configs/phrases26.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Create video processor
    processor = VideoProcessor(
        target_frames=config.get('frames', 32),
        target_size=(config.get('height', 96), config.get('width', 96)),
        grayscale=config.get('grayscale', True)
    )
    
    print(f"📋 Processing Configuration:")
    print(f"   Target frames: {config.get('frames', 32)}")
    print(f"   Target size: {config.get('height', 96)}x{config.get('width', 96)}")
    print(f"   Grayscale: {config.get('grayscale', True)}")
    
    # Process the video
    print(f"\n🔄 Processing video...")
    processed_tensor = processor.process_video(video_path)
    
    print(f"✅ Processing complete!")
    print(f"   Output shape: {processed_tensor.shape}")
    print(f"   Data type: {processed_tensor.dtype}")
    print(f"   Value range: [{processed_tensor.min():.3f}, {processed_tensor.max():.3f}]")
    
    return processed_tensor

def show_processing_steps(video_path, output_dir="video_processing_demo"):
    """Show detailed processing steps with visual outputs"""
    
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    print(f"\n📸 Creating Visual Processing Demo")
    print("=" * 50)
    
    # Step 1: Load original video
    cap = cv2.VideoCapture(video_path)
    original_frames = []
    
    # Read all frames
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        original_frames.append(frame)
    cap.release()
    
    print(f"📥 Loaded {len(original_frames)} original frames")
    
    # Step 2: Show processing steps
    with open('configs/phrases26.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    processor = VideoProcessor(
        target_frames=32,
        target_size=(96, 96),
        grayscale=True
    )
    
    # Process with intermediate steps
    print(f"🔄 Processing with intermediate visualization...")
    
    # Convert to tensor format
    frames_array = np.array(original_frames)
    frames_tensor = torch.from_numpy(frames_array).permute(0, 3, 1, 2).float() / 255.0
    
    print(f"   Original tensor shape: {frames_tensor.shape}")
    
    # Step 3: Resize frames
    import torch.nn.functional as F
    
    # Resize to target size
    resized_frames = F.interpolate(
        frames_tensor, 
        size=(96, 96), 
        mode='bilinear', 
        align_corners=False
    )
    print(f"   After resize: {resized_frames.shape}")
    
    # Step 4: Convert to grayscale
    if resized_frames.shape[1] == 3:  # RGB
        # Convert to grayscale using standard weights
        gray_frames = 0.299 * resized_frames[:, 0:1] + 0.587 * resized_frames[:, 1:2] + 0.114 * resized_frames[:, 2:3]
        print(f"   After grayscale: {gray_frames.shape}")
    else:
        gray_frames = resized_frames
    
    # Step 5: Temporal cropping/padding to exactly 32 frames
    current_frames = gray_frames.shape[0]
    target_frames = 32
    
    if current_frames > target_frames:
        # Crop from center
        start_idx = (current_frames - target_frames) // 2
        final_frames = gray_frames[start_idx:start_idx + target_frames]
        print(f"   After temporal crop: {final_frames.shape}")
    elif current_frames < target_frames:
        # Pad by repeating last frame
        padding_needed = target_frames - current_frames
        last_frame = gray_frames[-1:].repeat(padding_needed, 1, 1, 1)
        final_frames = torch.cat([gray_frames, last_frame], dim=0)
        print(f"   After temporal padding: {final_frames.shape}")
    else:
        final_frames = gray_frames
        print(f"   No temporal adjustment needed: {final_frames.shape}")
    
    # Step 6: Normalize
    final_frames = (final_frames - 0.5) / 0.5  # Normalize to [-1, 1]
    print(f"   After normalization: range [{final_frames.min():.3f}, {final_frames.max():.3f}]")
    
    # Rearrange to (C, T, H, W) format
    final_tensor = final_frames.permute(1, 0, 2, 3)
    print(f"   Final shape (C,T,H,W): {final_tensor.shape}")
    
    # Create visualization
    create_processing_visualization(
        original_frames, 
        final_tensor, 
        output_dir,
        video_path
    )
    
    return final_tensor

def create_processing_visualization(original_frames, processed_tensor, output_dir, video_path):
    """Create side-by-side visualization of processing steps"""
    
    print(f"🎨 Creating visualization...")
    
    # Select frames to show (every 8th frame for good coverage)
    frame_indices = list(range(0, len(original_frames), max(1, len(original_frames) // 8)))[:8]
    processed_indices = list(range(0, 32, 4))[:8]  # Every 4th processed frame
    
    fig, axes = plt.subplots(2, 8, figsize=(20, 6))
    fig.suptitle(f'Video Processing Pipeline: {Path(video_path).name}', fontsize=16)
    
    # Top row: Original frames
    for i, frame_idx in enumerate(frame_indices):
        if frame_idx < len(original_frames):
            frame = original_frames[frame_idx]
            # Convert BGR to RGB for matplotlib
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            axes[0, i].imshow(frame_rgb)
            axes[0, i].set_title(f'Original\nFrame {frame_idx}')
            axes[0, i].axis('off')
    
    # Bottom row: Processed frames
    for i, proc_idx in enumerate(processed_indices):
        if proc_idx < processed_tensor.shape[1]:
            # Get processed frame (C, T, H, W) -> (H, W)
            proc_frame = processed_tensor[0, proc_idx].numpy()
            # Denormalize for display
            proc_frame = (proc_frame + 1) / 2  # [-1,1] -> [0,1]
            axes[1, i].imshow(proc_frame, cmap='gray')
            axes[1, i].set_title(f'Processed\nFrame {proc_idx}')
            axes[1, i].axis('off')
    
    plt.tight_layout()
    
    # Save visualization
    viz_path = output_dir / f"processing_demo_{Path(video_path).stem}.png"
    plt.savefig(viz_path, dpi=150, bbox_inches='tight')
    print(f"💾 Visualization saved: {viz_path}")
    
    # Also save individual comparison
    create_detailed_comparison(original_frames, processed_tensor, output_dir, video_path)
    
    plt.close()

def create_detailed_comparison(original_frames, processed_tensor, output_dir, video_path):
    """Create detailed before/after comparison"""
    
    # Select middle frame for detailed comparison
    mid_original = len(original_frames) // 2
    mid_processed = 16  # Middle of 32 frames
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Original frame
    if mid_original < len(original_frames):
        orig_frame = cv2.cvtColor(original_frames[mid_original], cv2.COLOR_BGR2RGB)
        axes[0].imshow(orig_frame)
        axes[0].set_title(f'Original Frame\n{orig_frame.shape[1]}x{orig_frame.shape[0]} pixels')
        axes[0].axis('off')
    
    # Processed frame
    proc_frame = processed_tensor[0, mid_processed].numpy()
    proc_frame_display = (proc_frame + 1) / 2  # Denormalize for display
    axes[1].imshow(proc_frame_display, cmap='gray')
    axes[1].set_title(f'Processed Frame\n96x96 pixels, Grayscale')
    axes[1].axis('off')
    
    # Difference/overlay
    if mid_original < len(original_frames):
        # Resize original to match processed for comparison
        orig_resized = cv2.resize(original_frames[mid_original], (96, 96))
        orig_gray = cv2.cvtColor(orig_resized, cv2.COLOR_BGR2GRAY) / 255.0
        
        # Show difference
        diff = np.abs(orig_gray - proc_frame_display)
        axes[2].imshow(diff, cmap='hot')
        axes[2].set_title(f'Difference Map\n(Bright = More Different)')
        axes[2].axis('off')
    
    plt.suptitle(f'Detailed Processing Comparison: {Path(video_path).stem}', fontsize=14)
    plt.tight_layout()
    
    # Save detailed comparison
    detail_path = output_dir / f"detailed_comparison_{Path(video_path).stem}.png"
    plt.savefig(detail_path, dpi=150, bbox_inches='tight')
    print(f"💾 Detailed comparison saved: {detail_path}")
    
    plt.close()

def main():
    """Main demonstration function"""
    
    print("🎬 ICU Lipreading Video Processing Demonstration")
    print("=" * 60)
    
    # Use a reference video for demonstration
    demo_video = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    
    if not Path(demo_video).exists():
        print(f"❌ Demo video not found: {demo_video}")
        print("Please check the path to the reference videos.")
        return
    
    # Step 1: Analyze original video
    original_info = analyze_original_video(demo_video)
    
    if not original_info:
        return
    
    # Step 2: Demonstrate processing pipeline
    processed_tensor = demonstrate_processing_pipeline(demo_video)
    
    # Step 3: Show detailed processing steps with visualization
    final_tensor = show_processing_steps(demo_video)
    
    # Step 4: Verify consistency
    print(f"\n✅ Verification:")
    print(f"   Pipeline output shape: {processed_tensor.shape}")
    print(f"   Step-by-step output shape: {final_tensor.shape}")
    print(f"   Shapes match: {processed_tensor.shape == final_tensor.shape}")
    
    # Show summary
    print(f"\n📋 Processing Summary:")
    print(f"   Original: {original_info['width']}x{original_info['height']}, {original_info['frame_count']} frames")
    print(f"   Processed: 96x96, 32 frames, grayscale")
    print(f"   Compression ratio: {(original_info['width'] * original_info['height'] * original_info['frame_count']) / (96 * 96 * 32):.1f}x")
    print(f"   Time compression: {original_info['frame_count'] / 32:.1f}x")
    
    print(f"\n🎯 Processing complete! Check 'video_processing_demo/' for visualizations.")

if __name__ == '__main__':
    main()
