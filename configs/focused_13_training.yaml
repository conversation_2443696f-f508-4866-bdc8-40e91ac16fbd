# Configuration for focused 13-phrase ICU lipreading classifier training
# Optimized for high-performing phrases with transfer learning

# Model configuration
model:
  name: "FocusedMobile3DTiny"
  num_classes: 13
  pretrained_baseline_path: "checkpoints/reference_training/best_model.pth"
  transfer_learning: true

# Data configuration
data:
  manifest_path: "focused_13_phrases_manifest.csv"
  video_root: "/"
  
  # Video processing parameters (same as baseline)
  frames: 32
  height: 96
  width: 96
  grayscale: true
  
  # Data splits for focused dataset (78 videos total)
  train_ratio: 0.75   # 58-59 videos for training
  val_ratio: 0.15     # 11-12 videos for validation  
  test_ratio: 0.10    # 7-8 videos for testing
  
  # Split strategy
  split_by_speaker: false
  stratify_by_phrase: true  # Better stratification with 6 videos per phrase
  random_seed: 42
  
  # Data augmentation (enhanced for focused training)
  augmentation:
    enabled: true
    horizontal_flip: 0.4      # Increased for more variation
    temporal_crop: 0.3        # More temporal variation
    noise_std: 0.015          # Slightly more noise
    brightness_factor: 0.15   # More brightness variation
    rotation_degrees: 2       # Small rotation augmentation

# Training configuration (optimized for transfer learning)
training:
  batch_size: 6             # Larger batch size with more data
  num_epochs: 80            # Fewer epochs due to transfer learning
  learning_rate: 0.00005    # Lower LR for fine-tuning
  weight_decay: 0.005       # Reduced weight decay
  
  # Transfer learning strategy
  freeze_backbone_epochs: 10  # Freeze backbone for first 10 epochs
  fine_tune_lr: 0.00001      # Even lower LR for fine-tuning phase
  
  # Optimizer
  optimizer: "AdamW"
  scheduler: "CosineAnnealingLR"
  warmup_epochs: 5           # Shorter warmup
  
  # Early stopping (more aggressive)
  early_stopping:
    enabled: true
    patience: 12             # Shorter patience
    min_delta: 0.005         # Larger minimum improvement
  
  # Gradient clipping
  grad_clip_norm: 1.0
  
  # Validation frequency
  val_every_n_epochs: 2
  
  # Checkpointing
  save_every_n_epochs: 10
  save_best_model: true

# Regularization (adjusted for focused training)
regularization:
  dropout: 0.25              # Reduced dropout
  label_smoothing: 0.05      # Reduced label smoothing
  mixup_alpha: 0.15          # Reduced mixup

# Logging and monitoring
logging:
  log_dir: "logs/focused_13_training"
  tensorboard: true
  wandb: false
  log_every_n_steps: 3
  
# Checkpoints and model saving
checkpoints:
  save_dir: "checkpoints/focused_13_training"
  save_top_k: 3
  monitor: "val_accuracy"
  mode: "max"

# Hardware configuration
hardware:
  device: "auto"
  num_workers: 2
  pin_memory: true

# Evaluation
evaluation:
  metrics: ["accuracy", "top3_accuracy", "f1_score", "confusion_matrix"]
  save_predictions: true
  save_attention_maps: false
  compare_with_baseline: true

# Target phrases (13 high-performing phrases)
phrases:
  - "am i getting better"
  - "i feel anxious"
  - "i m confused"
  - "i need to move"
  - "i need to sit up"
  - "i want to phone my family"
  - "what happened to me"
  - "what time is my wife coming"
  - "where am i"
  - "who is with me today"
  - "i have a headache"
  - "my back hurts"
  - "stay with me please"

# Baseline performance reference
baseline_performance:
  perfect_phrases:
    - "am i getting better"
    - "i feel anxious"
    - "i m confused"
    - "i need to move"
    - "i need to sit up"
    - "i want to phone my family"
    - "what happened to me"
    - "what time is my wife coming"
    - "where am i"
    - "who is with me today"
  strong_phrases:
    - "i have a headache"
    - "my back hurts"
    - "stay with me please"
  baseline_accuracy: 0.512    # 51.2% on 26-class problem
  target_accuracy: 0.75      # Target 75% on 13-class problem

# Experiment tracking
experiment:
  name: "focused_13_phrase_training"
  description: "Transfer learning on 13 high-performing ICU phrases"
  tags: ["focused", "transfer_learning", "13_phrases", "high_performance"]
  baseline_model: "checkpoints/reference_training/best_model.pth"
  
# Training phases
training_phases:
  phase1:
    name: "frozen_backbone"
    epochs: 10
    description: "Train only classifier with frozen backbone"
    freeze_backbone: true
    learning_rate: 0.00005
  
  phase2:
    name: "fine_tuning"
    epochs: 70
    description: "Fine-tune entire model with lower learning rate"
    freeze_backbone: false
    learning_rate: 0.00001

# Expected improvements over baseline
expected_improvements:
  accuracy_gain: "+20-25%"    # From 51.2% to 70-75%
  confidence_improvement: "+15-20%"
  training_speed: "2x faster convergence"
  deployment_readiness: "Production ready for 13 phrases"

# Reproducibility
seed: 42
