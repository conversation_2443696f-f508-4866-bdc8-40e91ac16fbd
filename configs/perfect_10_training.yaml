# Configuration for Perfect 10 ICU lipreading classifier training
# Ultra-focused on 10 phrases with 100% baseline accuracy

# Model configuration
model:
  name: "Perfect10Mobile3DTiny"
  num_classes: 10
  pretrained_baseline_path: "checkpoints/reference_training/best_model.pth"
  transfer_learning: true

# Data configuration
data:
  manifest_path: "perfect_10_phrases_manifest.csv"
  video_root: "/"

  # Enhanced video processing parameters
  frames: 64        # Enhanced: 64 frames instead of 32
  height: 112       # Enhanced: 112×112 instead of 96×96
  width: 112
  grayscale: true
  fps: 25.0         # Enhanced: Standardized to 25 FPS
  use_dataset_normalization: true  # Enhanced: z-score normalization
  
  # Data splits for perfect 10 dataset (30 videos total)
  train_ratio: 0.70   # 21 videos for training
  val_ratio: 0.20     # 6 videos for validation  
  test_ratio: 0.10    # 3 videos for testing
  
  # Split strategy
  split_by_speaker: false
  stratify_by_phrase: true  # Ensure each phrase represented
  random_seed: 42
  
  # Enhanced data augmentation pipeline
  augmentation:
    enabled: true

    # Temporal augmentations (training only)
    temporal_jitter: 6        # Enhanced: ±6 frames (±10% of 64-frame sequence)
    time_warping: 0.1         # Enhanced: 0.9×-1.1× playback speed variation
    temporal_probability: 0.5 # Apply temporal augmentations with 50% probability

    # Spatial augmentations (training only)
    random_crops: 0.06        # Enhanced: ±6% of 112×112 image size
    random_translations: 4    # Enhanced: ±4 pixels in x/y directions
    photometric_jitter: 0.15  # Enhanced: ±15% brightness/contrast
    gaussian_blur_sigma: [0.5, 1.0]  # Enhanced: Gaussian blur σ=0.5-1.0
    gaussian_blur_prob: 0.1   # Enhanced: Applied with 10% probability

    # Disabled augmentations (preserve lip reading directionality)
    horizontal_flip: false    # Enhanced: Disabled to preserve lip movements

    # Legacy augmentations (kept for compatibility)
    noise_std: 0.02
    rotation_degrees: 2       # Reduced for better lip preservation

# Enhanced training configuration
training:
  batch_size: 4             # Small batch for small dataset
  num_epochs: 60            # Fewer epochs due to focused dataset
  learning_rate: 0.0001     # Higher LR for faster convergence
  weight_decay: 0.001       # Minimal weight decay

  # Enhanced class balancing
  class_balancing:
    enabled: true           # Enhanced: WeightedRandomSampler for equal phrase representation
    strategy: "weighted_sampler"  # Use WeightedRandomSampler
    weight_calculation: "inverse_frequency"  # weight = 1.0 / class_frequency
  
  # Transfer learning strategy
  freeze_backbone_epochs: 8   # Shorter freeze period
  fine_tune_lr: 0.00005      # Higher fine-tuning LR
  
  # Optimizer
  optimizer: "AdamW"
  scheduler: "CosineAnnealingLR"
  warmup_epochs: 3           # Short warmup
  
  # Early stopping (aggressive for fast convergence)
  early_stopping:
    enabled: true
    patience: 8              # Short patience
    min_delta: 0.01          # Larger minimum improvement
  
  # Gradient clipping
  grad_clip_norm: 1.0
  
  # Validation frequency
  val_every_n_epochs: 2
  
  # Checkpointing
  save_every_n_epochs: 8
  save_best_model: true

# Enhanced regularization
regularization:
  dropout: 0.3               # Enhanced: Increased to 0.3 for better generalization
  label_smoothing: 0.02      # Very light label smoothing
  mixup_alpha: 0.1           # Light mixup

# Logging and monitoring
logging:
  log_dir: "logs/perfect_10_training"
  tensorboard: true
  wandb: false
  log_every_n_steps: 2
  
# Checkpoints and model saving
checkpoints:
  save_dir: "checkpoints/perfect_10_training"
  save_top_k: 3
  monitor: "val_accuracy"
  mode: "max"

# Hardware configuration
hardware:
  device: "auto"
  num_workers: 1  # Conservative for small dataset
  pin_memory: true

# Evaluation
evaluation:
  metrics: ["accuracy", "top3_accuracy", "f1_score", "confusion_matrix"]
  save_predictions: true
  save_attention_maps: false
  compare_with_baseline: true

# Perfect 10 phrases (100% baseline accuracy)
phrases:
  - "am i getting better"
  - "i feel anxious"
  - "i m confused"
  - "i need to move"
  - "i need to sit up"
  - "i want to phone my family"
  - "what happened to me"
  - "what time is my wife coming"
  - "where am i"
  - "who is with me today"

# Baseline performance reference
baseline_performance:
  perfect_phrases_accuracy: 1.0    # 100% on these specific phrases
  overall_baseline_accuracy: 0.512  # 51.2% on 26-class problem
  target_accuracy: 0.90            # Target 90%+ on 10-class problem
  confidence_target: 0.95          # Target 95%+ confidence

# Experiment tracking
experiment:
  name: "perfect_10_phrase_training"
  description: "Ultra-focused training on 10 phrases with 100% baseline accuracy"
  tags: ["perfect_10", "transfer_learning", "ultra_focused", "maximum_accuracy"]
  baseline_model: "checkpoints/reference_training/best_model.pth"
  
# Training phases (shorter for focused training)
training_phases:
  phase1:
    name: "frozen_backbone"
    epochs: 8
    description: "Train only classifier with frozen backbone"
    freeze_backbone: true
    learning_rate: 0.0001
  
  phase2:
    name: "fine_tuning"
    epochs: 52
    description: "Fine-tune entire model with lower learning rate"
    freeze_backbone: false
    learning_rate: 0.00005

# Expected improvements over baseline
expected_improvements:
  accuracy_gain: "+40-45%"    # From 51.2% to 90-95%
  confidence_improvement: "+30-40%"
  training_speed: "3x faster convergence"
  deployment_readiness: "Maximum confidence for 10 phrases"
  real_world_impact: "Highly reliable ICU communication"

# Perfect 10 advantages
perfect_10_advantages:
  maximum_accuracy: "Focus on learnable phrases only"
  fastest_training: "Minimal data, maximum transfer learning"
  highest_confidence: "Model excels at these specific phrases"
  deployment_ready: "Production confidence for critical ICU phrases"
  user_trust: "Consistent high performance builds user confidence"

# Reproducibility
seed: 42
