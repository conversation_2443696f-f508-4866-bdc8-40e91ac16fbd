# ICU Lipreading - 26 Phrase Configuration
# Lightweight VSR system configuration for commercial deployment

phrases:
  - "where am i"
  - "who is with me today"
  - "what happened to me"
  - "am i getting better"
  - "please explain again"
  - "where is my wife"
  - "where is my husband"
  - "i want to phone my family"
  - "i want to see my wife"
  - "i want to see my husband"
  - "what time is my wife coming"
  - "what time is my husband coming"
  - "i feel anxious"
  - "stay with me please"
  - "my chest hurts"
  - "my back hurts"
  - "i m confused"
  - "i m in pain"
  - "i have a headache"
  - "i m uncomfortable"
  - "i need a medication"
  - "i need to lie down"
  - "i need to use the toilet"
  - "i need to sit up"
  - "i need help"
  - "i need to move"

# Video processing parameters
frames: 32
height: 96
width: 96
grayscale: true

# Model parameters
confidence_threshold: 0.6

# Training parameters
batch_size: 16
learning_rate: 0.001
weight_decay: 0.01
epochs: 40

# Data splits (speaker-wise to prevent leakage)
val_split: 0.1
test_split: 0.1

# Augmentation parameters
brightness_contrast_range: 0.15  # ±15%
scale_range: 0.10               # ±10%
vertical_jitter: 8              # ±8 pixels
temporal_jitter: 4              # ±4 frames

# Model architecture
model:
  backbone: "mobile3d_tiny"
  hidden_dim: 256
  num_gru_layers: 2
  dropout: 0.2
  
# Loss function
loss:
  type: "weighted_cross_entropy"  # or "focal"
  class_weights: "auto"          # automatically computed from data
  
# Optimizer
optimizer:
  type: "adamw"
  lr: 0.001
  weight_decay: 0.01
  
# Scheduler
scheduler:
  type: "cosine"
  warmup_epochs: 2
  
# Mixed precision training
amp: true

# Inference
inference:
  batch_size: 1
  device: "auto"  # auto-detect GPU/CPU
