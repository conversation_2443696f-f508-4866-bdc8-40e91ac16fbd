model:
  name: Mobile3DTiny
  num_classes: 26
  pretrained: false
data:
  manifest_path: reference_videos_manifest_fixed.csv
  video_root: /
  frames: 32
  height: 96
  width: 96
  grayscale: true
  train_ratio: 0.75
  val_ratio: 0.15
  test_ratio: 0.10
  split_by_speaker: false
  stratify_by_phrase: false
  random_seed: 42
  augmentation:
    enabled: true
    horizontal_flip: 0.3
    temporal_crop: 0.2
    noise_std: 0.01
    brightness_factor: 0.1
training:
  batch_size: 4
  num_epochs: 100
  learning_rate: 0.0001
  weight_decay: 0.01
  optimizer: AdamW
  scheduler: CosineAnnealingLR
  warmup_epochs: 10
  early_stopping:
    enabled: true
    patience: 15
    min_delta: 0.001
  grad_clip_norm: 1.0
  val_every_n_epochs: 2
  save_every_n_epochs: 10
  save_best_model: true
regularization:
  dropout: 0.3
  label_smoothing: 0.1
  mixup_alpha: 0.2
logging:
  log_dir: logs/reference_training
  tensorboard: true
  wandb: false
  log_every_n_steps: 5
checkpoints:
  save_dir: checkpoints/reference_training
  save_top_k: 3
  monitor: val_accuracy
  mode: max
hardware:
  device: auto
  num_workers: 2
  pin_memory: true
evaluation:
  metrics:
  - accuracy
  - top3_accuracy
  - f1_score
  - confusion_matrix
  save_predictions: true
  save_attention_maps: false
phrases:
- where am i
- who is with me today
- what happened to me
- am i getting better
- please explain again
- where is my wife
- where is my husband
- i want to phone my family
- i want to see my wife
- i want to see my husband
- what time is my wife coming
- what time is my husband coming
- i feel anxious
- stay with me please
- my chest hurts
- my back hurts
- i m confused
- i m in pain
- i have a headache
- i m uncomfortable
- i need a medication
- i need to lie down
- i need to use the toilet
- i need to sit up
- i need help
- i need to move
experiment:
  name: reference_training_baseline
  description: Baseline training on 80 mouth-cropped reference videos
  tags:
  - reference
  - baseline
  - mouth_cropped
  - 26_phrases
seed: 42
