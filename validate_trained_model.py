#!/usr/bin/env python3
"""
Validate trained VSR model against reference videos
Run this tomorrow morning after training completes
"""

import sys
import os
sys.path.append('.')

import json
import torch
import numpy as np
from pathlib import Path
import pandas as pd
import yaml
from collections import defaultdict

from backend.lightweight_vsr.utils_video import VideoProcessor
from backend.lightweight_vsr.model import Mobile3DTiny


def load_trained_model(checkpoint_path, config_path):
    """Load trained model from checkpoint"""
    
    # Load config
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Create model
    model = Mobile3DTiny(
        num_classes=len(config['phrases']),
        hidden_dim=config.get('model', {}).get('hidden_dim', 256),
        num_gru_layers=config.get('model', {}).get('num_gru_layers', 2),
        dropout=config.get('model', {}).get('dropout', 0.2)
    )
    
    # Load weights
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, config, checkpoint


def validate_against_references(model, config, reference_dir="icu-videos-today"):
    """Validate trained model against reference videos"""
    
    print("🧪 VALIDATING TRAINED MODEL AGAINST REFERENCE VIDEOS")
    print("="*60)
    
    # Setup
    phrases = config['phrases']
    phrase_to_idx = {phrase: idx for idx, phrase in enumerate(phrases)}
    
    processor = VideoProcessor(
        target_frames=config.get('frames', 32),
        target_size=(config.get('height', 96), config.get('width', 96)),
        grayscale=config.get('grayscale', True)
    )
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # Find reference videos
    reference_path = Path.home() / "Desktop" / reference_dir
    if not reference_path.exists():
        print(f"❌ Reference directory not found: {reference_path}")
        return None
    
    video_extensions = ['.mp4', '.webm', '.avi', '.mov', '.MP4', '.MOV']
    reference_videos = []
    for ext in video_extensions:
        reference_videos.extend(reference_path.glob(f'*{ext}'))
    
    print(f"Found {len(reference_videos)} reference videos")
    
    # Process each video
    results = []
    phrase_results = defaultdict(list)
    
    for video_path in reference_videos:
        try:
            # Extract expected phrase from filename
            expected_phrase = extract_phrase_from_filename(video_path.stem.lower(), phrases)
            
            # Process video
            video_tensor = processor.process_video(video_path)
            video_batch = video_tensor.unsqueeze(0).to(device)
            
            # Run inference
            with torch.no_grad():
                logits = model(video_batch)
                probabilities = torch.softmax(logits, dim=1)
            
            probs = probabilities.cpu().numpy()[0]
            top_indices = np.argsort(probs)[::-1]
            
            # Get prediction
            predicted_phrase = phrases[top_indices[0]]
            confidence = float(probs[top_indices[0]])
            
            # Check correctness
            is_correct = predicted_phrase == expected_phrase if expected_phrase else None
            
            result = {
                'video_name': video_path.name,
                'expected_phrase': expected_phrase,
                'predicted_phrase': predicted_phrase,
                'confidence': confidence,
                'is_correct': is_correct,
                'top3': [(phrases[idx], float(probs[idx])) for idx in top_indices[:3]]
            }
            
            results.append(result)
            
            if expected_phrase:
                phrase_results[expected_phrase].append(result)
            
            # Print result
            status = "✅" if is_correct else "❌" if is_correct is False else "❓"
            print(f"{status} {video_path.name}")
            print(f"   Expected: '{expected_phrase}'")
            print(f"   Predicted: '{predicted_phrase}' ({confidence:.3f})")
            
        except Exception as e:
            print(f"❌ Error processing {video_path.name}: {e}")
    
    return results, phrase_results


def extract_phrase_from_filename(filename, phrases):
    """Extract expected phrase from filename"""
    
    filename = filename.lower().replace('_', ' ').replace('-', ' ')
    
    # Try exact matches first
    for phrase in phrases:
        phrase_clean = phrase.lower()
        if phrase_clean in filename:
            return phrase
        
        # Try without punctuation
        phrase_no_punct = phrase_clean.replace("'", "").replace(",", "").replace(".", "")
        if phrase_no_punct in filename:
            return phrase
    
    # Try key word matching
    for phrase in phrases:
        phrase_words = phrase.lower().split()
        key_words = [w for w in phrase_words if len(w) > 2]
        if len(key_words) >= 2 and all(word in filename for word in key_words[:3]):
            return phrase
    
    return None


def generate_final_report(results, phrase_results, training_metrics_path):
    """Generate final validation report"""
    
    print("\n📊 GENERATING FINAL VALIDATION REPORT")
    print("="*50)
    
    # Load training metrics
    training_metrics = {}
    if Path(training_metrics_path).exists():
        with open(training_metrics_path, 'r') as f:
            training_metrics = json.load(f)
    
    # Calculate validation metrics
    total_videos = len(results)
    correct_predictions = sum(1 for r in results if r['is_correct'] is True)
    unknown_expected = sum(1 for r in results if r['expected_phrase'] is None)
    testable_videos = total_videos - unknown_expected
    
    accuracy = correct_predictions / testable_videos if testable_videos > 0 else 0
    avg_confidence = np.mean([r['confidence'] for r in results])
    
    # Per-phrase accuracy
    phrase_accuracy = {}
    for phrase, phrase_results_list in phrase_results.items():
        correct = sum(1 for r in phrase_results_list if r['is_correct'])
        phrase_accuracy[phrase] = correct / len(phrase_results_list) if phrase_results_list else 0
    
    # Create comprehensive report
    report = {
        'validation_summary': {
            'total_reference_videos': total_videos,
            'testable_videos': testable_videos,
            'correct_predictions': correct_predictions,
            'reference_accuracy': accuracy,
            'avg_confidence': avg_confidence,
            'meets_reference_target': accuracy >= 0.85
        },
        'training_summary': training_metrics.get('overall', {}),
        'phrase_accuracy': phrase_accuracy,
        'detailed_results': results,
        'readiness_assessment': {
            'training_complete': bool(training_metrics),
            'reference_validation_complete': True,
            'meets_accuracy_target': training_metrics.get('overall', {}).get('macro_f1', 0) >= 0.90,
            'meets_reference_target': accuracy >= 0.85,
            'ready_for_production': False  # Will be updated below
        }
    }
    
    # Determine production readiness
    training_f1 = training_metrics.get('overall', {}).get('macro_f1', 0)
    report['readiness_assessment']['ready_for_production'] = (
        training_f1 >= 0.90 and accuracy >= 0.85
    )
    
    # Save report
    with open('final_validation_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    print(f"📈 FINAL VALIDATION RESULTS")
    print(f"="*40)
    print(f"Training Accuracy: {training_f1:.1%}")
    print(f"Reference Accuracy: {accuracy:.1%} ({correct_predictions}/{testable_videos})")
    print(f"Average Confidence: {avg_confidence:.3f}")
    
    print(f"\n🎯 PRODUCTION READINESS")
    print(f"Training Target (≥90%): {'✅' if training_f1 >= 0.90 else '❌'}")
    print(f"Reference Target (≥85%): {'✅' if accuracy >= 0.85 else '❌'}")
    print(f"Ready for Production: {'✅' if report['readiness_assessment']['ready_for_production'] else '❌'}")
    
    if phrase_accuracy:
        print(f"\n📊 Per-phrase Reference Accuracy:")
        for phrase, acc in sorted(phrase_accuracy.items(), key=lambda x: x[1]):
            print(f"   {phrase}: {acc:.1%}")
    
    return report


def main():
    """Main validation function"""
    
    print("🧪 POST-TRAINING VALIDATION")
    print("="*40)
    
    # Check if training completed
    checkpoint_path = "artifacts/vsr_26p_v1/best.ckpt"
    config_path = "configs/phrases26.yaml"
    metrics_path = "artifacts/vsr_26p_v1/test_metrics.json"
    
    if not Path(checkpoint_path).exists():
        print("❌ Training checkpoint not found!")
        print("   Please complete training first")
        return False
    
    print("✅ Training checkpoint found")
    
    # Load trained model
    try:
        model, config, checkpoint = load_trained_model(checkpoint_path, config_path)
        print(f"✅ Model loaded (epoch {checkpoint['epoch']}, F1: {checkpoint['best_val_f1']:.3f})")
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False
    
    # Validate against reference videos
    try:
        results, phrase_results = validate_against_references(model, config)
        if not results:
            print("❌ Reference validation failed")
            return False
        print(f"✅ Reference validation completed ({len(results)} videos)")
    except Exception as e:
        print(f"❌ Error in reference validation: {e}")
        return False
    
    # Generate final report
    try:
        report = generate_final_report(results, phrase_results, metrics_path)
        print(f"✅ Final report saved: final_validation_report.json")
    except Exception as e:
        print(f"❌ Error generating report: {e}")
        return False
    
    # Final assessment
    ready = report['readiness_assessment']['ready_for_production']
    print(f"\n🎉 VALIDATION COMPLETE!")
    print(f"Production Ready: {'✅ YES' if ready else '❌ NEEDS WORK'}")
    
    return ready


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
