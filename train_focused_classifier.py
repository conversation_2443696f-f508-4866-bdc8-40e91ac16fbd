#!/usr/bin/env python3
"""
Train focused ICU lipreading classifier on 13 high-performing phrases
Implements transfer learning from 26-class baseline model
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
import seaborn as sns
from tqdm import tqdm
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
sys.path.append('.')

from focused_model import FocusedMobile3DTiny, FocusedModelManager
from focused_data_loader import create_focused_dataloaders

class FocusedTrainer:
    """Trainer for focused 13-phrase ICU lipreading classifier"""
    
    def __init__(self, config_path: str):
        """Initialize trainer with configuration"""
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Set random seed for reproducibility
        self.set_seed(self.config.get('seed', 42))
        
        # Setup device
        self.device = self.setup_device()
        
        # Create directories
        self.setup_directories()
        
        # Initialize components
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        self.model_manager = None
        
        # Training state
        self.current_epoch = 0
        self.current_phase = 1
        self.best_val_accuracy = 0.0
        self.training_history = {
            'train_loss': [],
            'train_accuracy': [],
            'val_loss': [],
            'val_accuracy': [],
            'learning_rate': [],
            'phase': []
        }
        
        print(f"🎯 Focused Trainer Initialized")
        print(f"   Device: {self.device}")
        print(f"   Config: {config_path}")
        print(f"   Target phrases: {self.config['model']['num_classes']}")
    
    def set_seed(self, seed: int):
        """Set random seed for reproducibility"""
        torch.manual_seed(seed)
        np.random.seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
    
    def setup_device(self):
        """Setup training device"""
        if torch.cuda.is_available():
            device = torch.device('cuda')
            print(f"🚀 Using GPU: {torch.cuda.get_device_name()}")
        else:
            device = torch.device('cpu')
            print(f"💻 Using CPU (for compatibility)")
        
        return device
    
    def setup_directories(self):
        """Create necessary directories"""
        
        # Checkpoints directory
        self.checkpoint_dir = Path(self.config['checkpoints']['save_dir'])
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # Logs directory
        self.log_dir = Path(self.config['logging']['log_dir'])
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📁 Directories created:")
        print(f"   Checkpoints: {self.checkpoint_dir}")
        print(f"   Logs: {self.log_dir}")
    
    def prepare_data(self):
        """Prepare data loaders for focused training"""
        
        print(f"\n📊 Preparing Focused Dataset")
        print("=" * 30)
        
        # Check if manifest exists
        manifest_path = self.config['data']['manifest_path']
        if not Path(manifest_path).exists():
            raise FileNotFoundError(f"Manifest not found: {manifest_path}")
        
        # Load and analyze manifest
        manifest_df = pd.read_csv(manifest_path)
        print(f"📋 Loaded manifest: {len(manifest_df)} videos")
        
        # Show phrase distribution
        phrase_counts = manifest_df['phrase'].value_counts()
        print(f"📊 Phrase distribution:")
        for phrase, count in phrase_counts.items():
            print(f"   {phrase.title()}: {count} videos")
        
        # Create data loaders for focused dataset
        try:
            train_loader, val_loader, test_loader, data_info = create_focused_dataloaders(
                self.config, manifest_path
            )
            
            self.train_loader = train_loader
            self.val_loader = val_loader
            self.test_loader = test_loader
            self.data_info = data_info
            
            print(f"\n✅ Data loaders created:")
            print(f"   Train: {data_info['train_size']} videos")
            print(f"   Validation: {data_info['val_size']} videos")
            print(f"   Test: {data_info['test_size']} videos")
            print(f"   Classes: {data_info['num_classes']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create data loaders: {e}")
            return False
    
    def initialize_model(self):
        """Initialize focused model with transfer learning"""
        
        print(f"\n🤖 Initializing Focused Model")
        print("=" * 30)
        
        # Create model manager
        baseline_model_path = self.config['model']['pretrained_baseline_path']
        focused_summary_path = "focused_13_phrases_summary.json"
        
        self.model_manager = FocusedModelManager(baseline_model_path, focused_summary_path)
        
        # Create focused model with transfer learning
        self.model = self.model_manager.create_focused_model()
        self.model.to(self.device)
        
        print(f"✅ Model: {self.model.__class__.__name__}")
        print(f"   Parameters: {self.model.get_num_parameters():,}")
        print(f"   Classes: {self.config['model']['num_classes']}")
        print(f"   Transfer learning: {self.config['model']['transfer_learning']}")
        
        # Initialize optimizer for phase 1 (frozen backbone)
        self.setup_phase1_training()
        
        # Initialize loss function
        label_smoothing = self.config['regularization'].get('label_smoothing', 0.0)
        self.criterion = nn.CrossEntropyLoss(label_smoothing=label_smoothing)
        
        print(f"✅ Loss: CrossEntropyLoss (smoothing={label_smoothing})")
    
    def setup_phase1_training(self):
        """Setup training for phase 1 (frozen backbone)"""
        
        print(f"\n🔒 Phase 1: Frozen Backbone Training")
        print("=" * 35)
        
        # Freeze backbone
        self.model.freeze_backbone(freeze=True)
        
        # Setup optimizer for classifier only
        lr = self.config['training']['learning_rate']
        weight_decay = self.config['training']['weight_decay']
        
        # Only optimize classifier parameters
        classifier_params = []
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                classifier_params.append(param)
        
        self.optimizer = optim.AdamW(
            classifier_params,
            lr=lr,
            weight_decay=weight_decay
        )
        
        # Setup scheduler
        phase1_epochs = self.config['training_phases']['phase1']['epochs']
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=phase1_epochs
        )
        
        print(f"✅ Phase 1 setup complete")
        print(f"   Trainable parameters: {sum(p.numel() for p in classifier_params):,}")
        print(f"   Learning rate: {lr}")
        print(f"   Epochs: {phase1_epochs}")
    
    def setup_phase2_training(self):
        """Setup training for phase 2 (fine-tuning)"""
        
        print(f"\n🔓 Phase 2: Fine-tuning Entire Model")
        print("=" * 35)
        
        # Unfreeze backbone
        self.model.freeze_backbone(freeze=False)
        
        # Setup optimizer for entire model
        fine_tune_lr = self.config['training']['fine_tune_lr']
        weight_decay = self.config['training']['weight_decay']
        
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=fine_tune_lr,
            weight_decay=weight_decay
        )
        
        # Setup scheduler for remaining epochs
        phase2_epochs = self.config['training_phases']['phase2']['epochs']
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=phase2_epochs
        )
        
        self.current_phase = 2
        
        print(f"✅ Phase 2 setup complete")
        print(f"   Trainable parameters: {self.model.get_num_parameters():,}")
        print(f"   Learning rate: {fine_tune_lr}")
        print(f"   Epochs: {phase2_epochs}")
    
    def train_epoch(self):
        """Train for one epoch"""
        
        self.model.train()
        total_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        phase_name = f"Phase {self.current_phase}"
        progress_bar = tqdm(self.train_loader, desc=f"{phase_name} Epoch {self.current_epoch+1}")
        
        for batch_idx, (videos, labels, metadata) in enumerate(progress_bar):
            # Move data to device
            videos = videos.to(self.device)
            labels = labels.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(videos)
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            if self.config['training'].get('grad_clip_norm'):
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config['training']['grad_clip_norm']
                )
            
            self.optimizer.step()
            
            # Statistics
            total_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_samples += labels.size(0)
            correct_predictions += (predicted == labels).sum().item()
            
            # Update progress bar
            current_accuracy = correct_predictions / total_samples
            progress_bar.set_postfix({
                'Loss': f"{loss.item():.4f}",
                'Acc': f"{current_accuracy:.3f}",
                'Phase': self.current_phase
            })
        
        # Calculate epoch metrics
        epoch_loss = total_loss / len(self.train_loader)
        epoch_accuracy = correct_predictions / total_samples
        
        return epoch_loss, epoch_accuracy
    
    def validate_epoch(self):
        """Validate for one epoch"""
        
        self.model.eval()
        total_loss = 0.0
        correct_predictions = 0
        total_samples = 0
        
        with torch.no_grad():
            for videos, labels, metadata in self.val_loader:
                # Move data to device
                videos = videos.to(self.device)
                labels = labels.to(self.device)
                
                # Forward pass
                outputs = self.model(videos)
                loss = self.criterion(outputs, labels)
                
                # Statistics
                total_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                total_samples += labels.size(0)
                correct_predictions += (predicted == labels).sum().item()
        
        # Calculate epoch metrics
        epoch_loss = total_loss / len(self.val_loader)
        epoch_accuracy = correct_predictions / total_samples
        
        return epoch_loss, epoch_accuracy

    def save_checkpoint(self, epoch: int, is_best: bool = False):
        """Save model checkpoint"""

        checkpoint = {
            'epoch': epoch,
            'current_phase': self.current_phase,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_accuracy': self.best_val_accuracy,
            'training_history': self.training_history,
            'config': self.config,
            'model_info': {
                'num_classes': self.config['model']['num_classes'],
                'target_phrases': self.config['phrases'],
                'baseline_model': self.config['model']['pretrained_baseline_path']
            }
        }

        # Save regular checkpoint
        checkpoint_path = self.checkpoint_dir / f"checkpoint_epoch_{epoch:03d}.pth"
        torch.save(checkpoint, checkpoint_path)

        # Save best model
        if is_best:
            best_path = self.checkpoint_dir / "best_focused_model.pth"
            torch.save(checkpoint, best_path)
            print(f"💾 Best focused model saved: {best_path}")

    def train(self):
        """Main training loop with two phases"""

        print(f"\n🚀 Starting Two-Phase Training")
        print("=" * 35)

        # Phase 1: Frozen backbone
        phase1_epochs = self.config['training_phases']['phase1']['epochs']
        phase2_epochs = self.config['training_phases']['phase2']['epochs']
        total_epochs = phase1_epochs + phase2_epochs

        val_every_n_epochs = self.config['training']['val_every_n_epochs']
        save_every_n_epochs = self.config['training']['save_every_n_epochs']

        # Early stopping setup
        early_stopping_enabled = self.config['training']['early_stopping']['enabled']
        patience = self.config['training']['early_stopping']['patience']
        min_delta = self.config['training']['early_stopping']['min_delta']
        patience_counter = 0

        print(f"📋 Training Plan:")
        print(f"   Phase 1 (Frozen): {phase1_epochs} epochs")
        print(f"   Phase 2 (Fine-tune): {phase2_epochs} epochs")
        print(f"   Total: {total_epochs} epochs")

        for epoch in range(total_epochs):
            self.current_epoch = epoch

            # Check if we need to switch to phase 2
            if epoch == phase1_epochs and self.current_phase == 1:
                self.setup_phase2_training()

            # Training phase
            train_loss, train_accuracy = self.train_epoch()

            # Update learning rate
            self.scheduler.step()
            current_lr = self.optimizer.param_groups[0]['lr']

            # Store training metrics
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_accuracy'].append(train_accuracy)
            self.training_history['learning_rate'].append(current_lr)
            self.training_history['phase'].append(self.current_phase)

            # Validation phase
            if (epoch + 1) % val_every_n_epochs == 0:
                val_loss, val_accuracy = self.validate_epoch()

                self.training_history['val_loss'].append(val_loss)
                self.training_history['val_accuracy'].append(val_accuracy)

                # Check for best model
                is_best = val_accuracy > self.best_val_accuracy + min_delta
                if is_best:
                    self.best_val_accuracy = val_accuracy
                    patience_counter = 0
                else:
                    patience_counter += 1

                phase_name = f"P{self.current_phase}"
                print(f"Epoch {epoch+1:3d}/{total_epochs} | {phase_name} | "
                      f"Train: {train_loss:.4f}/{train_accuracy:.3f} | "
                      f"Val: {val_loss:.4f}/{val_accuracy:.3f} | "
                      f"LR: {current_lr:.6f} | "
                      f"Best: {self.best_val_accuracy:.3f}")

                # Save checkpoint
                if (epoch + 1) % save_every_n_epochs == 0 or is_best:
                    self.save_checkpoint(epoch + 1, is_best)

                # Early stopping check
                if early_stopping_enabled and patience_counter >= patience:
                    print(f"🛑 Early stopping triggered after {patience} epochs without improvement")
                    break
            else:
                phase_name = f"P{self.current_phase}"
                print(f"Epoch {epoch+1:3d}/{total_epochs} | {phase_name} | "
                      f"Train: {train_loss:.4f}/{train_accuracy:.3f} | "
                      f"LR: {current_lr:.6f}")

        print(f"\n✅ Focused Training completed!")
        print(f"🎯 Best validation accuracy: {self.best_val_accuracy:.3f}")

        return self.best_val_accuracy

    def compare_with_baseline(self):
        """Compare focused model performance with baseline"""

        baseline_accuracy = self.config['baseline_performance']['baseline_accuracy']
        target_accuracy = self.config['baseline_performance']['target_accuracy']

        print(f"\n📊 Performance Comparison")
        print("=" * 25)
        print(f"   Baseline (26-class): {baseline_accuracy:.1%}")
        print(f"   Focused (13-class): {self.best_val_accuracy:.1%}")
        print(f"   Target: {target_accuracy:.1%}")

        improvement = self.best_val_accuracy - baseline_accuracy
        print(f"   Improvement: {improvement:+.1%}")

        if self.best_val_accuracy >= target_accuracy:
            print(f"🎉 Target accuracy achieved!")
        else:
            remaining = target_accuracy - self.best_val_accuracy
            print(f"📈 {remaining:.1%} to target")

def main():
    """Main training function"""
    
    print("🎯 Focused ICU Lipreading Training - 13 High-Performing Phrases")
    print("=" * 65)
    
    # Initialize trainer
    config_path = "configs/focused_13_training.yaml"
    trainer = FocusedTrainer(config_path)
    
    # Prepare data
    if not trainer.prepare_data():
        print("❌ Data preparation failed. Exiting.")
        return
    
    # Initialize model
    trainer.initialize_model()
    
    print(f"\n🚀 Starting Focused Training")
    print("=" * 30)
    print(f"📊 Dataset: {trainer.data_info['train_size']} train, {trainer.data_info['val_size']} val")
    print(f"🎯 Target: 13 high-performing ICU phrases")
    print(f"🔄 Transfer learning from 26-class baseline")
    print(f"⏱️  Two-phase training strategy")
    
    # Start focused training
    try:
        best_accuracy = trainer.train()

        # Compare with baseline
        trainer.compare_with_baseline()

        print(f"\n🎉 Focused Training Complete!")
        print("=" * 35)
        print(f"✅ Best validation accuracy: {best_accuracy:.3f}")
        print(f"✅ Model saved in: {trainer.checkpoint_dir}")
        print(f"✅ 13 high-performing phrases trained")
        print(f"✅ Transfer learning successful")

    except Exception as e:
        print(f"❌ Focused training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
