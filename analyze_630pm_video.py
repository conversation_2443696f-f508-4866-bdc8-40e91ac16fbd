#!/usr/bin/env python3
"""
Robust analysis of 630pm.webm video with adaptive preprocessing
Handles different video formats and cropping scenarios
"""

import torch
import torch.nn.functional as F
import numpy as np
import cv2
from pathlib import Path
import sys
import json
import time
from typing import List, Dict, Optional, Tuple

# Add current directory to path
sys.path.append('.')

from perfect_10_model import Perfect10Mobile3DTiny
from backend.lightweight_vsr.utils_video import VideoProcessor

class RobustVideoAnalyzer:
    """Robust video analyzer with adaptive preprocessing"""
    
    def __init__(self):
        """Initialize the robust video analyzer"""
        
        self.device = torch.device('cpu')
        
        # Perfect 10 phrases
        self.perfect_phrases = [
            "am i getting better", "i feel anxious", "i m confused",
            "i need to move", "i need to sit up", "i want to phone my family",
            "what happened to me", "what time is my wife coming",
            "where am i", "who is with me today"
        ]
        
        self.phrase_to_idx = {phrase: idx for idx, phrase in enumerate(self.perfect_phrases)}
        self.idx_to_phrase = {idx: phrase for idx, phrase in enumerate(self.perfect_phrases)}
        
        # Model
        self.model = None
        self.model_name = None
        
        print(f"🎯 Robust Video Analyzer Initialized")
        print(f"   Perfect phrases: {len(self.perfect_phrases)}")
        print(f"   Device: {self.device}")
    
    def inspect_video_properties(self, video_path: str) -> Dict:
        """Inspect video properties to determine appropriate preprocessing"""
        
        print(f"\n🔍 Inspecting Video Properties: {Path(video_path).name}")
        print("=" * 50)
        
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return {'success': False, 'error': 'Cannot open video'}
        
        # Get video properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        
        # Read first frame to check content
        ret, first_frame = cap.read()
        first_frame_available = ret and first_frame is not None
        
        cap.release()
        
        properties = {
            'success': True,
            'width': width,
            'height': height,
            'fps': fps,
            'frame_count': frame_count,
            'duration_seconds': duration,
            'first_frame_available': first_frame_available
        }
        
        print(f"📊 Video Properties:")
        print(f"   Resolution: {width}×{height}")
        print(f"   FPS: {fps:.2f}")
        print(f"   Frame count: {frame_count}")
        print(f"   Duration: {duration:.2f} seconds")
        print(f"   First frame: {'Available' if first_frame_available else 'Not available'}")
        
        # Determine if ICU cropping is appropriate
        is_likely_mouth_region = (width < 200 and height < 200)
        is_standard_resolution = (width >= 400 and height >= 200)
        
        properties['is_likely_mouth_region'] = is_likely_mouth_region
        properties['is_standard_resolution'] = is_standard_resolution
        properties['recommended_cropping'] = is_standard_resolution and not is_likely_mouth_region
        
        print(f"   Likely mouth region: {is_likely_mouth_region}")
        print(f"   Standard resolution: {is_standard_resolution}")
        print(f"   Recommended ICU cropping: {properties['recommended_cropping']}")
        
        return properties
    
    def load_model(self) -> bool:
        """Load the Enhanced Mobile3DTiny model"""
        
        print(f"\n🤖 Loading Enhanced Mobile3DTiny Model")
        print("=" * 40)
        
        model_path = "checkpoints/perfect_10_training/best_perfect_10_model.pth"
        
        if not Path(model_path).exists():
            print(f"❌ Model not found: {model_path}")
            print(f"💡 Train model: python enhanced_perfect_10_training.py")
            return False
        
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            
            self.model = Perfect10Mobile3DTiny(num_classes=10)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            self.model_name = "Enhanced Mobile3DTiny"
            
            accuracy = checkpoint.get('best_val_accuracy', 0.0)
            print(f"✅ Model loaded: {self.model.get_num_parameters():,} params, {accuracy:.1%} accuracy")
            
            return True
            
        except Exception as e:
            print(f"❌ Model loading failed: {e}")
            return False
    
    def create_adaptive_video_processor(self, video_properties: Dict) -> VideoProcessor:
        """Create video processor adapted to video properties"""
        
        print(f"\n🔧 Creating Adaptive Video Processor")
        print("=" * 35)
        
        # Determine mouth cropping based on video properties
        if video_properties['recommended_cropping']:
            mouth_crop = (133, 0, 133, 100)  # ICU coordinates
            print(f"   🎯 Applying ICU mouth-cropping: {mouth_crop}")
        else:
            mouth_crop = None
            print(f"   🎯 No mouth-cropping (video appears pre-processed)")
        
        # Create enhanced video processor
        processor = VideoProcessor(
            target_frames=64,
            target_size=(112, 112),
            grayscale=True,
            fps=25.0,
            mouth_crop=mouth_crop,
            use_dataset_normalization=True
        )
        
        print(f"   ✅ Enhanced preprocessing: 64 frames, 112×112, z-score normalization")
        print(f"   ✅ Dataset normalization: mean={processor.DATASET_MEAN:.6f}, std={processor.DATASET_STD:.6f}")
        
        return processor
    
    def create_temporal_crops(self, video_tensor: torch.Tensor) -> List[torch.Tensor]:
        """Create temporal crops for TTA"""
        
        C, T, H, W = video_tensor.shape
        target_frames = 64
        
        if T <= target_frames:
            return [video_tensor]
        
        crops = []
        
        # Beginning crop
        crop1 = video_tensor[:, :target_frames, :, :]
        crops.append(crop1)
        
        # Middle crop
        middle = T // 2
        start_middle = max(0, middle - target_frames // 2)
        end_middle = min(T, start_middle + target_frames)
        crop2 = video_tensor[:, start_middle:end_middle, :, :]
        
        if crop2.shape[1] < target_frames:
            padding_needed = target_frames - crop2.shape[1]
            last_frame = crop2[:, -1:, :, :].repeat(1, padding_needed, 1, 1)
            crop2 = torch.cat([crop2, last_frame], dim=1)
        
        crops.append(crop2)
        
        # End crop
        crop3 = video_tensor[:, -target_frames:, :, :]
        crops.append(crop3)
        
        return crops
    
    def analyze_video(self, video_path: str) -> Dict:
        """Analyze video with robust preprocessing"""
        
        video_name = Path(video_path).name
        print(f"\n🎬 Analyzing Video: {video_name}")
        print("=" * 40)
        
        if not Path(video_path).exists():
            return {
                'success': False,
                'error': f'Video file not found: {video_path}'
            }
        
        if not self.model:
            return {
                'success': False,
                'error': 'No model loaded'
            }
        
        try:
            # Step 1: Inspect video properties
            video_properties = self.inspect_video_properties(video_path)
            
            if not video_properties['success']:
                return {
                    'success': False,
                    'error': video_properties['error']
                }
            
            # Step 2: Create adaptive processor
            processor = self.create_adaptive_video_processor(video_properties)
            
            # Step 3: Process video
            print(f"\n🔄 Processing Video with Enhanced Pipeline")
            print("=" * 45)
            
            start_time = time.time()
            video_tensor = processor.process_video(video_path)
            processing_time = (time.time() - start_time) * 1000
            
            print(f"📊 Processed tensor: {list(video_tensor.shape)}")
            print(f"📊 Value range: [{video_tensor.min():.3f}, {video_tensor.max():.3f}]")
            print(f"⏱️  Processing time: {processing_time:.0f}ms")
            
            # Step 4: Apply TTA if appropriate
            if video_tensor.shape[1] > 64:
                temporal_crops = self.create_temporal_crops(video_tensor)
                print(f"🔄 TTA: {len(temporal_crops)} temporal crops")
                
                all_logits = []
                for crop in temporal_crops:
                    crop_batch = crop.unsqueeze(0).to(self.device)
                    with torch.no_grad():
                        logits = self.model(crop_batch)
                        all_logits.append(logits[0])
                
                final_logits = torch.stack(all_logits).mean(dim=0)
                probabilities = F.softmax(final_logits, dim=0)
                tta_used = True
                
            else:
                print(f"🔄 Single inference (video ≤64 frames)")
                video_batch = video_tensor.unsqueeze(0).to(self.device)
                with torch.no_grad():
                    logits = self.model(video_batch)
                    final_logits = logits[0]
                    probabilities = F.softmax(final_logits, dim=0)
                tta_used = False
            
            # Step 5: Generate predictions
            inference_time = (time.time() - start_time) * 1000
            
            top3_probs, top3_indices = torch.topk(probabilities, 3)
            top3_probs = top3_probs.cpu().numpy()
            top3_indices = top3_indices.cpu().numpy()
            top3_phrases = [self.idx_to_phrase[idx] for idx in top3_indices]
            
            # Determine confidence level
            top_confidence = float(top3_probs[0])
            if top_confidence >= 0.8:
                confidence_level = "Very High"
                confidence_emoji = "🟢"
            elif top_confidence >= 0.6:
                confidence_level = "High"
                confidence_emoji = "🟡"
            elif top_confidence >= 0.4:
                confidence_level = "Moderate"
                confidence_emoji = "🟠"
            else:
                confidence_level = "Low"
                confidence_emoji = "🔴"
            
            return {
                'success': True,
                'video_path': video_path,
                'video_name': video_name,
                'video_properties': video_properties,
                'model_used': self.model_name,
                'tensor_shape': list(video_tensor.shape),
                'value_range': [float(video_tensor.min()), float(video_tensor.max())],
                'tta_used': tta_used,
                'processing_time_ms': processing_time,
                'inference_time_ms': inference_time,
                'top_prediction': top3_phrases[0],
                'top_confidence': top_confidence,
                'confidence_level': confidence_level,
                'confidence_emoji': confidence_emoji,
                'top3_phrases': top3_phrases,
                'top3_probabilities': top3_probs.tolist(),
                'all_probabilities': probabilities.cpu().numpy().tolist()
            }
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'video_path': video_path
            }
    
    def display_results(self, result: Dict):
        """Display formatted analysis results"""
        
        print(f"\n" + "="*60)
        print(f"🎯 VIDEO ANALYSIS RESULTS")
        print(f"="*60)
        
        if not result['success']:
            print(f"❌ Analysis Failed: {result['error']}")
            return
        
        # Preprocessing validation status
        preprocessing_passed = (
            result['tensor_shape'] == [1, 64, 112, 112] and
            -5 <= result['value_range'][0] <= -2 and
            2 <= result['value_range'][1] <= 5
        )
        
        print(f"🔧 PREPROCESSING VALIDATION: {'PASSED' if preprocessing_passed else 'FAILED'}")
        print(f"📹 Video: {result['video_name']}")
        print(f"🤖 Model: {result['model_used']}")
        print(f"📊 Processing: {result['tensor_shape']}, range [{result['value_range'][0]:.3f}, {result['value_range'][1]:.3f}], TTA: {'Yes' if result['tta_used'] else 'No'}")
        
        print(f"\n🏆 TOP PREDICTION: \"{result['top_prediction'].title()}\" ({result['top_confidence']:.1%} confidence)")
        print(f"Confidence Level: {result['confidence_emoji']} {result['confidence_level']}")
        
        print(f"\n📊 TOP-3 PREDICTIONS:")
        rank_emojis = ["🥇", "🥈", "🥉"]
        for i, (phrase, prob) in enumerate(zip(result['top3_phrases'], result['top3_probabilities'])):
            print(f"{rank_emojis[i]} {phrase.title()}: {prob:.1%}")
        
        print(f"\n⏱️ Inference Time: {result['inference_time_ms']:.0f}ms")
        
        # Video properties summary
        props = result['video_properties']
        print(f"\n📊 Video Properties:")
        print(f"   Resolution: {props['width']}×{props['height']}")
        print(f"   Duration: {props['duration_seconds']:.2f}s ({props['frame_count']} frames)")
        print(f"   FPS: {props['fps']:.2f}")
        print(f"   ICU cropping applied: {props['recommended_cropping']}")

def main():
    """Main analysis function"""
    
    print("🎯 Robust Analysis of 630pm.webm")
    print("=" * 35)
    
    # Initialize analyzer
    analyzer = RobustVideoAnalyzer()
    
    # Load model
    if not analyzer.load_model():
        print("❌ Cannot proceed without model")
        return
    
    # Analyze target video
    video_path = "/Users/<USER>/Desktop/630pm.webm"
    result = analyzer.analyze_video(video_path)
    
    # Display results
    analyzer.display_results(result)
    
    # Save results
    output_file = "630pm_analysis_results.json"
    with open(output_file, 'w') as f:
        json.dump(result, f, indent=2, default=str)
    
    print(f"\n💾 Results saved: {output_file}")
    print(f"\n🎉 Video Analysis Complete!")

if __name__ == '__main__':
    main()
