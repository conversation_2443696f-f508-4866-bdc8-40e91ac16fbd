#!/usr/bin/env python3
"""
Compute dataset-specific normalization statistics for Perfect 10 training data
Fixed version that handles pre-cropped videos correctly
"""

import pandas as pd
import numpy as np
import torch
from pathlib import Path
import sys
from tqdm import tqdm

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

def compute_perfect_10_normalization_stats():
    """Compute mean and std from Perfect 10 training data (pre-cropped videos)"""
    
    print("📊 Computing Perfect 10 Dataset Normalization Statistics")
    print("=" * 55)
    
    # Load Perfect 10 manifest
    manifest_path = "perfect_10_phrases_manifest.csv"
    if not Path(manifest_path).exists():
        print(f"❌ Perfect 10 manifest not found: {manifest_path}")
        return None, None
    
    manifest_df = pd.read_csv(manifest_path)
    print(f"📋 Loaded manifest: {len(manifest_df)} videos")
    
    # Check if videos are pre-cropped
    sample_video = manifest_df.iloc[0]['video_path']
    is_pre_cropped = "_mouth_cropped" in sample_video
    
    print(f"📊 Video type: {'Pre-cropped' if is_pre_cropped else 'Original'}")
    
    # Create VideoProcessor without mouth-cropping for pre-cropped videos
    if is_pre_cropped:
        video_processor = VideoProcessor(
            target_frames=32,  # Use current settings for consistency
            target_size=(96, 96),
            grayscale=True,
            mouth_crop=None,  # No additional cropping for pre-cropped videos
            use_dataset_normalization=False  # Disable to get raw [0,1] values
        )
    else:
        video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True,
            mouth_crop=(133, 0, 133, 100),  # ICU coordinates for original videos
            use_dataset_normalization=False
        )
    
    # Collect pixel values from all training videos
    all_pixel_values = []
    processed_count = 0
    failed_count = 0
    
    print(f"\n🔄 Processing videos to collect pixel statistics...")
    
    for idx, row in tqdm(manifest_df.iterrows(), total=len(manifest_df), desc="Processing videos"):
        video_path = row['video_path']
        
        if not Path(video_path).exists():
            failed_count += 1
            continue
        
        try:
            # Process video through pipeline
            video_tensor = video_processor.process_video(video_path)
            
            # Extract pixel values (tensor shape: [C, T, H, W])
            pixel_values = video_tensor.numpy().flatten()
            all_pixel_values.extend(pixel_values)
            
            processed_count += 1
            
            # Show progress for first few videos
            if processed_count <= 3:
                print(f"   ✅ Processed {Path(video_path).name}: {video_tensor.shape}, range [{video_tensor.min():.3f}, {video_tensor.max():.3f}]")
            
        except Exception as e:
            print(f"⚠️  Failed to process {Path(video_path).name}: {e}")
            failed_count += 1
    
    print(f"\n📊 Processing Summary:")
    print(f"   Successfully processed: {processed_count} videos")
    print(f"   Failed: {failed_count} videos")
    print(f"   Total pixel values: {len(all_pixel_values):,}")
    
    if len(all_pixel_values) == 0:
        print(f"❌ No pixel values collected")
        return None, None
    
    # Convert to numpy array for efficient computation
    all_pixels = np.array(all_pixel_values, dtype=np.float32)
    
    # Compute statistics
    dataset_mean = np.mean(all_pixels)
    dataset_std = np.std(all_pixels)
    
    print(f"\n📈 Dataset Normalization Statistics:")
    print(f"   Mean: {dataset_mean:.6f}")
    print(f"   Std:  {dataset_std:.6f}")
    print(f"   Min:  {np.min(all_pixels):.6f}")
    print(f"   Max:  {np.max(all_pixels):.6f}")
    
    # Additional statistics
    print(f"   Median: {np.median(all_pixels):.6f}")
    print(f"   25th percentile: {np.percentile(all_pixels, 25):.6f}")
    print(f"   75th percentile: {np.percentile(all_pixels, 75):.6f}")
    
    # Validate statistics
    if dataset_std < 0.001:
        print(f"⚠️  Warning: Very low standard deviation ({dataset_std:.6f})")
    
    if not (0.0 <= dataset_mean <= 1.0):
        print(f"⚠️  Warning: Mean outside [0,1] range ({dataset_mean:.6f})")
    
    return dataset_mean, dataset_std

def update_videoprocessor_constants(mean: float, std: float):
    """Update VideoProcessor with computed normalization constants"""
    
    print(f"\n🔧 Updating VideoProcessor Constants")
    print("=" * 35)
    
    # Read current VideoProcessor file
    video_processor_path = "backend/lightweight_vsr/utils_video.py"
    
    with open(video_processor_path, 'r') as f:
        content = f.read()
    
    # Replace placeholder values with computed statistics
    old_mean_line = "    DATASET_MEAN = 0.485  # Will be computed from actual training data"
    old_std_line = "    DATASET_STD = 0.229   # Will be computed from actual training data"
    
    new_mean_line = f"    DATASET_MEAN = {mean:.6f}  # Computed from Perfect 10 training data"
    new_std_line = f"    DATASET_STD = {std:.6f}   # Computed from Perfect 10 training data"
    
    # Update content
    updated_content = content.replace(old_mean_line, new_mean_line)
    updated_content = updated_content.replace(old_std_line, new_std_line)
    
    # Write back to file
    with open(video_processor_path, 'w') as f:
        f.write(updated_content)
    
    print(f"✅ Updated VideoProcessor constants:")
    print(f"   DATASET_MEAN = {mean:.6f}")
    print(f"   DATASET_STD = {std:.6f}")

def test_enhanced_preprocessing():
    """Test the enhanced preprocessing with 64 frames and 112x112 resolution"""
    
    print(f"\n🧪 Testing Enhanced Preprocessing Pipeline")
    print("=" * 40)
    
    # Load a test video
    manifest_path = "perfect_10_phrases_manifest.csv"
    if not Path(manifest_path).exists():
        print(f"❌ Cannot test: manifest not found")
        return
    
    manifest_df = pd.read_csv(manifest_path)
    test_video = manifest_df.iloc[0]['video_path']
    
    if not Path(test_video).exists():
        print(f"❌ Cannot test: test video not found")
        return
    
    # Test enhanced preprocessing
    enhanced_processor = VideoProcessor(
        target_frames=64,  # Enhanced: 64 frames
        target_size=(112, 112),  # Enhanced: 112x112 resolution
        grayscale=True,
        mouth_crop=None,  # No cropping for pre-cropped videos
        use_dataset_normalization=True  # Enhanced: z-score normalization
    )
    
    try:
        # Process with enhanced settings
        tensor_enhanced = enhanced_processor.process_video(test_video)
        
        print(f"✅ Enhanced preprocessing test successful:")
        print(f"   Input video: {Path(test_video).name}")
        print(f"   Output tensor: {tensor_enhanced.shape}")
        print(f"   Expected: [1, 64, 112, 112]")
        print(f"   Value range: [{tensor_enhanced.min():.3f}, {tensor_enhanced.max():.3f}]")
        
        # Validate enhanced specifications
        expected_shape = torch.Size([1, 64, 112, 112])
        if tensor_enhanced.shape == expected_shape:
            print(f"   ✅ Shape validation: PASSED")
        else:
            print(f"   ❌ Shape validation: FAILED")
        
        # Check z-score normalization
        mean_val = torch.mean(tensor_enhanced).item()
        std_val = torch.std(tensor_enhanced).item()
        
        print(f"   Z-score stats: mean={mean_val:.3f}, std={std_val:.3f}")
        
        if abs(mean_val) < 0.5 and 0.5 < std_val < 2.0:
            print(f"   ✅ Z-score normalization: REASONABLE")
        else:
            print(f"   ⚠️  Z-score normalization: MAY NEED ADJUSTMENT")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced preprocessing test failed: {e}")
        return False

def main():
    """Main function to compute and update normalization statistics"""
    
    print("🎯 Perfect 10 Dataset Normalization Statistics Computation (Fixed)")
    print("=" * 65)
    
    # Compute statistics from training data
    mean, std = compute_perfect_10_normalization_stats()
    
    if mean is None or std is None:
        print(f"❌ Failed to compute normalization statistics")
        return
    
    # Update VideoProcessor with computed values
    update_videoprocessor_constants(mean, std)
    
    # Test enhanced preprocessing
    test_enhanced_preprocessing()
    
    print(f"\n🎉 Dataset Normalization Statistics Complete!")
    print("=" * 45)
    print(f"✅ Statistics computed from Perfect 10 training data")
    print(f"✅ VideoProcessor updated with z-score normalization")
    print(f"✅ Enhanced preprocessing tested: 64 frames, 112×112")
    print(f"✅ Ready for enhanced Perfect 10 training")
    
    # Save statistics for reference
    stats_info = {
        'dataset_mean': float(mean),
        'dataset_std': float(std),
        'description': 'Perfect 10 ICU lipreading dataset normalization statistics',
        'computed_from': 'Perfect 10 pre-cropped training videos',
        'usage': 'Z-score normalization: (pixel - mean) / std',
        'video_type': 'pre-cropped mouth regions',
        'preprocessing': '32 frames, 96x96 resolution for statistics computation'
    }
    
    import json
    with open('perfect_10_normalization_stats.json', 'w') as f:
        json.dump(stats_info, f, indent=2)
    
    print(f"💾 Statistics saved: perfect_10_normalization_stats.json")

if __name__ == '__main__':
    main()
