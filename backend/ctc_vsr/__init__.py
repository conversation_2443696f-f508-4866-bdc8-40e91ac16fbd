"""
CTC-based Visual Speech Recognition (VSR) Pipeline

This module provides a complete CTC-based VSR implementation with two recognition modes:
1. Phoneme-CTC + Constrained Lexicon Decoder (ICU mode)
2. Character-CTC + Open Vocabulary Decoder (Open mode)

The pipeline uses a shared visual encoder (ResNet3D-18) and separate CTC heads for
phoneme and character recognition, maintaining commercial licensing compliance.

Key Features:
- Shared visual encoder between recognition modes
- Confidence-based mode selection and fallback
- Commercial-safe licensing (no research-only weights)
- FastAPI integration with existing backend
- Comprehensive error handling and logging

Usage:
    from backend.ctc_vsr import CTCInference
    
    # Initialize inference pipeline
    inference = CTCInference()
    
    # Predict with automatic mode selection
    result = inference.predict_best(video_path)
    
    # Or use specific modes
    icu_result = inference.predict_icu(video_path)
    open_result = inference.predict_open(video_path)
"""

__version__ = "1.0.0"
__author__ = "ICU-Lipreading MVP Team"

# Import main components for easy access
from .infer import CTCInference
from .visual_encoder import VisualEncoder
from .model_phoneme_ctc import PhonemeCtcModel
from .model_char_ctc import CharCtcModel

__all__ = [
    "CTCInference",
    "VisualEncoder", 
    "PhonemeCtcModel",
    "CharCtcModel"
]
