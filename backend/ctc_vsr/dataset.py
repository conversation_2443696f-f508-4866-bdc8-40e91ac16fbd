"""
Dataset loader for CTC-based Visual Speech Recognition.

This module provides PyTorch dataset classes for loading video data with
corresponding phoneme and character targets from JSONL manifest files.
Supports both MP4 video files and numpy arrays with data augmentation.
"""

import os
import json
import logging
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Optional, Union
from pathlib import Path
import cv2

try:
    import librosa
except ImportError:
    librosa = None

from .tokens import (
    get_phoneme_vocab, get_character_vocab,
    phoneme_sequence_to_indices, character_sequence_to_indices
)

logger = logging.getLogger(__name__)

class VideoDataset(Dataset):
    """
    Dataset for loading video data with phoneme and character targets.
    """
    
    def __init__(
        self,
        manifest_path: str,
        config: Dict,
        mode: str = "train",
        target_type: str = "phoneme"
    ):
        """
        Initialize dataset.
        
        Args:
            manifest_path: Path to JSONL manifest file
            config: Configuration dictionary
            mode: Dataset mode ("train", "val", "test")
            target_type: Target type ("phoneme" or "character")
        """
        self.manifest_path = manifest_path
        self.config = config
        self.mode = mode
        self.target_type = target_type
        
        # Video preprocessing parameters
        self.frames = config['video']['frames']
        self.height = config['video']['height']
        self.width = config['video']['width']
        self.grayscale = config['video']['grayscale']
        self.normalize = config['video'].get('normalize', True)
        
        # Data augmentation parameters (only for training)
        self.augmentation = config.get('augmentation', {})
        self.use_augmentation = (mode == "train" and 
                               self.augmentation.get('enabled', False))
        
        # Load vocabulary
        if target_type == "phoneme":
            self.tokens, self.token_to_idx, self.idx_to_token = get_phoneme_vocab()
        elif target_type == "character":
            self.tokens, self.token_to_idx, self.idx_to_token = get_character_vocab()
        else:
            raise ValueError(f"Invalid target_type: {target_type}")
        
        # Load manifest
        self.samples = self._load_manifest()
        
        logger.info(f"Loaded {len(self.samples)} samples for {mode} mode "
                   f"with {target_type} targets")
    
    def _load_manifest(self) -> List[Dict]:
        """
        Load samples from JSONL manifest file.
        
        Returns:
            List of sample dictionaries
        """
        samples = []
        
        if not os.path.exists(self.manifest_path):
            logger.warning(f"Manifest file not found: {self.manifest_path}")
            return samples
        
        with open(self.manifest_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    sample = json.loads(line.strip())
                    
                    # Validate required fields
                    required_fields = ['video', 'target_phonemes', 'target_chars']
                    if not all(field in sample for field in required_fields):
                        logger.warning(f"Missing required fields in line {line_num}")
                        continue
                    
                    # Filter by split if specified
                    if 'split' in sample and sample['split'] != self.mode:
                        continue
                    
                    # Check if video file exists
                    if not os.path.exists(sample['video']):
                        logger.warning(f"Video file not found: {sample['video']}")
                        continue
                    
                    samples.append(sample)
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"Invalid JSON in line {line_num}: {e}")
                except Exception as e:
                    logger.warning(f"Error processing line {line_num}: {e}")
        
        return samples
    
    def _load_video(self, video_path: str) -> np.ndarray:
        """
        Load video from file path.
        
        Args:
            video_path: Path to video file (.mp4 or .npy)
            
        Returns:
            Video array with shape (frames, height, width, channels)
        """
        if video_path.endswith('.npy'):
            # Load numpy array
            video = np.load(video_path)
            
            # Ensure correct shape
            if video.ndim == 3:  # (frames, height, width)
                video = np.expand_dims(video, axis=-1)
            elif video.ndim == 4:  # (frames, height, width, channels)
                pass
            else:
                raise ValueError(f"Invalid video shape: {video.shape}")
        
        elif video_path.endswith(('.mp4', '.avi', '.mov')):
            # Load video file using OpenCV
            cap = cv2.VideoCapture(video_path)
            frames = []
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Convert BGR to RGB
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame)
            
            cap.release()
            
            if not frames:
                raise ValueError(f"No frames loaded from video: {video_path}")
            
            video = np.array(frames)
        
        else:
            raise ValueError(f"Unsupported video format: {video_path}")
        
        return video
    
    def _preprocess_video(self, video: np.ndarray) -> torch.Tensor:
        """
        Preprocess video array.
        
        Args:
            video: Input video array
            
        Returns:
            Preprocessed video tensor
        """
        # Resize frames
        if video.shape[1] != self.height or video.shape[2] != self.width:
            resized_frames = []
            for frame in video:
                resized = cv2.resize(frame, (self.width, self.height))
                if len(resized.shape) == 2:  # Grayscale
                    resized = np.expand_dims(resized, axis=-1)
                resized_frames.append(resized)
            video = np.array(resized_frames)
        
        # Convert to grayscale if specified
        if self.grayscale and video.shape[-1] == 3:
            video = np.mean(video, axis=-1, keepdims=True)
        
        # Temporal sampling/padding to get exact number of frames
        current_frames = video.shape[0]
        if current_frames > self.frames:
            # Sample frames uniformly
            indices = np.linspace(0, current_frames - 1, self.frames, dtype=int)
            video = video[indices]
        elif current_frames < self.frames:
            # Pad by repeating last frame
            padding = self.frames - current_frames
            last_frame = video[-1:].repeat(padding, axis=0)
            video = np.concatenate([video, last_frame], axis=0)
        
        # Apply data augmentation if enabled
        if self.use_augmentation:
            video = self._apply_augmentation(video)
        
        # Normalize pixel values
        if self.normalize:
            video = video.astype(np.float32) / 255.0
        
        # Convert to tensor and rearrange dimensions
        # From (frames, height, width, channels) to (channels, frames, height, width)
        video = torch.from_numpy(video).permute(3, 0, 1, 2)
        
        return video
    
    def _apply_augmentation(self, video: np.ndarray) -> np.ndarray:
        """
        Apply data augmentation to video.
        
        Args:
            video: Input video array
            
        Returns:
            Augmented video array
        """
        # Temporal jittering
        if self.augmentation.get('temporal_jitter', 0) > 0:
            jitter = self.augmentation['temporal_jitter']
            shift = int(np.random.uniform(-jitter, jitter) * video.shape[0])
            if shift != 0:
                if shift > 0:
                    video = np.concatenate([video[shift:], video[:shift]], axis=0)
                else:
                    video = np.concatenate([video[shift:], video[:shift]], axis=0)
        
        # Spatial cropping
        if self.augmentation.get('spatial_crop', 0) > 0:
            crop_ratio = self.augmentation['spatial_crop']
            h, w = video.shape[1], video.shape[2]
            crop_h = int(h * crop_ratio)
            crop_w = int(w * crop_ratio)
            
            if crop_h > 0 and crop_w > 0:
                top = np.random.randint(0, crop_h + 1)
                left = np.random.randint(0, crop_w + 1)
                video = video[:, top:h-crop_h+top, left:w-crop_w+left]
        
        # Brightness adjustment
        if self.augmentation.get('brightness', 0) > 0:
            brightness = self.augmentation['brightness']
            factor = np.random.uniform(1 - brightness, 1 + brightness)
            video = np.clip(video * factor, 0, 255)
        
        # Contrast adjustment
        if self.augmentation.get('contrast', 0) > 0:
            contrast = self.augmentation['contrast']
            factor = np.random.uniform(1 - contrast, 1 + contrast)
            mean = np.mean(video)
            video = np.clip((video - mean) * factor + mean, 0, 255)
        
        return video
    
    def __len__(self) -> int:
        """Return number of samples."""
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, int]:
        """
        Get sample by index.
        
        Args:
            idx: Sample index
            
        Returns:
            Tuple of (video_tensor, target_tensor, target_length)
        """
        sample = self.samples[idx]
        
        try:
            # Load and preprocess video
            video = self._load_video(sample['video'])
            video_tensor = self._preprocess_video(video)
            
            # Get target sequence
            if self.target_type == "phoneme":
                target_text = sample['target_phonemes']
                target_indices = phoneme_sequence_to_indices(target_text, self.token_to_idx)
            else:  # character
                target_text = sample['target_chars']
                target_indices = character_sequence_to_indices(target_text, self.token_to_idx)
            
            target_tensor = torch.tensor(target_indices, dtype=torch.long)
            target_length = len(target_indices)
            
            return video_tensor, target_tensor, target_length
            
        except Exception as e:
            logger.error(f"Error loading sample {idx}: {e}")
            # Return dummy data to avoid breaking the batch
            dummy_video = torch.zeros(1 if self.grayscale else 3, self.frames, 
                                    self.height, self.width)
            dummy_target = torch.tensor([0], dtype=torch.long)  # Blank token
            return dummy_video, dummy_target, 1

def collate_fn(batch: List[Tuple[torch.Tensor, torch.Tensor, int]]) -> Dict[str, torch.Tensor]:
    """
    Collate function for DataLoader.
    
    Args:
        batch: List of (video, target, target_length) tuples
        
    Returns:
        Dictionary with batched tensors
    """
    videos, targets, target_lengths = zip(*batch)
    
    # Stack videos (all same size)
    videos = torch.stack(videos)
    
    # Pad targets to same length
    max_target_length = max(target_lengths)
    padded_targets = []
    
    for target in targets:
        if len(target) < max_target_length:
            # Pad with blank tokens (index 0)
            padding = torch.zeros(max_target_length - len(target), dtype=torch.long)
            padded_target = torch.cat([target, padding])
        else:
            padded_target = target
        padded_targets.append(padded_target)
    
    targets = torch.stack(padded_targets)
    target_lengths = torch.tensor(target_lengths, dtype=torch.long)
    
    return {
        'videos': videos,
        'targets': targets,
        'target_lengths': target_lengths
    }

def create_dataloader(
    manifest_path: str,
    config: Dict,
    mode: str = "train",
    target_type: str = "phoneme",
    batch_size: Optional[int] = None,
    shuffle: Optional[bool] = None,
    num_workers: int = 0
) -> DataLoader:
    """
    Create DataLoader for video dataset.
    
    Args:
        manifest_path: Path to JSONL manifest file
        config: Configuration dictionary
        mode: Dataset mode ("train", "val", "test")
        target_type: Target type ("phoneme" or "character")
        batch_size: Batch size (uses config if None)
        shuffle: Whether to shuffle (auto-determined if None)
        num_workers: Number of worker processes
        
    Returns:
        DataLoader instance
    """
    dataset = VideoDataset(manifest_path, config, mode, target_type)
    
    if batch_size is None:
        batch_size = config['training']['batch_size']
    
    if shuffle is None:
        shuffle = (mode == "train")
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        collate_fn=collate_fn,
        pin_memory=torch.cuda.is_available()
    )
    
    return dataloader

if __name__ == "__main__":
    # Test dataset loading
    import yaml

    # Load config
    config_path = Path(__file__).parent / "config.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Test manifest path
    manifest_path = "data/train_manifest.jsonl"

    if os.path.exists(manifest_path):
        # Create dataset
        dataset = VideoDataset(manifest_path, config, mode="train", target_type="phoneme")
        print(f"Dataset size: {len(dataset)}")

        if len(dataset) > 0:
            # Test loading a sample
            video, target, target_length = dataset[0]
            print(f"Video shape: {video.shape}")
            print(f"Target shape: {target.shape}")
            print(f"Target length: {target_length}")

            # Create dataloader
            dataloader = create_dataloader(manifest_path, config, mode="train",
                                         target_type="phoneme", batch_size=2)

            # Test batch loading
            for batch in dataloader:
                print(f"Batch videos shape: {batch['videos'].shape}")
                print(f"Batch targets shape: {batch['targets'].shape}")
                print(f"Batch target lengths: {batch['target_lengths']}")
                break
    else:
        print(f"Manifest file not found: {manifest_path}")
        print("Create a manifest file to test the dataset loader.")
