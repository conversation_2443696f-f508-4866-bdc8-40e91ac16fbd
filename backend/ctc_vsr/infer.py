"""
Unified inference API for CTC-based Visual Speech Recognition.

This module provides a high-level interface for running inference with both
phoneme and character CTC models, including confidence-based mode selection
and fallback mechanisms.
"""

import os
import yaml
import logging
import numpy as np
import torch
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import cv2

from .model_phoneme_ctc import PhonemeCtcModel
from .model_char_ctc import CharCtcModel
from .decode_phoneme_lex import PhonemeLexiconDecoder
from .decode_char_ctc import create_character_decoder
from .tokens import get_phoneme_vocab, get_character_vocab

logger = logging.getLogger(__name__)

class CTCInference:
    """
    Unified inference pipeline for CTC Visual Speech Recognition.
    
    Supports both ICU mode (phoneme + lexicon) and open mode (character + open vocab)
    with automatic mode selection based on confidence thresholds.
    """
    
    def __init__(
        self,
        config_path: Optional[str] = None,
        phoneme_model_path: Optional[str] = None,
        char_model_path: Optional[str] = None,
        lexicon_path: Optional[str] = None,
        device: Optional[str] = None
    ):
        """
        Initialize CTC inference pipeline.
        
        Args:
            config_path: Path to configuration file
            phoneme_model_path: Path to phoneme CTC model checkpoint
            char_model_path: Path to character CTC model checkpoint
            lexicon_path: Path to phoneme lexicon file
            device: Device to use ('cpu', 'cuda', or 'auto')
        """
        # Load configuration
        if config_path is None:
            config_path = Path(__file__).parent / "config.yaml"
        
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Set device
        if device == "auto" or device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
        
        # Confidence thresholds
        confidence_config = self.config.get('confidence', {})
        self.icu_threshold = confidence_config.get('icu_threshold', 0.65)
        self.open_threshold = confidence_config.get('open_threshold', 0.55)
        self.beam_width = confidence_config.get('beam_width', 10)
        
        # Video preprocessing parameters
        video_config = self.config['video']
        self.frames = video_config['frames']
        self.height = video_config['height']
        self.width = video_config['width']
        self.grayscale = video_config['grayscale']
        self.normalize = video_config.get('normalize', True)
        
        # Initialize models and decoders
        self.phoneme_model = None
        self.char_model = None
        self.phoneme_decoder = None
        self.char_decoder = None
        
        # Load models if paths provided
        if phoneme_model_path:
            self._load_phoneme_model(phoneme_model_path, lexicon_path)
        
        if char_model_path:
            self._load_char_model(char_model_path)
        
        logger.info(f"CTC inference initialized on device: {self.device}")
    
    def _load_phoneme_model(self, model_path: str, lexicon_path: Optional[str] = None):
        """Load phoneme CTC model and lexicon decoder."""
        try:
            # Load model checkpoint
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # Initialize model
            self.phoneme_model = PhonemeCtcModel(self.config)
            self.phoneme_model.load_state_dict(checkpoint['model_state_dict'])
            self.phoneme_model.to(self.device)
            self.phoneme_model.eval()
            
            # Initialize lexicon decoder
            if lexicon_path and os.path.exists(lexicon_path):
                phoneme_tokens = self.phoneme_model.get_phoneme_tokens()
                self.phoneme_decoder = PhonemeLexiconDecoder(
                    lexicon_path=lexicon_path,
                    phoneme_to_idx=phoneme_tokens['token_to_idx'],
                    idx_to_phoneme=phoneme_tokens['idx_to_token'],
                    beam_width=self.beam_width
                )
            else:
                logger.warning(f"Lexicon not found: {lexicon_path}. Using greedy decoding.")
            
            logger.info(f"Phoneme model loaded: {model_path}")
            
        except Exception as e:
            logger.error(f"Failed to load phoneme model: {e}")
            self.phoneme_model = None
    
    def _load_char_model(self, model_path: str):
        """Load character CTC model and decoder."""
        try:
            # Load model checkpoint
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # Initialize model
            self.char_model = CharCtcModel(self.config)
            self.char_model.load_state_dict(checkpoint['model_state_dict'])
            self.char_model.to(self.device)
            self.char_model.eval()
            
            # Initialize character decoder
            self.char_decoder = create_character_decoder(beam_width=self.beam_width)
            
            logger.info(f"Character model loaded: {model_path}")
            
        except Exception as e:
            logger.error(f"Failed to load character model: {e}")
            self.char_model = None
    
    def _preprocess_video(self, video_input: Union[str, np.ndarray]) -> torch.Tensor:
        """
        Preprocess video input for inference.
        
        Args:
            video_input: Video file path or numpy array
            
        Returns:
            Preprocessed video tensor
        """
        if isinstance(video_input, str):
            # Load video from file
            if video_input.endswith('.npy'):
                video = np.load(video_input)
            else:
                # Load video using OpenCV
                cap = cv2.VideoCapture(video_input)
                frames = []
                
                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    frames.append(frame)
                
                cap.release()
                
                if not frames:
                    raise ValueError(f"No frames loaded from video: {video_input}")
                
                video = np.array(frames)
        
        elif isinstance(video_input, np.ndarray):
            video = video_input.copy()
        else:
            raise ValueError(f"Invalid video input type: {type(video_input)}")
        
        # Ensure correct shape
        if video.ndim == 3:  # (frames, height, width)
            video = np.expand_dims(video, axis=-1)
        elif video.ndim != 4:  # (frames, height, width, channels)
            raise ValueError(f"Invalid video shape: {video.shape}")
        
        # Resize frames
        if video.shape[1] != self.height or video.shape[2] != self.width:
            resized_frames = []
            for frame in video:
                resized = cv2.resize(frame, (self.width, self.height))
                if len(resized.shape) == 2:
                    resized = np.expand_dims(resized, axis=-1)
                resized_frames.append(resized)
            video = np.array(resized_frames)
        
        # Convert to grayscale if specified
        if self.grayscale and video.shape[-1] == 3:
            video = np.mean(video, axis=-1, keepdims=True)
        
        # Temporal sampling/padding
        current_frames = video.shape[0]
        if current_frames > self.frames:
            indices = np.linspace(0, current_frames - 1, self.frames, dtype=int)
            video = video[indices]
        elif current_frames < self.frames:
            padding = self.frames - current_frames
            last_frame = video[-1:].repeat(padding, axis=0)
            video = np.concatenate([video, last_frame], axis=0)
        
        # Normalize pixel values
        if self.normalize:
            video = video.astype(np.float32) / 255.0
        
        # Convert to tensor and rearrange dimensions
        video = torch.from_numpy(video).permute(3, 0, 1, 2)  # (C, T, H, W)
        video = video.unsqueeze(0)  # Add batch dimension (1, C, T, H, W)
        
        return video
    
    def predict_icu(self, video_input: Union[str, np.ndarray]) -> Dict:
        """
        Predict using ICU mode (phoneme + lexicon).
        
        Args:
            video_input: Video file path or numpy array
            
        Returns:
            Prediction result dictionary
        """
        if self.phoneme_model is None:
            return {
                'success': False,
                'error': 'Phoneme model not loaded',
                'text': '',
                'confidence': 0.0,
                'mode': 'icu'
            }
        
        try:
            # Preprocess video
            video_tensor = self._preprocess_video(video_input).to(self.device)
            
            # Run inference
            with torch.no_grad():
                outputs = self.phoneme_model(video_tensor)
                log_probs = outputs['log_probs']  # (seq_len, batch_size, vocab_size)
                log_probs_np = log_probs[:, 0, :].cpu().numpy()  # (seq_len, vocab_size)
            
            # Decode using lexicon if available
            if self.phoneme_decoder:
                hypotheses = self.phoneme_decoder.decode(log_probs_np, return_top_k=1)
                if hypotheses:
                    text, confidence, word_sequence = hypotheses[0]
                else:
                    text, confidence, word_sequence = '', 0.0, []
            else:
                # Fallback to greedy decoding
                text, confidence = self.phoneme_model.decode_greedy(log_probs[:, 0, :])
                word_sequence = text.split() if text else []
            
            return {
                'success': True,
                'text': text,
                'confidence': confidence,
                'mode': 'icu',
                'word_sequence': word_sequence,
                'phoneme_sequence': text if not self.phoneme_decoder else None
            }
            
        except Exception as e:
            logger.error(f"ICU prediction failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'confidence': 0.0,
                'mode': 'icu'
            }
    
    def predict_open(self, video_input: Union[str, np.ndarray]) -> Dict:
        """
        Predict using open mode (character + open vocab).
        
        Args:
            video_input: Video file path or numpy array
            
        Returns:
            Prediction result dictionary
        """
        if self.char_model is None:
            return {
                'success': False,
                'error': 'Character model not loaded',
                'text': '',
                'confidence': 0.0,
                'mode': 'open'
            }
        
        try:
            # Preprocess video
            video_tensor = self._preprocess_video(video_input).to(self.device)
            
            # Run inference
            with torch.no_grad():
                outputs = self.char_model(video_tensor)
                log_probs = outputs['log_probs']  # (seq_len, batch_size, vocab_size)
                log_probs_np = log_probs[:, 0, :].cpu().numpy()  # (seq_len, vocab_size)
            
            # Decode using character decoder
            if self.char_decoder:
                hypotheses = self.char_decoder.decode(log_probs_np, return_top_k=1)
                if hypotheses:
                    text, confidence = hypotheses[0]
                else:
                    text, confidence = '', 0.0
            else:
                # Fallback to greedy decoding
                text, confidence = self.char_model.decode_greedy(log_probs[:, 0, :])
            
            return {
                'success': True,
                'text': text.strip(),
                'confidence': confidence,
                'mode': 'open',
                'word_sequence': text.strip().split() if text.strip() else []
            }
            
        except Exception as e:
            logger.error(f"Open prediction failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'confidence': 0.0,
                'mode': 'open'
            }
    
    def predict_best(self, video_input: Union[str, np.ndarray]) -> Dict:
        """
        Predict using best mode based on confidence thresholds.
        
        Args:
            video_input: Video file path or numpy array
            
        Returns:
            Prediction result dictionary
        """
        # Try ICU mode first
        icu_result = self.predict_icu(video_input)
        
        if icu_result['success'] and icu_result['confidence'] >= self.icu_threshold:
            icu_result['selected_mode'] = 'icu'
            icu_result['fallback_used'] = False
            return icu_result
        
        # Fallback to open mode
        open_result = self.predict_open(video_input)
        
        if open_result['success'] and open_result['confidence'] >= self.open_threshold:
            open_result['selected_mode'] = 'open'
            open_result['fallback_used'] = True
            open_result['icu_confidence'] = icu_result.get('confidence', 0.0)
            return open_result
        
        # Both modes failed or below threshold
        return {
            'success': False,
            'text': 'PLEASE REPEAT',
            'confidence': 0.0,
            'mode': 'fallback',
            'selected_mode': 'fallback',
            'fallback_used': True,
            'icu_confidence': icu_result.get('confidence', 0.0),
            'open_confidence': open_result.get('confidence', 0.0),
            'error': 'All modes below confidence threshold'
        }
    
    def get_model_info(self) -> Dict:
        """Get information about loaded models."""
        return {
            'phoneme_model_loaded': self.phoneme_model is not None,
            'char_model_loaded': self.char_model is not None,
            'phoneme_decoder_loaded': self.phoneme_decoder is not None,
            'char_decoder_loaded': self.char_decoder is not None,
            'device': str(self.device),
            'icu_threshold': self.icu_threshold,
            'open_threshold': self.open_threshold,
            'beam_width': self.beam_width
        }

if __name__ == "__main__":
    # Test CTC inference
    import argparse
    
    parser = argparse.ArgumentParser(description="Test CTC inference")
    parser.add_argument("--video", type=str, help="Path to test video")
    parser.add_argument("--phoneme-model", type=str, help="Path to phoneme model")
    parser.add_argument("--char-model", type=str, help="Path to character model")
    parser.add_argument("--lexicon", type=str, help="Path to lexicon file")
    parser.add_argument("--mode", type=str, choices=["icu", "open", "best"], 
                       default="best", help="Inference mode")
    
    args = parser.parse_args()
    
    # Initialize inference
    inference = CTCInference(
        phoneme_model_path=args.phoneme_model,
        char_model_path=args.char_model,
        lexicon_path=args.lexicon
    )
    
    print("Model info:", inference.get_model_info())
    
    if args.video:
        # Run inference
        if args.mode == "icu":
            result = inference.predict_icu(args.video)
        elif args.mode == "open":
            result = inference.predict_open(args.video)
        else:
            result = inference.predict_best(args.video)
        
        print(f"Result: {result}")
    else:
        print("No video provided for testing")

        # Test with dummy video if no real video provided
        print("Creating dummy video for testing...")
        dummy_video = np.random.randint(0, 255, (64, 112, 112, 1), dtype=np.uint8)

        if args.mode == "icu":
            result = inference.predict_icu(dummy_video)
        elif args.mode == "open":
            result = inference.predict_open(dummy_video)
        else:
            result = inference.predict_best(dummy_video)

        print(f"Dummy video result: {result}")
