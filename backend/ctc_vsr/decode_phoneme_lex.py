"""
Phoneme lexicon-based beam search decoder for CTC Visual Speech Recognition.

This module implements a trie-based beam search decoder that constrains
recognition to a predefined lexicon of ICU vocabulary words using phoneme
sequences for improved accuracy in clinical settings.
"""

import json
import logging
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
import heapq

logger = logging.getLogger(__name__)

class TrieNode:
    """Node in a trie data structure for efficient lexicon search."""
    
    def __init__(self):
        self.children = {}
        self.is_word_end = False
        self.word = None
        self.phonemes = []

class PhonemeTrie:
    """
    Trie data structure for phoneme sequences in lexicon.
    """
    
    def __init__(self):
        self.root = TrieNode()
        self.word_count = 0
    
    def insert(self, phonemes: List[str], word: str):
        """
        Insert a phoneme sequence into the trie.
        
        Args:
            phonemes: List of phonemes (e.g., ['P', 'EY', 'N'])
            word: Corresponding word (e.g., 'PAIN')
        """
        node = self.root
        
        for phoneme in phonemes:
            if phoneme not in node.children:
                node.children[phoneme] = TrieNode()
            node = node.children[phoneme]
        
        node.is_word_end = True
        node.word = word
        node.phonemes = phonemes
        self.word_count += 1
    
    def search_prefix(self, phonemes: List[str]) -> Optional[TrieNode]:
        """
        Search for a phoneme prefix in the trie.
        
        Args:
            phonemes: List of phonemes to search
            
        Returns:
            TrieNode if prefix exists, None otherwise
        """
        node = self.root
        
        for phoneme in phonemes:
            if phoneme not in node.children:
                return None
            node = node.children[phoneme]
        
        return node
    
    def get_completions(self, node: TrieNode) -> List[Tuple[str, List[str]]]:
        """
        Get all word completions from a given node.
        
        Args:
            node: Starting trie node
            
        Returns:
            List of (word, phonemes) tuples
        """
        completions = []
        
        def dfs(current_node, path):
            if current_node.is_word_end:
                completions.append((current_node.word, current_node.phonemes))
            
            for phoneme, child_node in current_node.children.items():
                dfs(child_node, path + [phoneme])
        
        dfs(node, [])
        return completions

class BeamSearchState:
    """State for beam search decoding."""
    
    def __init__(
        self,
        phonemes: List[str],
        log_prob: float,
        trie_node: TrieNode,
        last_token: int,
        word_sequence: List[str]
    ):
        self.phonemes = phonemes
        self.log_prob = log_prob
        self.trie_node = trie_node
        self.last_token = last_token
        self.word_sequence = word_sequence
    
    def __lt__(self, other):
        # For heap ordering (higher probability = lower in heap)
        return self.log_prob > other.log_prob

class PhonemeLexiconDecoder:
    """
    Beam search decoder with phoneme lexicon constraints.
    """
    
    def __init__(
        self,
        lexicon_path: str,
        phoneme_to_idx: Dict[str, int],
        idx_to_phoneme: Dict[int, str],
        beam_width: int = 10
    ):
        """
        Initialize phoneme lexicon decoder.
        
        Args:
            lexicon_path: Path to lexicon JSON file
            phoneme_to_idx: Mapping from phoneme to index
            idx_to_phoneme: Mapping from index to phoneme
            beam_width: Beam search width
        """
        self.phoneme_to_idx = phoneme_to_idx
        self.idx_to_phoneme = idx_to_phoneme
        self.beam_width = beam_width
        self.blank_idx = 0  # Blank token is at index 0
        
        # Load lexicon and build trie
        self.lexicon = self._load_lexicon(lexicon_path)
        self.trie = self._build_trie()
        
        logger.info(f"Phoneme lexicon decoder initialized with {self.trie.word_count} words, "
                   f"beam_width={beam_width}")
    
    def _load_lexicon(self, lexicon_path: str) -> Dict[str, List[str]]:
        """
        Load lexicon from JSON file.
        
        Args:
            lexicon_path: Path to lexicon file
            
        Returns:
            Dictionary mapping words to phoneme sequences
        """
        try:
            with open(lexicon_path, 'r') as f:
                lexicon_json = json.load(f)
            
            # Convert to simple format
            lexicon = {}
            for word, data in lexicon_json.items():
                if isinstance(data, dict) and 'phonemes' in data:
                    lexicon[word] = data['phonemes']
                elif isinstance(data, list):
                    lexicon[word] = data
                else:
                    logger.warning(f"Invalid lexicon entry for word '{word}': {data}")
            
            logger.info(f"Loaded lexicon with {len(lexicon)} words")
            return lexicon
            
        except Exception as e:
            logger.error(f"Failed to load lexicon from {lexicon_path}: {e}")
            return {}
    
    def _build_trie(self) -> PhonemeTrie:
        """
        Build trie from lexicon.
        
        Returns:
            PhonemeTrie instance
        """
        trie = PhonemeTrie()
        
        for word, phonemes in self.lexicon.items():
            # Validate phonemes
            valid_phonemes = []
            for phoneme in phonemes:
                if phoneme in self.phoneme_to_idx:
                    valid_phonemes.append(phoneme)
                else:
                    logger.warning(f"Unknown phoneme '{phoneme}' in word '{word}'")
            
            if valid_phonemes:
                trie.insert(valid_phonemes, word)
            else:
                logger.warning(f"No valid phonemes for word '{word}'")
        
        return trie
    
    def decode(
        self,
        log_probs: np.ndarray,
        return_top_k: int = 5
    ) -> List[Tuple[str, float, List[str]]]:
        """
        Decode CTC log probabilities using beam search with lexicon constraints.
        
        Args:
            log_probs: CTC log probabilities (seq_len, vocab_size)
            return_top_k: Number of top hypotheses to return
            
        Returns:
            List of (decoded_text, confidence, word_sequence) tuples
        """
        seq_len, vocab_size = log_probs.shape
        
        # Initialize beam with empty state
        beam = [BeamSearchState(
            phonemes=[],
            log_prob=0.0,
            trie_node=self.trie.root,
            last_token=self.blank_idx,
            word_sequence=[]
        )]
        
        # Beam search over time steps
        for t in range(seq_len):
            new_beam = []
            
            for state in beam:
                # Get top tokens for this timestep
                top_tokens = np.argsort(log_probs[t])[-self.beam_width:]
                
                for token_idx in top_tokens:
                    token_log_prob = log_probs[t, token_idx]
                    new_log_prob = state.log_prob + token_log_prob
                    
                    # Skip if same as last token (CTC collapse)
                    if token_idx == state.last_token:
                        new_beam.append(BeamSearchState(
                            phonemes=state.phonemes.copy(),
                            log_prob=new_log_prob,
                            trie_node=state.trie_node,
                            last_token=token_idx,
                            word_sequence=state.word_sequence.copy()
                        ))
                        continue
                    
                    # Handle blank token
                    if token_idx == self.blank_idx:
                        new_beam.append(BeamSearchState(
                            phonemes=state.phonemes.copy(),
                            log_prob=new_log_prob,
                            trie_node=state.trie_node,
                            last_token=token_idx,
                            word_sequence=state.word_sequence.copy()
                        ))
                        continue
                    
                    # Handle phoneme token
                    if token_idx in self.idx_to_phoneme:
                        phoneme = self.idx_to_phoneme[token_idx]
                        new_phonemes = state.phonemes + [phoneme]
                        
                        # Check if phoneme sequence is valid in trie
                        trie_node = self.trie.search_prefix(new_phonemes)
                        
                        if trie_node is not None:
                            # Valid prefix - continue
                            new_state = BeamSearchState(
                                phonemes=new_phonemes,
                                log_prob=new_log_prob,
                                trie_node=trie_node,
                                last_token=token_idx,
                                word_sequence=state.word_sequence.copy()
                            )
                            
                            # Check if we completed a word
                            if trie_node.is_word_end:
                                # Add completed word and reset phoneme sequence
                                completed_state = BeamSearchState(
                                    phonemes=[],
                                    log_prob=new_log_prob,
                                    trie_node=self.trie.root,
                                    last_token=token_idx,
                                    word_sequence=state.word_sequence + [trie_node.word]
                                )
                                new_beam.append(completed_state)
                            
                            new_beam.append(new_state)
                        
                        # Also try starting a new word with this phoneme
                        if phoneme in self.trie.root.children:
                            new_trie_node = self.trie.root.children[phoneme]
                            new_state = BeamSearchState(
                                phonemes=[phoneme],
                                log_prob=new_log_prob,
                                trie_node=new_trie_node,
                                last_token=token_idx,
                                word_sequence=state.word_sequence.copy()
                            )
                            
                            # Check if single phoneme completes a word
                            if new_trie_node.is_word_end:
                                completed_state = BeamSearchState(
                                    phonemes=[],
                                    log_prob=new_log_prob,
                                    trie_node=self.trie.root,
                                    last_token=token_idx,
                                    word_sequence=state.word_sequence + [new_trie_node.word]
                                )
                                new_beam.append(completed_state)
                            
                            new_beam.append(new_state)
            
            # Prune beam to keep only top candidates
            if len(new_beam) > self.beam_width:
                new_beam.sort(key=lambda x: x.log_prob, reverse=True)
                beam = new_beam[:self.beam_width]
            else:
                beam = new_beam
        
        # Finalize hypotheses
        hypotheses = []
        
        for state in beam:
            # Try to complete any partial words
            word_sequence = state.word_sequence.copy()
            
            if state.phonemes and state.trie_node.is_word_end:
                word_sequence.append(state.trie_node.word)
            
            if word_sequence:
                decoded_text = ' '.join(word_sequence)
                confidence = np.exp(state.log_prob / len(decoded_text.split()))
                hypotheses.append((decoded_text, confidence, word_sequence))
        
        # Sort by confidence and return top-k
        hypotheses.sort(key=lambda x: x[1], reverse=True)
        return hypotheses[:return_top_k]
    
    def get_lexicon_stats(self) -> Dict:
        """Get lexicon statistics."""
        return {
            'total_words': len(self.lexicon),
            'trie_words': self.trie.word_count,
            'avg_phonemes_per_word': np.mean([len(phonemes) for phonemes in self.lexicon.values()]),
            'max_phonemes_per_word': max([len(phonemes) for phonemes in self.lexicon.values()]),
            'min_phonemes_per_word': min([len(phonemes) for phonemes in self.lexicon.values()])
        }

if __name__ == "__main__":
    # Test phoneme lexicon decoder
    from tokens import get_phoneme_vocab
    
    # Get phoneme vocabulary
    phoneme_tokens, phoneme_to_idx, idx_to_phoneme = get_phoneme_vocab()
    
    # Create dummy lexicon
    lexicon = {
        'PAIN': ['P', 'EY', 'N'],
        'HELP': ['HH', 'EH', 'L', 'P'],
        'NURSE': ['N', 'ER', 'S']
    }
    
    # Save dummy lexicon
    lexicon_path = "test_lexicon.json"
    lexicon_json = {
        word: {'phonemes': phonemes, 'phoneme_string': ' '.join(phonemes)}
        for word, phonemes in lexicon.items()
    }
    
    with open(lexicon_path, 'w') as f:
        json.dump(lexicon_json, f, indent=2)
    
    # Initialize decoder
    decoder = PhonemeLexiconDecoder(
        lexicon_path=lexicon_path,
        phoneme_to_idx=phoneme_to_idx,
        idx_to_phoneme=idx_to_phoneme,
        beam_width=5
    )
    
    # Test decoding with dummy log probabilities
    seq_len = 10
    vocab_size = len(phoneme_tokens)
    log_probs = np.random.randn(seq_len, vocab_size)
    log_probs = log_probs - np.log(np.sum(np.exp(log_probs), axis=1, keepdims=True))
    
    # Decode
    hypotheses = decoder.decode(log_probs, return_top_k=3)
    
    print("Decoding results:")
    for i, (text, confidence, words) in enumerate(hypotheses):
        print(f"  {i+1}. '{text}' (confidence: {confidence:.3f}, words: {words})")
    
    # Print stats
    stats = decoder.get_lexicon_stats()
    print(f"\nLexicon stats: {stats}")
    
    # Cleanup
    import os
    os.remove(lexicon_path)
