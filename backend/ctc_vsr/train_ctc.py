"""
Unified training script for CTC-based Visual Speech Recognition models.

This script supports training both phoneme and character CTC models with
comprehensive logging, checkpointing, and evaluation metrics.
"""

import os
import sys
import yaml
import json
import logging
import argparse
from pathlib import Path
from typing import Dict, Tuple
import time

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from ctc_vsr.model_phoneme_ctc import PhonemeCtcModel, PhonemeCtcTrainer
from ctc_vsr.model_char_ctc import CharCtcModel, CharCtcTrainer
from ctc_vsr.dataset import create_dataloader
# from ctc_vsr.metrics import calculate_cer, calculate_wer  # Will be implemented later

logger = logging.getLogger(__name__)

class CTCTrainer:
    """
    Unified trainer for CTC models.
    """
    
    def __init__(
        self,
        config: Dict,
        mode: str,
        train_manifest: str,
        val_manifest: str,
        output_dir: str,
        device: torch.device
    ):
        """
        Initialize CTC trainer.
        
        Args:
            config: Configuration dictionary
            mode: Training mode ("phoneme" or "character")
            train_manifest: Path to training manifest
            val_manifest: Path to validation manifest
            output_dir: Output directory for checkpoints and logs
            device: Training device
        """
        self.config = config
        self.mode = mode
        self.train_manifest = train_manifest
        self.val_manifest = val_manifest
        self.output_dir = Path(output_dir)
        self.device = device
        
        # Create output directories
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.checkpoint_dir = self.output_dir / "checkpoints"
        self.checkpoint_dir.mkdir(exist_ok=True)
        self.log_dir = self.output_dir / "logs"
        self.log_dir.mkdir(exist_ok=True)
        
        # Initialize model and trainer
        self._init_model()
        self._init_dataloaders()
        self._init_logging()
        
        # Training state
        self.epoch = 0
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.training_config = config['training']
        
        logger.info(f"CTC trainer initialized for {mode} mode")
    
    def _init_model(self):
        """Initialize model and trainer."""
        if self.mode == "phoneme":
            self.model = PhonemeCtcModel(self.config)
            self.trainer = PhonemeCtcTrainer(self.model, self.config, self.device)
        elif self.mode == "character":
            self.model = CharCtcModel(self.config)
            self.trainer = CharCtcTrainer(self.model, self.config, self.device)
        else:
            raise ValueError(f"Invalid mode: {self.mode}")
        
        self.model.to(self.device)
        
        # Count parameters
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        logger.info(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")
    
    def _init_dataloaders(self):
        """Initialize data loaders."""
        self.train_loader = create_dataloader(
            self.train_manifest,
            self.config,
            mode="train",
            target_type=self.mode,
            num_workers=4
        )
        
        self.val_loader = create_dataloader(
            self.val_manifest,
            self.config,
            mode="val",
            target_type=self.mode,
            batch_size=self.config['training']['batch_size'],
            shuffle=False,
            num_workers=4
        )
        
        logger.info(f"Data loaders initialized: {len(self.train_loader)} train batches, "
                   f"{len(self.val_loader)} val batches")
    
    def _init_logging(self):
        """Initialize logging and tensorboard."""
        # Setup file logging
        log_file = self.log_dir / f"train_{self.mode}.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Initialize tensorboard
        self.writer = SummaryWriter(self.log_dir / f"tensorboard_{self.mode}")
        
        logger.info(f"Logging initialized: {log_file}")
    
    def train_epoch(self) -> Tuple[float, float]:
        """
        Train for one epoch.
        
        Returns:
            Tuple of (average_loss, average_accuracy)
        """
        self.model.train()
        total_loss = 0.0
        total_accuracy = 0.0
        num_batches = len(self.train_loader)
        
        start_time = time.time()
        
        for batch_idx, batch in enumerate(self.train_loader):
            # Training step
            loss = self.trainer.train_step(batch)
            total_loss += loss
            
            # Log progress
            if batch_idx % 10 == 0:
                logger.info(f"Epoch {self.epoch}, Batch {batch_idx}/{num_batches}, "
                           f"Loss: {loss:.4f}")
        
        avg_loss = total_loss / num_batches
        epoch_time = time.time() - start_time
        
        logger.info(f"Epoch {self.epoch} training completed in {epoch_time:.1f}s, "
                   f"Average loss: {avg_loss:.4f}")
        
        return avg_loss, 0.0  # Accuracy calculation can be added if needed
    
    def validate_epoch(self) -> Tuple[float, float]:
        """
        Validate for one epoch.
        
        Returns:
            Tuple of (average_loss, average_accuracy)
        """
        self.model.eval()
        total_loss = 0.0
        total_accuracy = 0.0
        num_batches = len(self.val_loader)
        
        with torch.no_grad():
            for batch in self.val_loader:
                loss, accuracy = self.trainer.validate_step(batch)
                total_loss += loss
                total_accuracy += accuracy
        
        avg_loss = total_loss / num_batches
        avg_accuracy = total_accuracy / num_batches
        
        logger.info(f"Epoch {self.epoch} validation: Loss: {avg_loss:.4f}, "
                   f"Accuracy: {avg_accuracy:.4f}")
        
        return avg_loss, avg_accuracy
    
    def save_checkpoint(self, is_best: bool = False):
        """
        Save model checkpoint.
        
        Args:
            is_best: Whether this is the best model so far
        """
        checkpoint = {
            'epoch': self.epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.trainer.optimizer.state_dict(),
            'scheduler_state_dict': self.trainer.scheduler.state_dict(),
            'best_val_loss': self.best_val_loss,
            'config': self.config,
            'mode': self.mode
        }
        
        # Save regular checkpoint
        checkpoint_path = self.checkpoint_dir / f"checkpoint_epoch_{self.epoch}.pt"
        torch.save(checkpoint, checkpoint_path)
        
        # Save best model
        if is_best:
            best_path = self.checkpoint_dir / "best_model.pt"
            torch.save(checkpoint, best_path)
            logger.info(f"Best model saved: {best_path}")
        
        # Save latest model
        latest_path = self.checkpoint_dir / "latest_model.pt"
        torch.save(checkpoint, latest_path)
        
        logger.info(f"Checkpoint saved: {checkpoint_path}")
    
    def load_checkpoint(self, checkpoint_path: str) -> bool:
        """
        Load model checkpoint.
        
        Args:
            checkpoint_path: Path to checkpoint file
            
        Returns:
            True if checkpoint loaded successfully
        """
        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.trainer.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            self.epoch = checkpoint['epoch']
            self.best_val_loss = checkpoint['best_val_loss']
            
            logger.info(f"Checkpoint loaded: {checkpoint_path}, epoch {self.epoch}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load checkpoint {checkpoint_path}: {e}")
            return False
    
    def train(self, resume_from: str = None):
        """
        Main training loop.
        
        Args:
            resume_from: Path to checkpoint to resume from
        """
        # Resume from checkpoint if specified
        if resume_from and os.path.exists(resume_from):
            self.load_checkpoint(resume_from)
            self.epoch += 1  # Start from next epoch
        
        max_epochs = self.training_config['epochs']
        patience = self.training_config.get('patience', 3)
        
        logger.info(f"Starting training for {max_epochs} epochs")
        
        for epoch in range(self.epoch, max_epochs):
            self.epoch = epoch
            
            # Training
            train_loss, train_acc = self.train_epoch()
            
            # Validation
            val_loss, val_acc = self.validate_epoch()
            
            # Learning rate scheduling
            self.trainer.step_scheduler(val_loss)
            
            # Tensorboard logging
            self.writer.add_scalar('Loss/Train', train_loss, epoch)
            self.writer.add_scalar('Loss/Validation', val_loss, epoch)
            self.writer.add_scalar('Accuracy/Validation', val_acc, epoch)
            self.writer.add_scalar('Learning_Rate', 
                                 self.trainer.optimizer.param_groups[0]['lr'], epoch)
            
            # Check for improvement
            is_best = val_loss < self.best_val_loss
            if is_best:
                self.best_val_loss = val_loss
                self.patience_counter = 0
            else:
                self.patience_counter += 1
            
            # Save checkpoint
            self.save_checkpoint(is_best)
            
            # Early stopping
            if self.patience_counter >= patience:
                logger.info(f"Early stopping triggered after {patience} epochs without improvement")
                break
        
        logger.info("Training completed")
        self.writer.close()

def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Train CTC Visual Speech Recognition model")
    parser.add_argument("--config", type=str, default="backend/ctc_vsr/config.yaml",
                       help="Path to configuration file")
    parser.add_argument("--mode", type=str, choices=["phoneme", "character"], required=True,
                       help="Training mode: phoneme or character")
    parser.add_argument("--train-manifest", type=str, required=True,
                       help="Path to training manifest file")
    parser.add_argument("--val-manifest", type=str, required=True,
                       help="Path to validation manifest file")
    parser.add_argument("--output-dir", type=str, default="artifacts/training",
                       help="Output directory for checkpoints and logs")
    parser.add_argument("--resume-from", type=str, default=None,
                       help="Path to checkpoint to resume training from")
    parser.add_argument("--device", type=str, default="auto",
                       help="Training device (auto, cpu, cuda)")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    
    # Load configuration
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
    
    # Determine device
    if args.device == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(args.device)
    
    logger.info(f"Using device: {device}")
    
    # Check manifest files
    if not os.path.exists(args.train_manifest):
        logger.error(f"Training manifest not found: {args.train_manifest}")
        return 1
    
    if not os.path.exists(args.val_manifest):
        logger.error(f"Validation manifest not found: {args.val_manifest}")
        return 1
    
    try:
        # Initialize trainer
        trainer = CTCTrainer(
            config=config,
            mode=args.mode,
            train_manifest=args.train_manifest,
            val_manifest=args.val_manifest,
            output_dir=args.output_dir,
            device=device
        )
        
        # Start training
        trainer.train(resume_from=args.resume_from)
        
        logger.info("Training completed successfully")
        return 0
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
