"""
Lexicon builder for ICU vocabulary using G2P (Grapheme-to-Phoneme) conversion.

This module builds a pronunciation lexicon from the ICU vocabulary defined in
config.yaml using the g2p-en library for grapheme-to-phoneme conversion.
The lexicon is used for constrained beam search decoding in ICU mode.
"""

import os
import json
import yaml
import logging
from typing import Dict, List, Set
from pathlib import Path

try:
    from g2p_en import G2p
except ImportError:
    raise ImportError("g2p-en is required. Install with: pip install g2p-en")

from .tokens import get_phoneme_vocab

logger = logging.getLogger(__name__)

class LexiconBuilder:
    """
    Builds pronunciation lexicon from ICU vocabulary using G2P conversion.
    """
    
    def __init__(self, config_path: str = None):
        """
        Initialize lexicon builder.
        
        Args:
            config_path: Path to configuration file
        """
        if config_path is None:
            config_path = Path(__file__).parent / "config.yaml"
        
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Initialize G2P converter
        self.g2p = G2p()
        
        # Get phoneme vocabulary
        self.phoneme_tokens, self.phoneme_to_idx, self.idx_to_phoneme = get_phoneme_vocab()
        self.valid_phonemes = set(self.phoneme_tokens[1:])  # Exclude blank token
        
        logger.info("Lexicon builder initialized")
    
    def word_to_phonemes(self, word: str) -> List[str]:
        """
        Convert word to phoneme sequence using G2P.
        
        Args:
            word: Input word (e.g., "PAIN")
            
        Returns:
            List of phonemes in ARPAbet format
        """
        # Convert to lowercase for G2P
        word_lower = word.lower()
        
        # Get phonemes from G2P
        phonemes = self.g2p(word_lower)
        
        # Filter out stress markers and invalid phonemes
        filtered_phonemes = []
        for phoneme in phonemes:
            # Remove stress markers (0, 1, 2)
            clean_phoneme = ''.join(c for c in phoneme if not c.isdigit())
            
            # Only keep valid ARPAbet phonemes
            if clean_phoneme in self.valid_phonemes:
                filtered_phonemes.append(clean_phoneme)
            else:
                logger.warning(f"Unknown phoneme '{clean_phoneme}' for word '{word}', skipping")
        
        return filtered_phonemes
    
    def build_lexicon(self) -> Dict[str, List[str]]:
        """
        Build lexicon from ICU vocabulary.
        
        Returns:
            Dictionary mapping words to phoneme sequences
        """
        lexicon = {}
        vocabulary = self.config.get('icu_vocabulary', [])
        
        logger.info(f"Building lexicon for {len(vocabulary)} words")
        
        for word in vocabulary:
            try:
                phonemes = self.word_to_phonemes(word)
                if phonemes:  # Only add if we got valid phonemes
                    lexicon[word] = phonemes
                    logger.debug(f"'{word}' -> {' '.join(phonemes)}")
                else:
                    logger.warning(f"No valid phonemes found for word '{word}'")
            except Exception as e:
                logger.error(f"Error processing word '{word}': {e}")
        
        logger.info(f"Built lexicon with {len(lexicon)} entries")
        return lexicon
    
    def save_lexicon(self, lexicon: Dict[str, List[str]], output_path: str):
        """
        Save lexicon to JSON file.
        
        Args:
            lexicon: Dictionary mapping words to phoneme sequences
            output_path: Output file path
        """
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Convert to format suitable for JSON serialization
        lexicon_json = {
            word: {
                'phonemes': phonemes,
                'phoneme_string': ' '.join(phonemes)
            }
            for word, phonemes in lexicon.items()
        }
        
        with open(output_path, 'w') as f:
            json.dump(lexicon_json, f, indent=2)
        
        logger.info(f"Lexicon saved to {output_path}")
    
    def load_lexicon(self, lexicon_path: str) -> Dict[str, List[str]]:
        """
        Load lexicon from JSON file.
        
        Args:
            lexicon_path: Path to lexicon file
            
        Returns:
            Dictionary mapping words to phoneme sequences
        """
        with open(lexicon_path, 'r') as f:
            lexicon_json = json.load(f)
        
        # Convert back to simple format
        lexicon = {
            word: data['phonemes']
            for word, data in lexicon_json.items()
        }
        
        logger.info(f"Loaded lexicon with {len(lexicon)} entries from {lexicon_path}")
        return lexicon
    
    def validate_lexicon(self, lexicon: Dict[str, List[str]]) -> bool:
        """
        Validate lexicon entries.
        
        Args:
            lexicon: Dictionary mapping words to phoneme sequences
            
        Returns:
            True if lexicon is valid
        """
        valid = True
        
        for word, phonemes in lexicon.items():
            # Check if word is non-empty
            if not word.strip():
                logger.error(f"Empty word in lexicon")
                valid = False
                continue
            
            # Check if phonemes list is non-empty
            if not phonemes:
                logger.error(f"Empty phoneme sequence for word '{word}'")
                valid = False
                continue
            
            # Check if all phonemes are valid
            for phoneme in phonemes:
                if phoneme not in self.valid_phonemes:
                    logger.error(f"Invalid phoneme '{phoneme}' for word '{word}'")
                    valid = False
        
        if valid:
            logger.info("Lexicon validation passed")
        else:
            logger.error("Lexicon validation failed")
        
        return valid
    
    def print_lexicon_stats(self, lexicon: Dict[str, List[str]]):
        """
        Print lexicon statistics.
        
        Args:
            lexicon: Dictionary mapping words to phoneme sequences
        """
        if not lexicon:
            logger.info("Lexicon is empty")
            return
        
        # Calculate statistics
        num_words = len(lexicon)
        phoneme_lengths = [len(phonemes) for phonemes in lexicon.values()]
        avg_length = sum(phoneme_lengths) / len(phoneme_lengths)
        min_length = min(phoneme_lengths)
        max_length = max(phoneme_lengths)
        
        # Count unique phonemes used
        all_phonemes = set()
        for phonemes in lexicon.values():
            all_phonemes.update(phonemes)
        
        logger.info(f"Lexicon Statistics:")
        logger.info(f"  Total words: {num_words}")
        logger.info(f"  Average phonemes per word: {avg_length:.1f}")
        logger.info(f"  Min phonemes per word: {min_length}")
        logger.info(f"  Max phonemes per word: {max_length}")
        logger.info(f"  Unique phonemes used: {len(all_phonemes)}")
        logger.info(f"  Phonemes: {sorted(all_phonemes)}")

def main():
    """
    Main function to build and save lexicon.
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Build ICU lexicon from vocabulary")
    parser.add_argument("--config", type=str, help="Path to config file")
    parser.add_argument("--output", type=str, default="artifacts/lexicon/icu_lexicon.json",
                       help="Output lexicon file path")
    parser.add_argument("--validate", action="store_true", help="Validate lexicon after building")
    parser.add_argument("--stats", action="store_true", help="Print lexicon statistics")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        # Initialize builder
        builder = LexiconBuilder(args.config)
        
        # Build lexicon
        lexicon = builder.build_lexicon()
        
        if not lexicon:
            logger.error("Failed to build lexicon - no valid entries")
            return 1
        
        # Validate if requested
        if args.validate:
            if not builder.validate_lexicon(lexicon):
                logger.error("Lexicon validation failed")
                return 1
        
        # Print statistics if requested
        if args.stats:
            builder.print_lexicon_stats(lexicon)
        
        # Save lexicon
        builder.save_lexicon(lexicon, args.output)
        
        logger.info("Lexicon building completed successfully")
        return 0
        
    except Exception as e:
        logger.error(f"Error building lexicon: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
