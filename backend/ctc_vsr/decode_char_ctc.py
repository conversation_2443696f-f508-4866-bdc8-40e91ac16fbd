"""
Character-based CTC decoder for open vocabulary Visual Speech Recognition.

This module implements a character-level CTC decoder using pyctcdecode
for open vocabulary recognition with optional language model support.
"""

import logging
import numpy as np
from typing import List, Tuple, Optional, Dict

try:
    from pyctcdecode import build_ctcdecoder
except ImportError:
    build_ctcdecoder = None
    logging.warning("pyctcdecode not available. Install with: pip install pyctcdecode")

from .tokens import get_character_vocab

logger = logging.getLogger(__name__)

class CharacterCtcDecoder:
    """
    Character-level CTC decoder using pyctcdecode.
    """
    
    def __init__(
        self,
        beam_width: int = 10,
        language_model_path: Optional[str] = None,
        alpha: float = 0.5,
        beta: float = 1.0
    ):
        """
        Initialize character CTC decoder.
        
        Args:
            beam_width: Beam search width
            language_model_path: Path to KenLM language model (optional)
            alpha: Language model weight
            beta: Word insertion bonus
        """
        self.beam_width = beam_width
        self.language_model_path = language_model_path
        self.alpha = alpha
        self.beta = beta
        
        # Get character vocabulary
        self.char_tokens, self.char_to_idx, self.idx_to_char = get_character_vocab()
        
        # Build decoder
        self.decoder = self._build_decoder()
        
        logger.info(f"Character CTC decoder initialized with beam_width={beam_width}, "
                   f"vocab_size={len(self.char_tokens)}")
    
    def _build_decoder(self):
        """
        Build pyctcdecode decoder.
        
        Returns:
            CTC decoder instance
        """
        if build_ctcdecoder is None:
            logger.error("pyctcdecode not available. Cannot build decoder.")
            return None
        
        # Create labels list (excluding blank token)
        labels = self.char_tokens[1:]  # Skip blank token at index 0
        
        try:
            if self.language_model_path:
                # Build decoder with language model
                decoder = build_ctcdecoder(
                    labels=labels,
                    kenlm_model_path=self.language_model_path,
                    alpha=self.alpha,
                    beta=self.beta
                )
                logger.info(f"Built decoder with language model: {self.language_model_path}")
            else:
                # Build decoder without language model
                decoder = build_ctcdecoder(labels=labels)
                logger.info("Built decoder without language model")
            
            return decoder
            
        except Exception as e:
            logger.error(f"Failed to build decoder: {e}")
            return None
    
    def decode_greedy(self, log_probs: np.ndarray) -> Tuple[str, float]:
        """
        Greedy CTC decoding (fallback when pyctcdecode not available).
        
        Args:
            log_probs: CTC log probabilities (seq_len, vocab_size)
            
        Returns:
            Tuple of (decoded_text, confidence_score)
        """
        # Get most likely tokens at each timestep
        predicted = np.argmax(log_probs, axis=-1)
        
        # Remove consecutive duplicates and blank tokens
        decoded_indices = []
        prev_token = None
        
        for token in predicted:
            if token != 0 and token != prev_token:  # 0 is blank token
                decoded_indices.append(token)
            prev_token = token
        
        # Convert indices to characters
        chars = []
        for idx in decoded_indices:
            if idx in self.idx_to_char:
                chars.append(self.idx_to_char[idx])
        
        decoded_text = ''.join(chars)
        
        # Calculate confidence score (average probability of predicted tokens)
        probs = np.exp(log_probs)
        confidence = np.mean(np.max(probs, axis=-1))
        
        return decoded_text, confidence
    
    def decode_beam_search(
        self,
        log_probs: np.ndarray,
        return_top_k: int = 5
    ) -> List[Tuple[str, float]]:
        """
        Beam search CTC decoding using pyctcdecode.
        
        Args:
            log_probs: CTC log probabilities (seq_len, vocab_size)
            return_top_k: Number of top hypotheses to return
            
        Returns:
            List of (decoded_text, confidence_score) tuples
        """
        if self.decoder is None:
            # Fallback to greedy decoding
            logger.warning("Decoder not available, using greedy decoding")
            text, confidence = self.decode_greedy(log_probs)
            return [(text, confidence)]
        
        try:
            # Convert log probabilities to the format expected by pyctcdecode
            # pyctcdecode expects (seq_len, vocab_size) with blank at index 0
            
            # Decode with beam search
            beam_results = self.decoder.decode_beams(
                log_probs,
                beam_width=self.beam_width
            )
            
            # Extract top-k results
            hypotheses = []
            for i, (text, score) in enumerate(beam_results[:return_top_k]):
                # Convert score to confidence (normalize by length)
                confidence = np.exp(score / max(1, len(text.split())))
                hypotheses.append((text.strip(), confidence))
            
            return hypotheses
            
        except Exception as e:
            logger.error(f"Beam search decoding failed: {e}")
            # Fallback to greedy decoding
            text, confidence = self.decode_greedy(log_probs)
            return [(text, confidence)]
    
    def decode(
        self,
        log_probs: np.ndarray,
        use_beam_search: bool = True,
        return_top_k: int = 5
    ) -> List[Tuple[str, float]]:
        """
        Decode CTC log probabilities.
        
        Args:
            log_probs: CTC log probabilities (seq_len, vocab_size)
            use_beam_search: Whether to use beam search (vs greedy)
            return_top_k: Number of top hypotheses to return
            
        Returns:
            List of (decoded_text, confidence_score) tuples
        """
        if use_beam_search and self.decoder is not None:
            return self.decode_beam_search(log_probs, return_top_k)
        else:
            text, confidence = self.decode_greedy(log_probs)
            return [(text, confidence)]
    
    def get_vocab_info(self) -> Dict:
        """Get vocabulary information."""
        return {
            'vocab_size': len(self.char_tokens),
            'tokens': self.char_tokens,
            'has_language_model': self.language_model_path is not None,
            'beam_width': self.beam_width
        }

class SimpleCharacterDecoder:
    """
    Simple character decoder without external dependencies.
    
    This is a fallback implementation that doesn't require pyctcdecode.
    """
    
    def __init__(self, beam_width: int = 10):
        """
        Initialize simple character decoder.
        
        Args:
            beam_width: Beam search width
        """
        self.beam_width = beam_width
        
        # Get character vocabulary
        self.char_tokens, self.char_to_idx, self.idx_to_char = get_character_vocab()
        
        logger.info(f"Simple character decoder initialized with beam_width={beam_width}")
    
    def decode_greedy(self, log_probs: np.ndarray) -> Tuple[str, float]:
        """
        Greedy CTC decoding.
        
        Args:
            log_probs: CTC log probabilities (seq_len, vocab_size)
            
        Returns:
            Tuple of (decoded_text, confidence_score)
        """
        # Get most likely tokens at each timestep
        predicted = np.argmax(log_probs, axis=-1)
        
        # Remove consecutive duplicates and blank tokens
        decoded_indices = []
        prev_token = None
        
        for token in predicted:
            if token != 0 and token != prev_token:  # 0 is blank token
                decoded_indices.append(token)
            prev_token = token
        
        # Convert indices to characters
        chars = []
        for idx in decoded_indices:
            if idx in self.idx_to_char:
                chars.append(self.idx_to_char[idx])
        
        decoded_text = ''.join(chars)
        
        # Calculate confidence score
        probs = np.exp(log_probs)
        confidence = np.mean(np.max(probs, axis=-1))
        
        return decoded_text, confidence
    
    def decode_beam_search(
        self,
        log_probs: np.ndarray,
        return_top_k: int = 5
    ) -> List[Tuple[str, float]]:
        """
        Simple beam search implementation.
        
        Args:
            log_probs: CTC log probabilities (seq_len, vocab_size)
            return_top_k: Number of top hypotheses to return
            
        Returns:
            List of (decoded_text, confidence_score) tuples
        """
        seq_len, vocab_size = log_probs.shape
        
        # Initialize beam with empty sequence
        beam = [{'sequence': [], 'log_prob': 0.0, 'last_token': 0}]
        
        for t in range(seq_len):
            new_beam = []
            
            for state in beam:
                # Get top tokens for this timestep
                top_tokens = np.argsort(log_probs[t])[-self.beam_width:]
                
                for token_idx in top_tokens:
                    token_log_prob = log_probs[t, token_idx]
                    new_log_prob = state['log_prob'] + token_log_prob
                    
                    # Skip if same as last token (CTC collapse)
                    if token_idx == state['last_token']:
                        new_beam.append({
                            'sequence': state['sequence'].copy(),
                            'log_prob': new_log_prob,
                            'last_token': token_idx
                        })
                        continue
                    
                    # Handle blank token
                    if token_idx == 0:
                        new_beam.append({
                            'sequence': state['sequence'].copy(),
                            'log_prob': new_log_prob,
                            'last_token': token_idx
                        })
                        continue
                    
                    # Handle character token
                    if token_idx in self.idx_to_char:
                        new_sequence = state['sequence'] + [token_idx]
                        new_beam.append({
                            'sequence': new_sequence,
                            'log_prob': new_log_prob,
                            'last_token': token_idx
                        })
            
            # Prune beam
            if len(new_beam) > self.beam_width:
                new_beam.sort(key=lambda x: x['log_prob'], reverse=True)
                beam = new_beam[:self.beam_width]
            else:
                beam = new_beam
        
        # Convert to text and calculate confidence
        hypotheses = []
        for state in beam:
            chars = [self.idx_to_char[idx] for idx in state['sequence'] 
                    if idx in self.idx_to_char]
            text = ''.join(chars)
            confidence = np.exp(state['log_prob'] / max(1, len(text)))
            hypotheses.append((text, confidence))
        
        # Sort by confidence and return top-k
        hypotheses.sort(key=lambda x: x[1], reverse=True)
        return hypotheses[:return_top_k]
    
    def decode(
        self,
        log_probs: np.ndarray,
        use_beam_search: bool = True,
        return_top_k: int = 5
    ) -> List[Tuple[str, float]]:
        """
        Decode CTC log probabilities.
        
        Args:
            log_probs: CTC log probabilities (seq_len, vocab_size)
            use_beam_search: Whether to use beam search (vs greedy)
            return_top_k: Number of top hypotheses to return
            
        Returns:
            List of (decoded_text, confidence_score) tuples
        """
        if use_beam_search:
            return self.decode_beam_search(log_probs, return_top_k)
        else:
            text, confidence = self.decode_greedy(log_probs)
            return [(text, confidence)]

def create_character_decoder(
    beam_width: int = 10,
    language_model_path: Optional[str] = None,
    use_simple: bool = False
) -> object:
    """
    Factory function to create character decoder.
    
    Args:
        beam_width: Beam search width
        language_model_path: Path to language model (optional)
        use_simple: Force use of simple decoder
        
    Returns:
        Character decoder instance
    """
    if use_simple or build_ctcdecoder is None:
        return SimpleCharacterDecoder(beam_width=beam_width)
    else:
        return CharacterCtcDecoder(
            beam_width=beam_width,
            language_model_path=language_model_path
        )

if __name__ == "__main__":
    # Test character decoder
    import numpy as np
    
    # Create decoder
    decoder = create_character_decoder(beam_width=5)
    
    # Test with dummy log probabilities
    seq_len = 15
    vocab_size = len(decoder.char_tokens) if hasattr(decoder, 'char_tokens') else 28
    log_probs = np.random.randn(seq_len, vocab_size)
    log_probs = log_probs - np.log(np.sum(np.exp(log_probs), axis=1, keepdims=True))
    
    # Test greedy decoding
    if hasattr(decoder, 'decode_greedy'):
        text, confidence = decoder.decode_greedy(log_probs)
        print(f"Greedy: '{text}' (confidence: {confidence:.3f})")
    
    # Test beam search decoding
    hypotheses = decoder.decode(log_probs, use_beam_search=True, return_top_k=3)
    
    print("Beam search results:")
    for i, (text, confidence) in enumerate(hypotheses):
        print(f"  {i+1}. '{text}' (confidence: {confidence:.3f})")
    
    print("Character decoder test completed successfully!")
