"""
Token mappings for CTC-based Visual Speech Recognition.

This module defines the token vocabularies for both phoneme and character-based
CTC models. The blank token is always at index 0 for CTC compatibility.

ARPAbet phoneme set is used for phoneme-based recognition, and standard
English characters (A-Z + space) are used for character-based recognition.
"""

import logging
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)

# ARPAbet phoneme set (CMU pronunciation dictionary standard)
# Blank token must be at index 0 for CTC
PHONEME_TOKENS = [
    '<blank>',  # Index 0 - CTC blank token
    
    # Vowels
    'AA',  # odd     AA D
    'AE',  # at      AE T
    'AH',  # hut     HH AH T
    'AO',  # ought   AO T
    'AW',  # cow     K AW
    'AY',  # hide    HH AY D
    'EH',  # Ed      EH D
    'ER',  # hurt    HH ER T
    'EY',  # ate     EY T
    'IH',  # it      IH T
    'IY',  # eat     IY T
    'OW',  # oat     OW T
    'OY',  # toy     T OY
    'UH',  # hood    HH UH D
    'UW',  # two     T UW
    
    # Consonants
    'B',   # be      B IY
    'CH',  # cheese  CH IY Z
    'D',   # dee     D IY
    'DH',  # thee    DH IY
    'F',   # fee     F IY
    'G',   # green   G R IY N
    'HH',  # he      HH IY
    'JH',  # gee     JH IY
    'K',   # key     K IY
    'L',   # lee     L IY
    'M',   # me      M IY
    'N',   # knee    N IY
    'NG',  # ping    P IH NG
    'P',   # pee     P IY
    'R',   # read    R IY D
    'S',   # sea     S IY
    'SH',  # she     SH IY
    'T',   # tea     T IY
    'TH',  # theta   TH EY T AH
    'V',   # vee     V IY
    'W',   # we      W IY
    'Y',   # yield   Y IY L D
    'Z',   # zee     Z IY
    'ZH',  # seizure S IY ZH ER
]

# Character tokens for open vocabulary recognition
# Blank token must be at index 0 for CTC
CHARACTER_TOKENS = [
    '<blank>',  # Index 0 - CTC blank token
    ' ',        # Space character
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
    'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
    'U', 'V', 'W', 'X', 'Y', 'Z'
]

def get_phoneme_vocab() -> Tuple[List[str], Dict[str, int], Dict[int, str]]:
    """
    Get phoneme vocabulary mappings.
    
    Returns:
        Tuple containing:
        - tokens: List of phoneme tokens
        - token_to_idx: Mapping from token to index
        - idx_to_token: Mapping from index to token
    """
    tokens = PHONEME_TOKENS.copy()
    token_to_idx = {token: idx for idx, token in enumerate(tokens)}
    idx_to_token = {idx: token for idx, token in enumerate(tokens)}
    
    logger.info(f"Phoneme vocabulary size: {len(tokens)}")
    return tokens, token_to_idx, idx_to_token

def get_character_vocab() -> Tuple[List[str], Dict[str, int], Dict[int, str]]:
    """
    Get character vocabulary mappings.
    
    Returns:
        Tuple containing:
        - tokens: List of character tokens
        - token_to_idx: Mapping from token to index
        - idx_to_token: Mapping from index to token
    """
    tokens = CHARACTER_TOKENS.copy()
    token_to_idx = {token: idx for idx, token in enumerate(tokens)}
    idx_to_token = {idx: token for idx, token in enumerate(tokens)}
    
    logger.info(f"Character vocabulary size: {len(tokens)}")
    return tokens, token_to_idx, idx_to_token

def phoneme_sequence_to_indices(phoneme_sequence: str, token_to_idx: Dict[str, int]) -> List[int]:
    """
    Convert phoneme sequence string to list of indices.
    
    Args:
        phoneme_sequence: Space-separated phoneme sequence (e.g., "P EY N")
        token_to_idx: Mapping from phoneme token to index
        
    Returns:
        List of phoneme indices
        
    Raises:
        ValueError: If unknown phoneme encountered
    """
    phonemes = phoneme_sequence.strip().split()
    indices = []
    
    for phoneme in phonemes:
        if phoneme not in token_to_idx:
            raise ValueError(f"Unknown phoneme: {phoneme}")
        indices.append(token_to_idx[phoneme])
    
    return indices

def character_sequence_to_indices(text: str, token_to_idx: Dict[str, int]) -> List[int]:
    """
    Convert text string to list of character indices.
    
    Args:
        text: Input text (e.g., "PAIN")
        token_to_idx: Mapping from character token to index
        
    Returns:
        List of character indices
        
    Raises:
        ValueError: If unknown character encountered
    """
    text = text.upper().strip()
    indices = []
    
    for char in text:
        if char not in token_to_idx:
            raise ValueError(f"Unknown character: {char}")
        indices.append(token_to_idx[char])
    
    return indices

def indices_to_phoneme_sequence(indices: List[int], idx_to_token: Dict[int, str]) -> str:
    """
    Convert list of indices to phoneme sequence string.
    
    Args:
        indices: List of phoneme indices
        idx_to_token: Mapping from index to phoneme token
        
    Returns:
        Space-separated phoneme sequence
    """
    phonemes = []
    for idx in indices:
        if idx in idx_to_token and idx_to_token[idx] != '<blank>':
            phonemes.append(idx_to_token[idx])
    
    return ' '.join(phonemes)

def indices_to_character_sequence(indices: List[int], idx_to_token: Dict[int, str]) -> str:
    """
    Convert list of indices to character sequence string.
    
    Args:
        indices: List of character indices
        idx_to_token: Mapping from index to character token
        
    Returns:
        Character sequence string
    """
    chars = []
    for idx in indices:
        if idx in idx_to_token and idx_to_token[idx] != '<blank>':
            chars.append(idx_to_token[idx])
    
    return ''.join(chars)

# Vocabulary sizes for model initialization
PHONEME_VOCAB_SIZE = len(PHONEME_TOKENS)
CHARACTER_VOCAB_SIZE = len(CHARACTER_TOKENS)

if __name__ == "__main__":
    # Test the token mappings
    print("Phoneme vocabulary:")
    phoneme_tokens, phoneme_to_idx, idx_to_phoneme = get_phoneme_vocab()
    print(f"Size: {len(phoneme_tokens)}")
    print(f"Sample: {phoneme_tokens[:10]}")
    
    print("\nCharacter vocabulary:")
    char_tokens, char_to_idx, idx_to_char = get_character_vocab()
    print(f"Size: {len(char_tokens)}")
    print(f"Sample: {char_tokens[:10]}")
    
    # Test conversions
    print("\nTesting phoneme conversion:")
    phoneme_seq = "P EY N"
    indices = phoneme_sequence_to_indices(phoneme_seq, phoneme_to_idx)
    reconstructed = indices_to_phoneme_sequence(indices, idx_to_phoneme)
    print(f"Original: {phoneme_seq}")
    print(f"Indices: {indices}")
    print(f"Reconstructed: {reconstructed}")
    
    print("\nTesting character conversion:")
    text = "PAIN"
    indices = character_sequence_to_indices(text, char_to_idx)
    reconstructed = indices_to_character_sequence(indices, idx_to_char)
    print(f"Original: {text}")
    print(f"Indices: {indices}")
    print(f"Reconstructed: {reconstructed}")
