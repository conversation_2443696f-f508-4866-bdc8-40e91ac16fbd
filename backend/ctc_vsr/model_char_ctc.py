"""
Character-based CTC model for Visual Speech Recognition.

This module implements a CTC model for character recognition using a shared
visual encoder and BiLSTM layers with a CTC head for English characters.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
from typing import Dict, Tuple, Optional

from .visual_encoder import make_visual_encoder
from .tokens import CHARACTER_VOCAB_SIZE, get_character_vocab

logger = logging.getLogger(__name__)

class CharCtcModel(nn.Module):
    """
    CTC model for character recognition.
    
    Architecture:
    - Visual encoder (ResNet3D-18 + projection)
    - Bidirectional LSTM layers
    - Linear projection to character vocabulary
    - CTC loss for training
    """
    
    def __init__(
        self,
        config: Dict,
        encoder_type: str = "simple"
    ):
        """
        Initialize character CTC model.
        
        Args:
            config: Configuration dictionary
            encoder_type: Type of visual encoder ("simple" or "temporal")
        """
        super().__init__()
        
        self.config = config
        model_config = config['model']
        
        # Model parameters
        self.feat_dim = model_config['feat_dim']
        self.hidden_dim = model_config['hidden_dim']
        self.num_layers = model_config['num_layers']
        self.dropout = model_config['dropout']
        self.bidirectional = model_config.get('bidirectional', True)
        
        # Vocabulary
        self.vocab_size = CHARACTER_VOCAB_SIZE
        self.char_tokens, self.char_to_idx, self.idx_to_char = get_character_vocab()
        
        # Visual encoder (shared component)
        self.visual_encoder = make_visual_encoder(
            feat_dim=self.feat_dim,
            pretrained=True,
            encoder_type=encoder_type,
            dropout=self.dropout
        )
        
        # LSTM layers for temporal modeling
        self.lstm = nn.LSTM(
            input_size=self.feat_dim,
            hidden_size=self.hidden_dim,
            num_layers=self.num_layers,
            dropout=self.dropout if self.num_layers > 1 else 0,
            bidirectional=self.bidirectional,
            batch_first=True
        )
        
        # Calculate LSTM output dimension
        lstm_output_dim = self.hidden_dim * (2 if self.bidirectional else 1)
        
        # CTC head for character classification
        self.ctc_head = nn.Sequential(
            nn.Linear(lstm_output_dim, lstm_output_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(self.dropout),
            nn.Linear(lstm_output_dim // 2, self.vocab_size)
        )
        
        # CTC loss function
        self.ctc_loss = nn.CTCLoss(
            blank=0,  # Blank token is at index 0
            zero_infinity=True,
            reduction='mean'
        )
        
        self._init_weights()
        
        logger.info(f"Character CTC model initialized with vocab_size={self.vocab_size}, "
                   f"hidden_dim={self.hidden_dim}, num_layers={self.num_layers}")
    
    def _init_weights(self):
        """Initialize model weights."""
        # Initialize LSTM weights
        for name, param in self.lstm.named_parameters():
            if 'weight_ih' in name:
                nn.init.xavier_uniform_(param.data)
            elif 'weight_hh' in name:
                nn.init.orthogonal_(param.data)
            elif 'bias' in name:
                nn.init.constant_(param.data, 0)
        
        # Initialize CTC head weights
        for module in self.ctc_head.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(
        self,
        videos: torch.Tensor,
        targets: Optional[torch.Tensor] = None,
        target_lengths: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass through character CTC model.
        
        Args:
            videos: Video tensor (batch_size, channels, frames, height, width)
            targets: Target character sequences (batch_size, max_target_length)
            target_lengths: Actual target lengths (batch_size,)
            
        Returns:
            Dictionary containing:
            - log_probs: CTC log probabilities (seq_len, batch_size, vocab_size)
            - output_lengths: Output sequence lengths (batch_size,)
            - loss: CTC loss (if targets provided)
        """
        batch_size = videos.size(0)
        
        # Extract visual features
        # Shape: (batch_size, sequence_length, feat_dim)
        visual_features = self.visual_encoder(videos)
        
        # LSTM temporal modeling
        # Shape: (batch_size, sequence_length, lstm_output_dim)
        lstm_output, _ = self.lstm(visual_features)
        
        # CTC head for character classification
        # Shape: (batch_size, sequence_length, vocab_size)
        logits = self.ctc_head(lstm_output)
        
        # Apply log softmax for CTC
        log_probs = F.log_softmax(logits, dim=-1)
        
        # Transpose for CTC loss (seq_len, batch_size, vocab_size)
        log_probs_ctc = log_probs.transpose(0, 1)
        
        # Output sequence lengths (all sequences have same length after encoding)
        output_lengths = torch.full((batch_size,), log_probs_ctc.size(0), 
                                  dtype=torch.long, device=videos.device)
        
        result = {
            'log_probs': log_probs_ctc,
            'output_lengths': output_lengths,
            'logits': logits  # Keep for inference
        }
        
        # Calculate CTC loss if targets provided
        if targets is not None and target_lengths is not None:
            # Flatten targets and remove padding
            targets_flat = []
            for i, length in enumerate(target_lengths):
                targets_flat.extend(targets[i][:length].tolist())
            targets_flat = torch.tensor(targets_flat, dtype=torch.long, device=videos.device)
            
            loss = self.ctc_loss(
                log_probs_ctc,
                targets_flat,
                output_lengths,
                target_lengths
            )
            result['loss'] = loss
        
        return result
    
    def decode_greedy(self, log_probs: torch.Tensor) -> Tuple[str, float]:
        """
        Greedy CTC decoding.
        
        Args:
            log_probs: Log probabilities (seq_len, vocab_size)
            
        Returns:
            Tuple of (decoded_text, confidence_score)
        """
        # Get most likely tokens at each timestep
        _, predicted = torch.max(log_probs, dim=-1)
        predicted = predicted.cpu().numpy()
        
        # Remove consecutive duplicates and blank tokens
        decoded_indices = []
        prev_token = None
        
        for token in predicted:
            if token != 0 and token != prev_token:  # 0 is blank token
                decoded_indices.append(token)
            prev_token = token
        
        # Convert indices to characters
        chars = []
        for idx in decoded_indices:
            if idx in self.idx_to_char:
                chars.append(self.idx_to_char[idx])
        
        decoded_text = ''.join(chars)
        
        # Calculate confidence score (average probability of predicted tokens)
        probs = torch.exp(log_probs)
        confidence = torch.mean(torch.max(probs, dim=-1)[0]).item()
        
        return decoded_text, confidence
    
    def get_vocab_size(self) -> int:
        """Get vocabulary size."""
        return self.vocab_size
    
    def get_character_tokens(self) -> Dict:
        """Get character token mappings."""
        return {
            'tokens': self.char_tokens,
            'token_to_idx': self.char_to_idx,
            'idx_to_token': self.idx_to_char
        }

class CharCtcTrainer:
    """
    Trainer class for character CTC model.
    """
    
    def __init__(
        self,
        model: CharCtcModel,
        config: Dict,
        device: torch.device
    ):
        """
        Initialize trainer.
        
        Args:
            model: Character CTC model
            config: Configuration dictionary
            device: Training device
        """
        self.model = model
        self.config = config
        self.device = device
        
        training_config = config['training']
        
        # Optimizer
        self.optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=training_config['learning_rate'],
            weight_decay=training_config['weight_decay']
        )
        
        # Learning rate scheduler
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=training_config.get('patience', 3),
            min_lr=1e-6
        )
        
        # Gradient clipping
        self.grad_clip = training_config.get('grad_clip', 1.0)
        
        logger.info("Character CTC trainer initialized")
    
    def train_step(self, batch: Dict[str, torch.Tensor]) -> float:
        """
        Single training step.
        
        Args:
            batch: Batch dictionary with videos, targets, target_lengths
            
        Returns:
            Loss value
        """
        self.model.train()
        self.optimizer.zero_grad()
        
        # Move batch to device
        videos = batch['videos'].to(self.device)
        targets = batch['targets'].to(self.device)
        target_lengths = batch['target_lengths'].to(self.device)
        
        # Forward pass
        outputs = self.model(videos, targets, target_lengths)
        loss = outputs['loss']
        
        # Backward pass
        loss.backward()
        
        # Gradient clipping
        if self.grad_clip > 0:
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.grad_clip)
        
        self.optimizer.step()
        
        return loss.item()
    
    def validate_step(self, batch: Dict[str, torch.Tensor]) -> Tuple[float, float]:
        """
        Single validation step.
        
        Args:
            batch: Batch dictionary
            
        Returns:
            Tuple of (loss, accuracy)
        """
        self.model.eval()
        
        with torch.no_grad():
            # Move batch to device
            videos = batch['videos'].to(self.device)
            targets = batch['targets'].to(self.device)
            target_lengths = batch['target_lengths'].to(self.device)
            
            # Forward pass
            outputs = self.model(videos, targets, target_lengths)
            loss = outputs['loss']
            
            # Calculate accuracy (simplified - could use edit distance)
            log_probs = outputs['log_probs']
            batch_size = videos.size(0)
            correct = 0
            
            for i in range(batch_size):
                decoded_text, _ = self.model.decode_greedy(log_probs[:, i, :])
                # This is a simplified accuracy calculation
                # In practice, you'd want to use edit distance or similar metrics
                correct += 1 if len(decoded_text.strip()) > 0 else 0
            
            accuracy = correct / batch_size
        
        return loss.item(), accuracy
    
    def step_scheduler(self, val_loss: float):
        """Step learning rate scheduler."""
        self.scheduler.step(val_loss)

if __name__ == "__main__":
    # Test character CTC model
    import yaml
    from pathlib import Path
    
    # Load config
    config_path = Path(__file__).parent / "config.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Create model
    model = CharCtcModel(config)
    model.to(device)
    
    print(f"Model vocabulary size: {model.get_vocab_size()}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Test forward pass
    batch_size = 2
    videos = torch.randn(batch_size, 1, 64, 112, 112).to(device)
    targets = torch.tensor([[1, 2, 3], [4, 5, 0]], dtype=torch.long).to(device)
    target_lengths = torch.tensor([3, 2], dtype=torch.long).to(device)
    
    with torch.no_grad():
        outputs = model(videos, targets, target_lengths)
        print(f"Log probs shape: {outputs['log_probs'].shape}")
        print(f"Output lengths: {outputs['output_lengths']}")
        
        # Test greedy decoding
        decoded_text, confidence = model.decode_greedy(outputs['log_probs'][:, 0, :])
        print(f"Decoded text: '{decoded_text}'")
        print(f"Confidence: {confidence:.3f}")
    
    print("Character CTC model test completed successfully!")
