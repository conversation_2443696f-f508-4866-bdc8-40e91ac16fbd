# CTC-based Visual Speech Recognition (VSR) Pipeline

This module provides a complete CTC-based Visual Speech Recognition implementation with two complementary recognition modes for the ICU-Lipreading MVP project.

## Overview

The CTC VSR pipeline offers two recognition modes:

1. **ICU Mode (Phoneme-CTC + Constrained Lexicon)**: High-accuracy recognition for clinical vocabulary using phoneme-level CTC with beam search over a constrained lexicon of ICU terms and common names.

2. **Open Mode (Character-CTC + Open Vocabulary)**: Broader coverage recognition using character-level CTC with pyctcdecode for names and phrases not in the ICU lexicon.

## Key Features

- **Shared Visual Encoder**: ResNet3D-18 backbone shared between both recognition modes
- **Commercial Licensing**: Uses only MIT/BSD licensed components (no research-only weights)
- **Confidence-based Selection**: Automatic mode selection with fallback mechanisms
- **FastAPI Integration**: RESTful API endpoints for easy integration
- **Comprehensive Training**: Support for both phoneme and character model training
- **Robust Evaluation**: CER/WER metrics and confidence scoring

## Architecture

```
Video Input (MP4/NPY)
        ↓
Visual Encoder (ResNet3D-18)
        ↓
   Feature Sequence
        ↓
    ┌─────────────────┐
    │   ICU Mode      │    │   Open Mode     │
    │ Phoneme CTC     │    │ Character CTC   │
    │ + Lexicon       │    │ + pyctcdecode   │
    │ Beam Search     │    │ Open Vocab      │
    └─────────────────┘    └─────────────────┘
        ↓                          ↓
   Confidence ≥ 0.65?         Confidence ≥ 0.55?
        ↓                          ↓
    Return Result              Return Result
                    ↓
              "PLEASE REPEAT"
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Build ICU lexicon:
```bash
python -m backend.ctc_vsr.build_lexicon --output artifacts/lexicon/icu_lexicon.json
```

## Quick Start

### Training Models

1. **Prepare data**: Create JSONL manifest files with video paths and targets:
```json
{"video": "data/roi/vol01_pain_01.npy", "target_phonemes": "P EY N", "target_chars": "PAIN", "speaker": "vol01", "split": "train"}
```

2. **Train phoneme CTC model**:
```bash
python -m backend.ctc_vsr.train_ctc \
    --mode phoneme \
    --train-manifest data/train_manifest.jsonl \
    --val-manifest data/val_manifest.jsonl \
    --output-dir artifacts/training/phoneme
```

3. **Train character CTC model**:
```bash
python -m backend.ctc_vsr.train_ctc \
    --mode character \
    --train-manifest data/train_manifest.jsonl \
    --val-manifest data/val_manifest.jsonl \
    --output-dir artifacts/training/character
```

### Running Inference

#### Python API
```python
from backend.ctc_vsr import CTCInference

# Initialize inference pipeline
inference = CTCInference(
    phoneme_model_path="artifacts/models/phoneme_ctc_best.pt",
    char_model_path="artifacts/models/char_ctc_best.pt",
    lexicon_path="artifacts/lexicon/icu_lexicon.json"
)

# Predict with automatic mode selection
result = inference.predict_best("path/to/video.mp4")
print(f"Prediction: {result['text']} (confidence: {result['confidence']:.3f})")

# Use specific modes
icu_result = inference.predict_icu("path/to/video.mp4")
open_result = inference.predict_open("path/to/video.mp4")
```

#### REST API

Start the server with CTC enabled:
```bash
export VSR_IMPL=ctc
uvicorn backend.api.app:app --host 0.0.0.0 --port 8000 --reload
```

Make predictions:
```bash
# Automatic mode selection
curl -X POST "http://localhost:8000/ctc/predict" -F "file=@test_video.mp4"

# ICU mode only
curl -X POST "http://localhost:8000/ctc/predict_icu" -F "file=@test_video.mp4"

# Open mode only
curl -X POST "http://localhost:8000/ctc/predict_open" -F "file=@test_video.mp4"
```

## Configuration

Edit `backend/ctc_vsr/config.yaml` to customize:

- **Video preprocessing**: Frame count, resolution, grayscale conversion
- **Model architecture**: Feature dimensions, LSTM layers, dropout
- **Training parameters**: Batch size, learning rate, epochs
- **Confidence thresholds**: ICU and open mode thresholds
- **ICU vocabulary**: Clinical terms and common names

## File Structure

```
backend/ctc_vsr/
├── __init__.py              # Module initialization
├── config.yaml             # Configuration file
├── tokens.py               # Phoneme/character token mappings
├── build_lexicon.py        # G2P-based lexicon builder
├── dataset.py              # Video dataset loader
├── visual_encoder.py       # Shared ResNet3D-18 encoder
├── model_phoneme_ctc.py    # Phoneme CTC model
├── model_char_ctc.py       # Character CTC model
├── train_ctc.py            # Unified training script
├── decode_phoneme_lex.py   # Phoneme lexicon decoder
├── decode_char_ctc.py      # Character CTC decoder
├── infer.py                # Unified inference API
├── api_router.py           # FastAPI router
├── metrics.py              # Evaluation metrics
└── README.md               # This file
```

## Environment Variables

Configure the pipeline using environment variables:

- `VSR_IMPL=ctc`: Enable CTC pipeline in main API
- `CTC_CONFIG_PATH`: Path to configuration file
- `CTC_PHONEME_MODEL_PATH`: Path to phoneme model checkpoint
- `CTC_CHAR_MODEL_PATH`: Path to character model checkpoint
- `CTC_LEXICON_PATH`: Path to ICU lexicon file
- `CTC_DEVICE`: Device to use (cpu/cuda/auto)

## API Endpoints

### Health and Info
- `GET /ctc/health`: Health check
- `GET /ctc/info`: Model information

### Prediction
- `POST /ctc/predict`: Automatic mode selection
- `POST /ctc/predict_icu`: ICU mode only
- `POST /ctc/predict_open`: Open mode only
- `POST /ctc/predict_array`: Predict from numpy array

### Response Format
```json
{
  "success": true,
  "text": "PAIN",
  "confidence": 0.87,
  "mode": "icu",
  "selected_mode": "icu",
  "fallback_used": false,
  "word_sequence": ["PAIN"],
  "filename": "test_video.mp4"
}
```

## Training Data Format

Create JSONL manifest files with the following format:

```json
{"video": "data/roi/speaker01_pain_001.npy", "target_phonemes": "P EY N", "target_chars": "PAIN", "speaker": "speaker01", "split": "train", "duration": 2.1}
{"video": "data/roi/speaker02_help_001.mp4", "target_phonemes": "HH EH L P", "target_chars": "HELP", "speaker": "speaker02", "split": "val", "duration": 1.8}
```

Required fields:
- `video`: Path to video file (.mp4, .avi, .mov, .npy)
- `target_phonemes`: Space-separated ARPAbet phonemes
- `target_chars`: Target text in uppercase
- `split`: Data split ("train", "val", "test")

Optional fields:
- `speaker`: Speaker identifier
- `duration`: Video duration in seconds

## Evaluation

Calculate metrics on test data:

```python
from backend.ctc_vsr.metrics import evaluate_predictions

# Evaluate predictions
results = evaluate_predictions(
    predictions=["PAIN", "HELP", "NURSE"],
    references=["PAIN", "HELP", "DOCTOR"],
    confidences=[0.9, 0.8, 0.6]
)

print(f"Mean CER: {results['mean_cer']:.3f}")
print(f"Mean WER: {results['mean_wer']:.3f}")
print(f"Perfect matches: {results['perfect_matches']}")
```

## Troubleshooting

### Common Issues

1. **Models not loading**: Check file paths and ensure models are trained
2. **Low confidence scores**: Adjust thresholds in config.yaml
3. **CUDA out of memory**: Reduce batch size or use CPU
4. **Import errors**: Install missing dependencies

### Debug Mode

Enable verbose logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Performance Optimization

- Use GPU for training and inference when available
- Adjust batch size based on available memory
- Use temporal visual encoder for better sequence modeling
- Fine-tune confidence thresholds based on your data

## License

This module uses only commercially-licensed components:
- PyTorch/torchvision (BSD)
- pyctcdecode (MIT)
- g2p-en (MIT)
- FastAPI (MIT)

No research-only model weights or GPL-licensed code is included.
