"""
Evaluation metrics for CTC-based Visual Speech Recognition.

This module provides functions to calculate Character Error Rate (CER),
Word Error Rate (WER), and confidence scoring metrics for VSR evaluation.
"""

import logging
from typing import List, Tuple, Dict
import numpy as np

try:
    import editdistance
except ImportError:
    editdistance = None
    logging.warning("editdistance not available. Install with: pip install editdistance")

logger = logging.getLogger(__name__)

def calculate_edit_distance(reference: str, hypothesis: str) -> int:
    """
    Calculate edit distance between reference and hypothesis.
    
    Args:
        reference: Reference text
        hypothesis: Hypothesis text
        
    Returns:
        Edit distance (number of operations)
    """
    if editdistance is not None:
        return editdistance.eval(reference, hypothesis)
    else:
        # Fallback implementation using dynamic programming
        return _edit_distance_dp(reference, hypothesis)

def _edit_distance_dp(s1: str, s2: str) -> int:
    """
    Dynamic programming implementation of edit distance.
    
    Args:
        s1: First string
        s2: Second string
        
    Returns:
        Edit distance
    """
    m, n = len(s1), len(s2)
    
    # Create DP table
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    
    # Initialize base cases
    for i in range(m + 1):
        dp[i][0] = i
    for j in range(n + 1):
        dp[0][j] = j
    
    # Fill DP table
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if s1[i-1] == s2[j-1]:
                dp[i][j] = dp[i-1][j-1]
            else:
                dp[i][j] = 1 + min(
                    dp[i-1][j],    # deletion
                    dp[i][j-1],    # insertion
                    dp[i-1][j-1]   # substitution
                )
    
    return dp[m][n]

def calculate_cer(reference: str, hypothesis: str) -> float:
    """
    Calculate Character Error Rate (CER).
    
    CER = (S + D + I) / N
    where S = substitutions, D = deletions, I = insertions, N = length of reference
    
    Args:
        reference: Reference text
        hypothesis: Hypothesis text
        
    Returns:
        Character Error Rate (0.0 to 1.0+)
    """
    # Remove spaces and convert to uppercase for character-level comparison
    ref_chars = reference.replace(' ', '').upper()
    hyp_chars = hypothesis.replace(' ', '').upper()
    
    if len(ref_chars) == 0:
        return 0.0 if len(hyp_chars) == 0 else float('inf')
    
    edit_dist = calculate_edit_distance(ref_chars, hyp_chars)
    cer = edit_dist / len(ref_chars)
    
    return cer

def calculate_wer(reference: str, hypothesis: str) -> float:
    """
    Calculate Word Error Rate (WER).
    
    WER = (S + D + I) / N
    where S = substitutions, D = deletions, I = insertions, N = number of words in reference
    
    Args:
        reference: Reference text
        hypothesis: Hypothesis text
        
    Returns:
        Word Error Rate (0.0 to 1.0+)
    """
    # Split into words and convert to uppercase
    ref_words = reference.upper().split()
    hyp_words = hypothesis.upper().split()
    
    if len(ref_words) == 0:
        return 0.0 if len(hyp_words) == 0 else float('inf')
    
    edit_dist = calculate_edit_distance(ref_words, hyp_words)
    wer = edit_dist / len(ref_words)
    
    return wer

def calculate_detailed_wer(reference: str, hypothesis: str) -> Dict[str, float]:
    """
    Calculate detailed WER with breakdown of error types.
    
    Args:
        reference: Reference text
        hypothesis: Hypothesis text
        
    Returns:
        Dictionary with WER breakdown
    """
    ref_words = reference.upper().split()
    hyp_words = hypothesis.upper().split()
    
    if len(ref_words) == 0:
        return {
            'wer': 0.0 if len(hyp_words) == 0 else float('inf'),
            'substitutions': 0,
            'deletions': 0,
            'insertions': len(hyp_words),
            'correct': 0,
            'total_ref_words': 0,
            'total_hyp_words': len(hyp_words)
        }
    
    # Calculate alignment using DP
    m, n = len(ref_words), len(hyp_words)
    dp = [[float('inf')] * (n + 1) for _ in range(m + 1)]
    
    # Initialize base cases
    for i in range(m + 1):
        dp[i][0] = i
    for j in range(n + 1):
        dp[0][j] = j
    
    # Fill DP table
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if ref_words[i-1] == hyp_words[j-1]:
                dp[i][j] = dp[i-1][j-1]  # match
            else:
                dp[i][j] = 1 + min(
                    dp[i-1][j],    # deletion
                    dp[i][j-1],    # insertion
                    dp[i-1][j-1]   # substitution
                )
    
    # Backtrack to count error types
    i, j = m, n
    substitutions = deletions = insertions = correct = 0
    
    while i > 0 or j > 0:
        if i > 0 and j > 0 and ref_words[i-1] == hyp_words[j-1]:
            correct += 1
            i -= 1
            j -= 1
        elif i > 0 and j > 0 and dp[i][j] == dp[i-1][j-1] + 1:
            substitutions += 1
            i -= 1
            j -= 1
        elif i > 0 and dp[i][j] == dp[i-1][j] + 1:
            deletions += 1
            i -= 1
        elif j > 0 and dp[i][j] == dp[i][j-1] + 1:
            insertions += 1
            j -= 1
        else:
            break
    
    total_errors = substitutions + deletions + insertions
    wer = total_errors / len(ref_words)
    
    return {
        'wer': wer,
        'substitutions': substitutions,
        'deletions': deletions,
        'insertions': insertions,
        'correct': correct,
        'total_ref_words': len(ref_words),
        'total_hyp_words': len(hyp_words),
        'total_errors': total_errors
    }

def calculate_confidence_metrics(
    predictions: List[Tuple[str, float]],
    references: List[str]
) -> Dict[str, float]:
    """
    Calculate confidence-based metrics.
    
    Args:
        predictions: List of (prediction, confidence) tuples
        references: List of reference texts
        
    Returns:
        Dictionary with confidence metrics
    """
    if len(predictions) != len(references):
        raise ValueError("Number of predictions and references must match")
    
    if not predictions:
        return {}
    
    confidences = [conf for _, conf in predictions]
    texts = [text for text, _ in predictions]
    
    # Calculate CER and WER for each prediction
    cers = [calculate_cer(ref, pred) for ref, pred in zip(references, texts)]
    wers = [calculate_wer(ref, pred) for ref, pred in zip(references, texts)]
    
    # Calculate correlation between confidence and accuracy
    accuracies = [1.0 - cer for cer in cers]  # Convert CER to accuracy
    
    # Pearson correlation coefficient
    conf_mean = np.mean(confidences)
    acc_mean = np.mean(accuracies)
    
    numerator = sum((c - conf_mean) * (a - acc_mean) 
                   for c, a in zip(confidences, accuracies))
    conf_var = sum((c - conf_mean) ** 2 for c in confidences)
    acc_var = sum((a - acc_mean) ** 2 for a in accuracies)
    
    correlation = numerator / (np.sqrt(conf_var * acc_var) + 1e-8)
    
    return {
        'mean_confidence': np.mean(confidences),
        'std_confidence': np.std(confidences),
        'mean_cer': np.mean(cers),
        'std_cer': np.std(cers),
        'mean_wer': np.mean(wers),
        'std_wer': np.std(wers),
        'confidence_accuracy_correlation': correlation,
        'min_confidence': np.min(confidences),
        'max_confidence': np.max(confidences)
    }

def calculate_threshold_metrics(
    predictions: List[Tuple[str, float]],
    references: List[str],
    thresholds: List[float]
) -> Dict[float, Dict[str, float]]:
    """
    Calculate metrics at different confidence thresholds.
    
    Args:
        predictions: List of (prediction, confidence) tuples
        references: List of reference texts
        thresholds: List of confidence thresholds to evaluate
        
    Returns:
        Dictionary mapping thresholds to metrics
    """
    results = {}
    
    for threshold in thresholds:
        # Filter predictions above threshold
        filtered_preds = []
        filtered_refs = []
        
        for (text, conf), ref in zip(predictions, references):
            if conf >= threshold:
                filtered_preds.append((text, conf))
                filtered_refs.append(ref)
        
        if not filtered_preds:
            results[threshold] = {
                'coverage': 0.0,
                'mean_cer': float('inf'),
                'mean_wer': float('inf'),
                'count': 0
            }
            continue
        
        # Calculate metrics for filtered predictions
        coverage = len(filtered_preds) / len(predictions)
        cers = [calculate_cer(ref, pred) for (pred, _), ref in zip(filtered_preds, filtered_refs)]
        wers = [calculate_wer(ref, pred) for (pred, _), ref in zip(filtered_preds, filtered_refs)]
        
        results[threshold] = {
            'coverage': coverage,
            'mean_cer': np.mean(cers),
            'std_cer': np.std(cers),
            'mean_wer': np.mean(wers),
            'std_wer': np.std(wers),
            'count': len(filtered_preds)
        }
    
    return results

def evaluate_predictions(
    predictions: List[str],
    references: List[str],
    confidences: List[float] = None
) -> Dict[str, float]:
    """
    Comprehensive evaluation of predictions.
    
    Args:
        predictions: List of predicted texts
        references: List of reference texts
        confidences: Optional list of confidence scores
        
    Returns:
        Dictionary with evaluation metrics
    """
    if len(predictions) != len(references):
        raise ValueError("Number of predictions and references must match")
    
    # Calculate basic metrics
    cers = [calculate_cer(ref, pred) for ref, pred in zip(references, predictions)]
    wers = [calculate_wer(ref, pred) for ref, pred in zip(references, predictions)]
    
    results = {
        'mean_cer': np.mean(cers),
        'std_cer': np.std(cers),
        'median_cer': np.median(cers),
        'mean_wer': np.mean(wers),
        'std_wer': np.std(wers),
        'median_wer': np.median(wers),
        'perfect_matches': sum(1 for cer in cers if cer == 0.0),
        'perfect_match_rate': sum(1 for cer in cers if cer == 0.0) / len(cers),
        'total_samples': len(predictions)
    }
    
    # Add confidence-based metrics if available
    if confidences is not None:
        if len(confidences) != len(predictions):
            raise ValueError("Number of confidences must match predictions")
        
        pred_conf_pairs = list(zip(predictions, confidences))
        conf_metrics = calculate_confidence_metrics(pred_conf_pairs, references)
        results.update(conf_metrics)
    
    return results

if __name__ == "__main__":
    # Test metrics
    references = [
        "HELLO WORLD",
        "PAIN IN CHEST",
        "NEED HELP NURSE",
        "WATER PLEASE"
    ]
    
    predictions = [
        "HELLO WORD",
        "PAIN IN CHEST",
        "NEED HELP",
        "WATER"
    ]
    
    confidences = [0.8, 0.95, 0.7, 0.6]
    
    # Test individual metrics
    print("Individual metrics:")
    for ref, pred in zip(references, predictions):
        cer = calculate_cer(ref, pred)
        wer = calculate_wer(ref, pred)
        detailed_wer = calculate_detailed_wer(ref, pred)
        print(f"  '{ref}' -> '{pred}': CER={cer:.3f}, WER={wer:.3f}")
        print(f"    Detailed WER: {detailed_wer}")
    
    # Test comprehensive evaluation
    print("\nComprehensive evaluation:")
    results = evaluate_predictions(predictions, references, confidences)
    for key, value in results.items():
        print(f"  {key}: {value}")
    
    # Test threshold metrics
    print("\nThreshold metrics:")
    thresholds = [0.5, 0.7, 0.9]
    pred_conf_pairs = list(zip(predictions, confidences))
    threshold_results = calculate_threshold_metrics(pred_conf_pairs, references, thresholds)
    
    for threshold, metrics in threshold_results.items():
        print(f"  Threshold {threshold}: {metrics}")
    
    print("\nMetrics test completed successfully!")
