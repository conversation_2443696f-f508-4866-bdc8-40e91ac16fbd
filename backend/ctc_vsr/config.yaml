# CTC Visual Speech Recognition Configuration

# Video preprocessing parameters
video:
  frames: 64              # Number of frames to extract from video
  height: 112             # Target height for video frames
  width: 112              # Target width for video frames
  grayscale: true         # Convert to grayscale
  fps: 25                 # Target frames per second
  normalize: true         # Normalize pixel values to [0, 1]

# Model architecture parameters
model:
  feat_dim: 128           # Visual encoder output feature dimension
  hidden_dim: 256         # LSTM hidden dimension
  num_layers: 2           # Number of LSTM layers
  dropout: 0.1            # Dropout rate
  bidirectional: true     # Use bidirectional LSTM

# Training parameters
training:
  batch_size: 8           # Training batch size
  epochs: 8               # Number of training epochs
  learning_rate: 3e-4     # Initial learning rate
  weight_decay: 1e-4      # Weight decay for regularization
  grad_clip: 1.0          # Gradient clipping threshold
  patience: 3             # Early stopping patience
  min_delta: 1e-4         # Minimum improvement for early stopping

# Inference confidence thresholds
confidence:
  icu_threshold: 0.65     # Minimum confidence for ICU mode
  open_threshold: 0.55    # Minimum confidence for open mode
  beam_width: 10          # Beam search width
  
# Data augmentation (training only)
augmentation:
  enabled: true
  temporal_jitter: 0.1    # Random temporal shift
  spatial_crop: 0.05      # Random spatial cropping
  brightness: 0.1         # Random brightness adjustment
  contrast: 0.1           # Random contrast adjustment

# ICU vocabulary - clinical terms and common names
icu_vocabulary:
  # Emergency/Pain terms
  - "PAIN"
  - "HELP"
  - "EMERGENCY"
  - "HURT"
  - "STOP"
  
  # Medical staff
  - "NURSE"
  - "DOCTOR"
  - "THERAPIST"
  
  # Basic needs
  - "WATER"
  - "BATHROOM"
  - "TOILET"
  - "MEDICINE"
  - "PILLS"
  - "FOOD"
  - "HUNGRY"
  - "THIRSTY"
  
  # Communication
  - "YES"
  - "NO"
  - "PLEASE"
  - "THANK YOU"
  - "SORRY"
  
  # Common patient names
  - "JOHN"
  - "MARY"
  - "DAVID"
  - "SARAH"
  - "MICHAEL"
  - "JENNIFER"
  - "ROBERT"
  - "LISA"
  - "WILLIAM"
  - "KAREN"
  - "JAMES"
  - "SUSAN"
  - "RICHARD"
  - "JESSICA"
  - "CHARLES"
  - "NANCY"
  - "THOMAS"
  - "BETTY"
  - "CHRISTOPHER"
  - "HELEN"
  
  # Body parts
  - "HEAD"
  - "CHEST"
  - "STOMACH"
  - "BACK"
  - "ARM"
  - "LEG"
  - "HAND"
  - "FOOT"
  
  # Feelings/Status
  - "TIRED"
  - "SICK"
  - "DIZZY"
  - "NAUSEOUS"
  - "COLD"
  - "HOT"
  - "BETTER"
  - "WORSE"

# File paths
paths:
  artifacts_dir: "artifacts"
  models_dir: "artifacts/models"
  lexicon_dir: "artifacts/lexicon"
  data_dir: "data"
  
# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/ctc_vsr.log"
