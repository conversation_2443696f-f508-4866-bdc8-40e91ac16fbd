"""
Visual encoder for CTC-based Visual Speech Recognition.

This module implements a shared visual encoder using ResNet3D-18 backbone
with linear projection to configurable feature dimension. The encoder is
shared between phoneme and character CTC models for efficiency.
"""

import torch
import torch.nn as nn
import logging
from typing import Tuple, Optional
from torchvision.models.video import r3d_18

logger = logging.getLogger(__name__)

class VisualEncoder(nn.Module):
    """
    Visual encoder using ResNet3D-18 backbone with linear projection.
    
    The encoder processes video input and outputs temporal feature sequences
    suitable for CTC-based sequence modeling.
    """
    
    def __init__(
        self,
        feat_dim: int = 128,
        pretrained: bool = True,
        dropout: float = 0.1
    ):
        """
        Initialize visual encoder.
        
        Args:
            feat_dim: Output feature dimension
            pretrained: Whether to use pretrained weights (Kinetics)
            dropout: Dropout rate for regularization
        """
        super().__init__()
        
        self.feat_dim = feat_dim
        self.pretrained = pretrained
        self.dropout_rate = dropout
        
        # Load ResNet3D-18 backbone (MIT licensed, Kinetics pretrained)
        self.backbone = r3d_18(pretrained=pretrained)
        
        # Remove final classification layer
        # ResNet3D structure: conv1 -> bn1 -> relu -> maxpool -> layer1-4 -> avgpool -> fc
        modules = list(self.backbone.children())[:-1]  # Remove fc layer
        self.feature_extractor = nn.Sequential(*modules)
        
        # The backbone outputs 512-dimensional features after global average pooling
        backbone_dim = 512
        
        # Add projection layer to map to desired feature dimension
        self.projection = nn.Sequential(
            nn.Linear(backbone_dim, feat_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        # Initialize projection layer
        self._init_projection()
        
        logger.info(f"Visual encoder initialized with feat_dim={feat_dim}, "
                   f"pretrained={pretrained}")
    
    def _init_projection(self):
        """Initialize projection layer weights."""
        for module in self.projection.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through visual encoder.
        
        Args:
            x: Input video tensor with shape (batch_size, channels, frames, height, width)
            
        Returns:
            Feature tensor with shape (batch_size, sequence_length, feat_dim)
        """
        batch_size = x.size(0)
        
        # Extract features using ResNet3D backbone
        # Input: (batch_size, channels, frames, height, width)
        # Output after avgpool: (batch_size, 512, 1, 1, 1)
        features = self.feature_extractor(x)
        
        # Remove spatial and temporal dimensions
        # Shape: (batch_size, 512)
        features = features.view(batch_size, -1)
        
        # Project to desired feature dimension
        # Shape: (batch_size, feat_dim)
        features = self.projection(features)
        
        # For CTC, we need a sequence dimension
        # Since ResNet3D gives us a single feature vector per video,
        # we'll expand it to create a sequence
        # This is a simplified approach - in practice, you might want to
        # extract features at multiple temporal scales
        
        # For now, create a sequence by repeating the feature vector
        # In a more sophisticated implementation, you could:
        # 1. Extract features from multiple temporal windows
        # 2. Use a temporal convolutional network
        # 3. Apply attention mechanisms
        
        # Create a sequence of length equal to a fraction of input frames
        # This is a reasonable approximation for CTC alignment
        input_frames = x.size(2)  # frames dimension
        sequence_length = max(1, input_frames // 4)  # Downsample by 4x
        
        # Expand features to sequence
        # Shape: (batch_size, sequence_length, feat_dim)
        features = features.unsqueeze(1).expand(batch_size, sequence_length, self.feat_dim)
        
        return features
    
    def get_output_dim(self) -> int:
        """Get output feature dimension."""
        return self.feat_dim
    
    def get_sequence_length(self, input_frames: int) -> int:
        """
        Get output sequence length for given input frames.
        
        Args:
            input_frames: Number of input frames
            
        Returns:
            Output sequence length
        """
        return max(1, input_frames // 4)

class TemporalVisualEncoder(nn.Module):
    """
    Enhanced visual encoder with explicit temporal modeling.
    
    This version extracts features at multiple temporal scales and uses
    a temporal convolutional network for better sequence modeling.
    """
    
    def __init__(
        self,
        feat_dim: int = 128,
        pretrained: bool = True,
        dropout: float = 0.1,
        temporal_kernel_size: int = 3,
        temporal_stride: int = 2
    ):
        """
        Initialize temporal visual encoder.
        
        Args:
            feat_dim: Output feature dimension
            pretrained: Whether to use pretrained weights
            dropout: Dropout rate
            temporal_kernel_size: Kernel size for temporal convolution
            temporal_stride: Stride for temporal downsampling
        """
        super().__init__()
        
        self.feat_dim = feat_dim
        self.temporal_stride = temporal_stride
        
        # Load ResNet3D-18 backbone
        backbone = r3d_18(pretrained=pretrained)
        
        # Extract feature layers (before final pooling and classification)
        self.conv1 = backbone.conv1
        self.bn1 = backbone.bn1
        self.relu = backbone.relu
        self.maxpool = backbone.maxpool
        self.layer1 = backbone.layer1
        self.layer2 = backbone.layer2
        self.layer3 = backbone.layer3
        self.layer4 = backbone.layer4
        
        # Add temporal convolution for sequence modeling
        # After layer4, we have features with shape (batch, 512, T/8, H/32, W/32)
        self.temporal_conv = nn.Conv3d(
            512, 256, 
            kernel_size=(temporal_kernel_size, 1, 1),
            stride=(temporal_stride, 1, 1),
            padding=(temporal_kernel_size // 2, 0, 0)
        )
        
        # Global spatial pooling
        self.global_pool = nn.AdaptiveAvgPool3d((None, 1, 1))
        
        # Projection to final feature dimension
        self.projection = nn.Sequential(
            nn.Linear(256, feat_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        self._init_weights()
        
        logger.info(f"Temporal visual encoder initialized with feat_dim={feat_dim}")
    
    def _init_weights(self):
        """Initialize new layer weights."""
        nn.init.kaiming_normal_(self.temporal_conv.weight, mode='fan_out', nonlinearity='relu')
        if self.temporal_conv.bias is not None:
            nn.init.constant_(self.temporal_conv.bias, 0)
        
        for module in self.projection.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through temporal visual encoder.
        
        Args:
            x: Input video tensor (batch_size, channels, frames, height, width)
            
        Returns:
            Feature sequence (batch_size, sequence_length, feat_dim)
        """
        # ResNet3D feature extraction
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)
        
        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)
        
        # Temporal convolution for sequence modeling
        x = self.temporal_conv(x)
        x = self.relu(x)
        
        # Global spatial pooling (keep temporal dimension)
        x = self.global_pool(x)  # (batch, 256, T', 1, 1)
        
        # Remove spatial dimensions
        x = x.squeeze(-1).squeeze(-1)  # (batch, 256, T')
        
        # Transpose to get sequence-first format
        x = x.transpose(1, 2)  # (batch, T', 256)
        
        # Project to final feature dimension
        batch_size, seq_len, _ = x.shape
        x = x.reshape(-1, 256)  # (batch * T', 256)
        x = self.projection(x)  # (batch * T', feat_dim)
        x = x.reshape(batch_size, seq_len, self.feat_dim)  # (batch, T', feat_dim)
        
        return x
    
    def get_output_dim(self) -> int:
        """Get output feature dimension."""
        return self.feat_dim
    
    def get_sequence_length(self, input_frames: int) -> int:
        """
        Get output sequence length for given input frames.
        
        Args:
            input_frames: Number of input frames
            
        Returns:
            Output sequence length
        """
        # Account for ResNet3D downsampling (8x) and temporal conv stride
        return max(1, (input_frames // 8) // self.temporal_stride)

def make_visual_encoder(
    feat_dim: int = 128,
    pretrained: bool = True,
    encoder_type: str = "simple",
    **kwargs
) -> nn.Module:
    """
    Factory function to create visual encoder.
    
    Args:
        feat_dim: Output feature dimension
        pretrained: Whether to use pretrained weights
        encoder_type: Type of encoder ("simple" or "temporal")
        **kwargs: Additional arguments for encoder
        
    Returns:
        Visual encoder module
    """
    if encoder_type == "simple":
        return VisualEncoder(feat_dim=feat_dim, pretrained=pretrained, **kwargs)
    elif encoder_type == "temporal":
        return TemporalVisualEncoder(feat_dim=feat_dim, pretrained=pretrained, **kwargs)
    else:
        raise ValueError(f"Unknown encoder type: {encoder_type}")

if __name__ == "__main__":
    # Test visual encoder
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Test simple encoder
    print("Testing simple visual encoder...")
    encoder = VisualEncoder(feat_dim=128, pretrained=False)
    encoder.to(device)
    
    # Create dummy input (batch=2, channels=1, frames=64, height=112, width=112)
    x = torch.randn(2, 1, 64, 112, 112).to(device)
    
    with torch.no_grad():
        features = encoder(x)
        print(f"Input shape: {x.shape}")
        print(f"Output shape: {features.shape}")
        print(f"Expected sequence length: {encoder.get_sequence_length(64)}")
    
    # Test temporal encoder
    print("\nTesting temporal visual encoder...")
    temporal_encoder = TemporalVisualEncoder(feat_dim=128, pretrained=False)
    temporal_encoder.to(device)
    
    with torch.no_grad():
        features = temporal_encoder(x)
        print(f"Input shape: {x.shape}")
        print(f"Output shape: {features.shape}")
        print(f"Expected sequence length: {temporal_encoder.get_sequence_length(64)}")
    
    print("\nVisual encoder tests completed successfully!")
