"""
FastAPI router for CTC-based Visual Speech Recognition endpoints.

This module provides REST API endpoints for the CTC VSR pipeline,
including ICU mode, open mode, and automatic mode selection.
"""

import os
import logging
import tempfile
from pathlib import Path
from typing import Dict, Optional

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse
import numpy as np

from .infer import CTCInference

logger = logging.getLogger(__name__)

# Global inference instance (initialized on first use)
_inference_instance: Optional[CTCInference] = None

def get_inference() -> CTCInference:
    """
    Get or create CTC inference instance.
    
    Returns:
        CTCInference instance
    """
    global _inference_instance
    
    if _inference_instance is None:
        # Get model paths from environment or use defaults
        config_path = os.getenv('CTC_CONFIG_PATH', 'backend/ctc_vsr/config.yaml')
        phoneme_model_path = os.getenv('CTC_PHONEME_MODEL_PATH', 'artifacts/models/phoneme_ctc_best.pt')
        char_model_path = os.getenv('CTC_CHAR_MODEL_PATH', 'artifacts/models/char_ctc_best.pt')
        lexicon_path = os.getenv('CTC_LEXICON_PATH', 'artifacts/lexicon/icu_lexicon.json')
        device = os.getenv('CTC_DEVICE', 'auto')
        
        try:
            _inference_instance = CTCInference(
                config_path=config_path if os.path.exists(config_path) else None,
                phoneme_model_path=phoneme_model_path if os.path.exists(phoneme_model_path) else None,
                char_model_path=char_model_path if os.path.exists(char_model_path) else None,
                lexicon_path=lexicon_path if os.path.exists(lexicon_path) else None,
                device=device
            )
            logger.info("CTC inference instance initialized")
        except Exception as e:
            logger.error(f"Failed to initialize CTC inference: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to initialize CTC models: {e}")
    
    return _inference_instance

# Create router
router = APIRouter(prefix="/ctc", tags=["CTC Visual Speech Recognition"])

@router.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        inference = get_inference()
        model_info = inference.get_model_info()
        return {
            "status": "healthy",
            "models_loaded": {
                "phoneme": model_info["phoneme_model_loaded"],
                "character": model_info["char_model_loaded"]
            },
            "device": model_info["device"]
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={"status": "unhealthy", "error": str(e)}
        )

@router.get("/info")
async def get_model_info(inference: CTCInference = Depends(get_inference)):
    """Get detailed model information."""
    return inference.get_model_info()

@router.post("/predict")
async def predict_best_mode(
    file: UploadFile = File(...),
    inference: CTCInference = Depends(get_inference)
):
    """
    Predict using automatic mode selection (ICU -> Open -> Fallback).
    
    Args:
        file: Video file (MP4, AVI, MOV, or NPY)
        
    Returns:
        Prediction result with selected mode and confidence
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Validate file type
    allowed_extensions = {'.mp4', '.avi', '.mov', '.npy'}
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_ext}. Allowed: {allowed_extensions}"
        )
    
    # Save uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
        try:
            # Write uploaded content to temp file
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            
            # Run inference
            result = inference.predict_best(temp_file.name)
            
            # Add metadata
            result.update({
                "filename": file.filename,
                "file_size": len(content),
                "endpoint": "predict_best"
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            raise HTTPException(status_code=500, detail=f"Prediction failed: {e}")
        
        finally:
            # Clean up temp file
            try:
                os.unlink(temp_file.name)
            except Exception as e:
                logger.warning(f"Failed to delete temp file: {e}")

@router.post("/predict_icu")
async def predict_icu_mode(
    file: UploadFile = File(...),
    inference: CTCInference = Depends(get_inference)
):
    """
    Predict using ICU mode (phoneme + constrained lexicon).
    
    Args:
        file: Video file (MP4, AVI, MOV, or NPY)
        
    Returns:
        ICU mode prediction result
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Validate file type
    allowed_extensions = {'.mp4', '.avi', '.mov', '.npy'}
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_ext}. Allowed: {allowed_extensions}"
        )
    
    # Save uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
        try:
            # Write uploaded content to temp file
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            
            # Run ICU inference
            result = inference.predict_icu(temp_file.name)
            
            # Add metadata
            result.update({
                "filename": file.filename,
                "file_size": len(content),
                "endpoint": "predict_icu"
            })
            
            return result
            
        except Exception as e:
            logger.error(f"ICU prediction failed: {e}")
            raise HTTPException(status_code=500, detail=f"ICU prediction failed: {e}")
        
        finally:
            # Clean up temp file
            try:
                os.unlink(temp_file.name)
            except Exception as e:
                logger.warning(f"Failed to delete temp file: {e}")

@router.post("/predict_open")
async def predict_open_mode(
    file: UploadFile = File(...),
    inference: CTCInference = Depends(get_inference)
):
    """
    Predict using open mode (character + open vocabulary).
    
    Args:
        file: Video file (MP4, AVI, MOV, or NPY)
        
    Returns:
        Open mode prediction result
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Validate file type
    allowed_extensions = {'.mp4', '.avi', '.mov', '.npy'}
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file_ext}. Allowed: {allowed_extensions}"
        )
    
    # Save uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
        try:
            # Write uploaded content to temp file
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            
            # Run open inference
            result = inference.predict_open(temp_file.name)
            
            # Add metadata
            result.update({
                "filename": file.filename,
                "file_size": len(content),
                "endpoint": "predict_open"
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Open prediction failed: {e}")
            raise HTTPException(status_code=500, detail=f"Open prediction failed: {e}")
        
        finally:
            # Clean up temp file
            try:
                os.unlink(temp_file.name)
            except Exception as e:
                logger.warning(f"Failed to delete temp file: {e}")

@router.post("/predict_array")
async def predict_from_array(
    array_data: Dict,
    mode: str = "best",
    inference: CTCInference = Depends(get_inference)
):
    """
    Predict from numpy array data (for programmatic access).
    
    Args:
        array_data: Dictionary with 'data' key containing flattened array and 'shape' key
        mode: Prediction mode ("best", "icu", or "open")
        
    Returns:
        Prediction result
    """
    try:
        # Reconstruct numpy array
        if 'data' not in array_data or 'shape' not in array_data:
            raise HTTPException(status_code=400, detail="Array data must contain 'data' and 'shape' keys")
        
        array = np.array(array_data['data']).reshape(array_data['shape'])
        
        # Validate array shape
        if array.ndim not in [3, 4]:
            raise HTTPException(status_code=400, detail=f"Invalid array shape: {array.shape}")
        
        # Run inference based on mode
        if mode == "icu":
            result = inference.predict_icu(array)
        elif mode == "open":
            result = inference.predict_open(array)
        elif mode == "best":
            result = inference.predict_best(array)
        else:
            raise HTTPException(status_code=400, detail=f"Invalid mode: {mode}")
        
        # Add metadata
        result.update({
            "input_shape": array.shape,
            "input_dtype": str(array.dtype),
            "endpoint": "predict_array",
            "mode_requested": mode
        })
        
        return result
        
    except Exception as e:
        logger.error(f"Array prediction failed: {e}")
        raise HTTPException(status_code=500, detail=f"Array prediction failed: {e}")

# Error handlers
@router.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception in CTC router: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": str(exc)}
    )
