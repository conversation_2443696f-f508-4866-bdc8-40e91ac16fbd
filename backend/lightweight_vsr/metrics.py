"""
Metrics computation for ICU phrase classification
Includes overall metrics and demographic-sliced evaluation
"""

import numpy as np
import pandas as pd
import torch
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support, 
    confusion_matrix, classification_report
)
from typing import Dict, List, Tuple, Optional
import json
from pathlib import Path


def compute_metrics(y_true: List[int], 
                   y_pred: List[int], 
                   phrase_names: List[str]) -> Dict:
    """
    Compute comprehensive classification metrics
    
    Args:
        y_true: True labels
        y_pred: Predicted labels  
        phrase_names: List of phrase names in order
        
    Returns:
        Dictionary with all metrics
    """
    # Overall metrics
    accuracy = accuracy_score(y_true, y_pred)
    precision, recall, f1, support = precision_recall_fscore_support(
        y_true, y_pred, average=None, zero_division=0
    )
    
    # Macro averages
    macro_precision = precision_recall_fscore_support(
        y_true, y_pred, average='macro', zero_division=0
    )[0]
    macro_recall = precision_recall_fscore_support(
        y_true, y_pred, average='macro', zero_division=0
    )[1]
    macro_f1 = precision_recall_fscore_support(
        y_true, y_pred, average='macro', zero_division=0
    )[2]
    
    # Weighted averages
    weighted_precision = precision_recall_fscore_support(
        y_true, y_pred, average='weighted', zero_division=0
    )[0]
    weighted_recall = precision_recall_fscore_support(
        y_true, y_pred, average='weighted', zero_division=0
    )[1]
    weighted_f1 = precision_recall_fscore_support(
        y_true, y_pred, average='weighted', zero_division=0
    )[2]
    
    # Per-class metrics
    per_class_metrics = {}
    for i, phrase in enumerate(phrase_names):
        per_class_metrics[phrase] = {
            'precision': float(precision[i]) if i < len(precision) else 0.0,
            'recall': float(recall[i]) if i < len(recall) else 0.0,
            'f1': float(f1[i]) if i < len(f1) else 0.0,
            'support': int(support[i]) if i < len(support) else 0
        }
    
    # Confusion matrix
    cm = confusion_matrix(y_true, y_pred)
    
    # Find worst performing phrase
    worst_phrase_f1 = min(per_class_metrics.values(), key=lambda x: x['f1'])['f1']
    worst_phrase = min(per_class_metrics.keys(), key=lambda x: per_class_metrics[x]['f1'])
    
    return {
        'accuracy': float(accuracy),
        'macro_precision': float(macro_precision),
        'macro_recall': float(macro_recall),
        'macro_f1': float(macro_f1),
        'weighted_precision': float(weighted_precision),
        'weighted_recall': float(weighted_recall),
        'weighted_f1': float(weighted_f1),
        'worst_phrase_f1': float(worst_phrase_f1),
        'worst_phrase': worst_phrase,
        'per_class': per_class_metrics,
        'confusion_matrix': cm.tolist(),
        'num_samples': len(y_true)
    }


def compute_demographics_metrics(y_true: List[int],
                               y_pred: List[int], 
                               metadata: pd.DataFrame,
                               phrase_names: List[str]) -> Dict:
    """
    Compute metrics sliced by demographic groups
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        metadata: DataFrame with demographic information
        phrase_names: List of phrase names in order
        
    Returns:
        Dictionary with overall and sliced metrics
    """
    # Overall metrics
    overall_metrics = compute_metrics(y_true, y_pred, phrase_names)
    
    # Demographic slices
    demographic_axes = ['age_group', 'gender', 'ethnicity', 'lighting']
    sliced_metrics = {}
    
    for axis in demographic_axes:
        if axis not in metadata.columns:
            continue
            
        axis_metrics = {}
        unique_values = metadata[axis].unique()
        
        for value in unique_values:
            if pd.isna(value) or value == 'unknown':
                continue
                
            # Get indices for this demographic group
            mask = metadata[axis] == value
            indices = mask[mask].index.tolist()
            
            if len(indices) < 5:  # Skip groups with too few samples
                continue
                
            # Extract predictions for this group
            group_y_true = [y_true[i] for i in indices if i < len(y_true)]
            group_y_pred = [y_pred[i] for i in indices if i < len(y_pred)]
            
            if len(group_y_true) == 0:
                continue
                
            # Compute metrics for this group
            group_metrics = compute_metrics(group_y_true, group_y_pred, phrase_names)
            axis_metrics[str(value)] = group_metrics
        
        if axis_metrics:
            sliced_metrics[axis] = axis_metrics
    
    # Flag problematic subgroups (>10 pts below overall macro-F1)
    overall_macro_f1 = overall_metrics['macro_f1']
    flagged_groups = []
    
    for axis, groups in sliced_metrics.items():
        for group_name, group_metrics in groups.items():
            delta = group_metrics['macro_f1'] - overall_macro_f1
            if delta < -0.10:
                flagged_groups.append({
                    'axis': axis,
                    'group': group_name,
                    'macro_f1': group_metrics['macro_f1'],
                    'delta': delta
                })
    
    return {
        'overall': overall_metrics,
        'slices': sliced_metrics,
        'flagged_groups': flagged_groups,
        'overall_macro_f1': overall_macro_f1
    }


def save_metrics(metrics: Dict, output_path: str):
    """Save metrics to JSON file"""
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w') as f:
        json.dump(metrics, f, indent=2)


def print_metrics_summary(metrics: Dict):
    """Print a summary of metrics"""
    print("\n" + "="*50)
    print("METRICS SUMMARY")
    print("="*50)
    
    if 'overall' in metrics:
        # Demographics metrics format
        overall = metrics['overall']
        print(f"Overall Accuracy: {overall['accuracy']:.3f}")
        print(f"Overall Macro-F1: {overall['macro_f1']:.3f}")
        print(f"Worst Phrase F1: {overall['worst_phrase_f1']:.3f} ({overall['worst_phrase']})")
        
        # Print flagged groups
        if metrics.get('flagged_groups'):
            print(f"\n⚠️  FLAGGED GROUPS (>10 pts below overall):")
            for group in metrics['flagged_groups']:
                print(f"  {group['axis']}.{group['group']}: {group['macro_f1']:.3f} (Δ {group['delta']:+.3f})")
        else:
            print("\n✅ No demographic groups flagged")
            
        # Print demographic breakdown
        print(f"\nDemographic Breakdown:")
        for axis, groups in metrics.get('slices', {}).items():
            print(f"\n{axis.upper()}:")
            for group_name, group_metrics in groups.items():
                delta = group_metrics['macro_f1'] - overall['macro_f1']
                flag = "  <-- CHECK" if delta < -0.10 else ""
                print(f"  {group_name:15s} macroF1={group_metrics['macro_f1']:.3f} (Δ {delta:+.3f}){flag}")
    else:
        # Simple metrics format
        print(f"Accuracy: {metrics['accuracy']:.3f}")
        print(f"Macro-F1: {metrics['macro_f1']:.3f}")
        print(f"Worst Phrase F1: {metrics['worst_phrase_f1']:.3f} ({metrics['worst_phrase']})")
    
    print("="*50)


def check_acceptance_criteria(metrics: Dict) -> Tuple[bool, List[str]]:
    """
    Check if metrics meet acceptance criteria
    
    Returns:
        passed: Whether all criteria are met
        issues: List of issues found
    """
    issues = []
    
    if 'overall' in metrics:
        overall = metrics['overall']
    else:
        overall = metrics
    
    # Check macro-F1 >= 0.90
    if overall['macro_f1'] < 0.90:
        issues.append(f"Macro-F1 {overall['macro_f1']:.3f} < 0.90")
    
    # Check worst phrase F1 >= 0.80
    if overall['worst_phrase_f1'] < 0.80:
        issues.append(f"Worst phrase F1 {overall['worst_phrase_f1']:.3f} < 0.80 ({overall['worst_phrase']})")
    
    # Check demographic fairness
    if 'flagged_groups' in metrics and metrics['flagged_groups']:
        issues.append(f"{len(metrics['flagged_groups'])} demographic groups >10 pts below overall")
    
    passed = len(issues) == 0
    return passed, issues


def evaluate_model(model: torch.nn.Module,
                  dataloader: torch.utils.data.DataLoader,
                  device: torch.device,
                  phrase_names: List[str]) -> Tuple[List[int], List[int], pd.DataFrame]:
    """
    Evaluate model on a dataset
    
    Returns:
        y_true: True labels
        y_pred: Predicted labels  
        metadata: Metadata for all samples
    """
    model.eval()
    y_true = []
    y_pred = []
    all_metadata = []
    
    with torch.no_grad():
        for batch_videos, batch_labels, batch_metadata in dataloader:
            batch_videos = batch_videos.to(device)
            
            # Forward pass
            logits = model(batch_videos)
            predictions = torch.argmax(logits, dim=1)
            
            # Collect results
            y_true.extend(batch_labels.cpu().numpy().tolist())
            y_pred.extend(predictions.cpu().numpy().tolist())
            all_metadata.extend(batch_metadata)
    
    # Convert metadata to DataFrame
    metadata_df = pd.DataFrame(all_metadata)
    
    return y_true, y_pred, metadata_df


if __name__ == "__main__":
    # Test metrics computation
    phrase_names = ["phrase_a", "phrase_b", "phrase_c"]
    y_true = [0, 1, 2, 0, 1, 2, 0, 1]
    y_pred = [0, 1, 1, 0, 1, 2, 1, 1]
    
    metrics = compute_metrics(y_true, y_pred, phrase_names)
    print_metrics_summary(metrics)
    
    passed, issues = check_acceptance_criteria(metrics)
    print(f"\nAcceptance criteria: {'PASSED' if passed else 'FAILED'}")
    if issues:
        for issue in issues:
            print(f"  - {issue}")
