# Lightweight VSR Module

Commercial-safe phrase classifier for ICU lipreading using a lightweight 3D CNN + BiGRU architecture.

## Features

- **Commercial-safe**: No LipNet dependencies, no research-only weights
- **Direct phrase classification**: Softmax over 26 ICU phrases
- **Lightweight architecture**: Mobile3DTiny with <8M parameters
- **Demographics evaluation**: Fairness metrics across age, gender, ethnicity, lighting
- **TorchScript export**: Production-ready model deployment
- **Speaker-wise splits**: Prevents data leakage during training

## Architecture

```
Input Video (32 frames, 96x96, grayscale)
    ↓
Depthwise-Separable 3D Conv Blocks
    ↓
Spatial Global Average Pooling
    ↓
Bidirectional GRU (256×2)
    ↓
Temporal Mean Pooling
    ↓
LayerNorm → Dropout → Linear(26) → Softmax
```

## Quick Start

1. **Install dependencies**:
   ```bash
   pip install -r backend/lightweight_vsr/requirements.txt
   ```

2. **Prepare data manifest**:
   ```csv
   video_path,speaker_id,phrase,age_group,gender,ethnicity,lighting
   /path/to/video1.mp4,spk001,i'm in pain,65-80,F,Aboriginal,indoor_bright
   ```

3. **Train model**:
   ```bash
   python backend/lightweight_vsr/train.py \
     --manifest data/manifest.csv \
     --out_dir artifacts/vsr_26p_v1 \
     --config configs/phrases26.yaml \
     --epochs 40
   ```

4. **Export to TorchScript**:
   ```bash
   python backend/lightweight_vsr/infer.py export \
     --checkpoint artifacts/vsr_26p_v1/best.ckpt \
     --config configs/phrases26.yaml \
     --output artifacts/vsr_26p_v1/model.ts
   ```

5. **Run inference**:
   ```python
   from backend.lightweight_vsr.infer import predict_phrase
   result = predict_phrase("video.mp4")
   print(f"Phrase: {result['phrase']} (confidence: {result['confidence']:.3f})")
   ```

## Performance Targets

- **Accuracy**: Val macro-F1 ≥ 0.90, worst-phrase F1 ≥ 0.80
- **Fairness**: No demographic subgroup >10 pts below overall macro-F1
- **Latency**: <150ms per 32-frame clip on T4 GPU
- **Model size**: <10 MB TorchScript

## Module Structure

```
backend/lightweight_vsr/
├── __init__.py          # Module exports
├── model.py             # Mobile3DTiny architecture
├── dataset.py           # Data loading and preprocessing
├── train.py             # Training pipeline
├── infer.py             # Inference and TorchScript export
├── utils_video.py       # Video processing utilities
├── metrics.py           # Evaluation metrics
├── requirements.txt     # Dependencies
└── README.md           # This file
```

## Configuration

The system uses YAML configuration files (e.g., `configs/phrases26.yaml`):

```yaml
phrases:
  - "i'm in pain"
  - "i can't breathe"
  # ... 26 total phrases

frames: 32
height: 96
width: 96
grayscale: true
confidence_threshold: 0.6

model:
  hidden_dim: 256
  num_gru_layers: 2
  dropout: 0.2

optimizer:
  lr: 0.001
  weight_decay: 0.01
```

## Training Output

After training, the output directory contains:

- `best.ckpt` / `last.ckpt`: Model checkpoints
- `metrics.json`: Overall evaluation metrics
- `metrics_by_demo.json`: Demographics-sliced metrics
- `model.ts`: TorchScript export (after export step)
- `tensorboard/`: Training logs

## Demographics Evaluation

The system automatically evaluates fairness across demographic groups:

```python
# Example metrics_by_demo.json structure
{
  "overall": {"macro_f1": 0.92, ...},
  "slices": {
    "age_group": {
      "18-40": {"macro_f1": 0.91, ...},
      "65-80": {"macro_f1": 0.89, ...}
    },
    "gender": {...},
    "ethnicity": {...},
    "lighting": {...}
  },
  "flagged_groups": [...]  # Groups >10 pts below overall
}
```

## API Integration

The module integrates with the main API through:

- `backend.api.app.predict_v2()`: New endpoint using lightweight VSR
- Feature flag `VSR_IMPL=lightweight`: Routes legacy `/predict` to new model

## License

This module and all generated weights are your intellectual property.
Suggested license: Apache-2.0 or MIT.
