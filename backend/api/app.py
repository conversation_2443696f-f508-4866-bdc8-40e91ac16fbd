"""
ICU Lipreading API with both legacy LipNet and new lightweight VSR endpoints
"""

from flask import Flask, request, jsonify
import os
import cv2
import numpy as np
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Feature flag for VSR implementation
USE_LIGHTWEIGHT = os.getenv("VSR_IMPL", "legacy") == "lightweight"

# Import legacy components
try:
    import tensorflow as tf
    import pickle
    from tensorflow.keras.models import load_model
    from demographic_utils import augment_features_with_demographics
    
    # Load legacy models
    try:
        lipnet_model = load_model('models/lipnet_embedding_model.h5')
        logger.info("LipNet embedding model loaded successfully")
    except Exception as e:
        logger.error(f"Error loading LipNet embedding model: {e}")
        lipnet_model = None

    try:
        with open('models/icu_classifier.pkl', 'rb') as f:
            icu_classifier = pickle.load(f)
        logger.info("ICU classifier loaded successfully")
    except Exception as e:
        logger.error(f"Error loading ICU classifier: {e}")
        icu_classifier = None
        
    LEGACY_AVAILABLE = True
    
except ImportError as e:
    logger.warning(f"Legacy dependencies not available: {e}")
    LEGACY_AVAILABLE = False
    lipnet_model = None
    icu_classifier = None

# Import lightweight VSR components
try:
    from backend.lightweight_vsr.infer import predict_phrase as predict_lightweight
    LIGHTWEIGHT_AVAILABLE = True
    logger.info("Lightweight VSR module loaded successfully")
except ImportError as e:
    logger.warning(f"Lightweight VSR not available: {e}")
    LIGHTWEIGHT_AVAILABLE = False

# Legacy ICU phrases
LEGACY_ICU_PHRASES = [
    "Call the nurse",
    "Help me", 
    "I cant breathe",
    "I feel sick",
    "I feel tired"
]


def save_upload_to_tmp(file) -> str:
    """Save uploaded file to temporary location"""
    suffix = Path(file.filename).suffix if file.filename else '.mp4'
    with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
        file.save(tmp_file.name)
        return tmp_file.name


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    status = {
        "status": "healthy",
        "vsr_impl": "lightweight" if USE_LIGHTWEIGHT else "legacy",
        "legacy_available": LEGACY_AVAILABLE,
        "lightweight_available": LIGHTWEIGHT_AVAILABLE
    }
    return jsonify(status), 200


@app.route('/predict_v2', methods=['POST'])
def predict_v2():
    """New lightweight VSR prediction endpoint"""
    if not LIGHTWEIGHT_AVAILABLE:
        return jsonify({"error": "Lightweight VSR not available"}), 503
    
    if 'file' not in request.files:
        return jsonify({"error": "No video file provided"}), 400
    
    video_file = request.files['file']
    if video_file.filename == '':
        return jsonify({"error": "Empty video file"}), 400
    
    tmp_path = None
    try:
        # Save uploaded file
        tmp_path = save_upload_to_tmp(video_file)
        
        # Predict using lightweight VSR
        result = predict_lightweight(tmp_path)
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error in predict_v2: {e}")
        return jsonify({"error": str(e)}), 500
        
    finally:
        # Clean up temporary file
        if tmp_path and os.path.exists(tmp_path):
            os.remove(tmp_path)


@app.route('/predict', methods=['POST'])
def predict():
    """Legacy prediction endpoint with optional routing to lightweight VSR"""
    if USE_LIGHTWEIGHT and LIGHTWEIGHT_AVAILABLE:
        # Route to lightweight implementation
        return predict_v2()
    
    # Use legacy implementation
    if not LEGACY_AVAILABLE:
        return jsonify({"error": "Legacy implementation not available"}), 503
    
    if 'video' not in request.files:
        return jsonify({"error": "No video file provided"}), 400
    
    video_file = request.files['video']
    if video_file.filename == '':
        return jsonify({"error": "Empty video file"}), 400
    
    # Get demographic information if provided
    demographic = request.form.get('demographic', 'male_under_50')
    
    tmp_path = None
    try:
        # Save uploaded file
        tmp_path = save_upload_to_tmp(video_file)
        
        # Extract frames from the video
        frames = extract_frames(tmp_path)
        
        # Get LipNet embeddings
        embeddings = get_lipnet_embeddings(frames)
        
        # Augment embeddings with demographic features
        augmented_embeddings = augment_features_with_demographics(embeddings, tmp_path, demographic)
        
        # Make prediction using the classifier
        prediction_idx, confidence = predict_phrase_legacy(augmented_embeddings)
        
        return jsonify({
            "phrase": LEGACY_ICU_PHRASES[prediction_idx],
            "confidence": float(confidence)
        }), 200
    
    except Exception as e:
        logger.error(f"Error processing video: {e}")
        return jsonify({"error": str(e)}), 500
        
    finally:
        # Clean up temporary file
        if tmp_path and os.path.exists(tmp_path):
            os.remove(tmp_path)


# Legacy helper functions
def extract_frames(video_path, max_frames=75):
    """Extract frames from a video file (legacy)"""
    frames = []
    cap = cv2.VideoCapture(video_path)
    
    while len(frames) < max_frames and cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        
        # Preprocess frame (grayscale, resize to match model input)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Resize to match LipNet input dimensions (46x140)
        resized = cv2.resize(gray, (140, 46))
        normalized = resized / 255.0  # Normalize pixel values
        
        frames.append(normalized)
    
    cap.release()
    
    # Pad if needed to reach max_frames
    if len(frames) < max_frames:
        last_frame = frames[-1] if frames else np.zeros((46, 140))
        frames.extend([last_frame] * (max_frames - len(frames)))
    
    return np.array(frames)


def get_lipnet_embeddings(frames):
    """Get embeddings from the LipNet model (legacy)"""
    if lipnet_model is None:
        raise ValueError("LipNet embedding model not loaded")
    
    # Reshape for model input (add batch dimension and channel dimension)
    input_frames = frames.reshape(1, frames.shape[0], frames.shape[1], frames.shape[2], 1)
    
    # Get embeddings from the model
    embeddings = lipnet_model.predict(input_frames)
    
    # Flatten the embeddings to a 1D vector by taking the mean across the time dimension
    flattened_embeddings = np.mean(embeddings, axis=1).flatten()
    
    return flattened_embeddings


def predict_phrase_legacy(embeddings):
    """Predict the ICU phrase using the legacy classifier"""
    if icu_classifier is None:
        raise ValueError("ICU classifier not loaded")
    
    # Get prediction probabilities
    try:
        probs = icu_classifier.predict_proba([embeddings])[0]
    except AttributeError:
        logger.warning("Using legacy classifier format")
        probs = icu_classifier.predict_proba([embeddings])[0]
    
    # Get the index of the highest probability
    prediction_idx = np.argmax(probs)
    confidence = probs[prediction_idx]
    
    # Add confidence threshold for more reliable predictions
    if confidence < 0.4:
        logger.warning(f"Low confidence prediction: {confidence:.2f}")
    
    return prediction_idx, confidence


@app.route('/status', methods=['GET'])
def status():
    """Detailed status endpoint"""
    return jsonify({
        "vsr_implementation": "lightweight" if USE_LIGHTWEIGHT else "legacy",
        "legacy_available": LEGACY_AVAILABLE,
        "lightweight_available": LIGHTWEIGHT_AVAILABLE,
        "legacy_models": {
            "lipnet_loaded": lipnet_model is not None,
            "classifier_loaded": icu_classifier is not None
        } if LEGACY_AVAILABLE else None,
        "endpoints": {
            "/predict": "legacy (or routed to lightweight if VSR_IMPL=lightweight)",
            "/predict_v2": "lightweight VSR",
            "/health": "health check",
            "/status": "detailed status"
        }
    }), 200


if __name__ == '__main__':
    logger.info(f"Starting API with VSR_IMPL={os.getenv('VSR_IMPL', 'legacy')}")
    logger.info(f"Legacy available: {LEGACY_AVAILABLE}")
    logger.info(f"Lightweight available: {LIGHTWEIGHT_AVAILABLE}")
    
    app.run(host='0.0.0.0', port=5000, debug=False)
