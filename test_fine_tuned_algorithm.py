#!/usr/bin/env python3
"""
Test the fine-tuned mouth detection algorithm to verify complete upper lip capture.
"""

import cv2
import numpy as np
import sys
from pathlib import Path

# Add the current directory to path
sys.path.append('/Users/<USER>/Desktop/app dev 23.5.25')

try:
    from enhanced_video_preprocessor import EnhancedVideoPreprocessor
except ImportError as e:
    print(f"❌ Error importing enhanced_video_preprocessor: {e}")
    sys.exit(1)

def test_fine_tuned_coordinates(video_path):
    """Test the fine-tuned coordinates on the sample video"""
    print(f"🔧 TESTING FINE-TUNED MOUTH DETECTION ALGORITHM")
    print(f"Video: {Path(video_path).name}")
    
    # Extract a frame for analysis
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        print(f"❌ Cannot open video: {video_path}")
        return
    
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    cap.set(cv2.CAP_PROP_POS_FRAMES, 25)  # Middle frame
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        print(f"❌ Cannot read frame from video")
        return
    
    print(f"📹 Video size: {width}x{height}")
    
    # Test mouth detection with fine-tuned algorithm
    preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    x1, y1, x2, y2 = preprocessor.detect_mouth_region(gray)
    
    print(f"🎯 Fine-tuned coordinates:")
    print(f"   Region: ({x1}, {y1}) to ({x2}, {y2})")
    print(f"   Size: {x2-x1}x{y2-y1}")
    print(f"   Y percentage: {y1/height*100:.1f}% to {y2/height*100:.1f}%")
    
    # Compare with previous coordinates
    prev_x1, prev_y1, prev_x2, prev_y2 = 146, 20, 253, 80
    y_shift = y1 - prev_y1
    print(f"📊 Comparison with previous:")
    print(f"   Previous: ({prev_x1}, {prev_y1}) to ({prev_x2}, {prev_y2})")
    print(f"   Y-shift: {y_shift} pixels upward")
    
    # Create comparison visualization
    comparison = frame.copy()
    
    # Draw previous coordinates in RED
    cv2.rectangle(comparison, (prev_x1, prev_y1), (prev_x2, prev_y2), (0, 0, 255), 2)
    cv2.putText(comparison, "PREVIOUS (cut upper lip)", (prev_x1, prev_y1-10), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    
    # Draw fine-tuned coordinates in GREEN
    cv2.rectangle(comparison, (x1, y1), (x2, y2), (0, 255, 0), 3)
    cv2.putText(comparison, "FINE-TUNED (complete lip)", (x1, y1-10), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # Add grid overlay for reference
    col_width = width // 3
    row_height = height // 2
    cv2.line(comparison, (col_width, 0), (col_width, height), (255, 255, 255), 1)
    cv2.line(comparison, (2*col_width, 0), (2*col_width, height), (255, 255, 255), 1)
    cv2.line(comparison, (0, row_height), (width, row_height), (255, 255, 255), 1)
    
    cv2.imwrite("fine_tuned_comparison.jpg", comparison)
    print(f"💾 Saved fine_tuned_comparison.jpg")
    
    # Extract and compare crops
    prev_crop = frame[prev_y1:prev_y2, prev_x1:prev_x2]
    fine_tuned_crop = frame[y1:y2, x1:x2]
    
    if prev_crop.size > 0 and fine_tuned_crop.size > 0:
        # Scale up for visibility
        prev_scaled = cv2.resize(prev_crop, (320, 180), interpolation=cv2.INTER_NEAREST)
        fine_tuned_scaled = cv2.resize(fine_tuned_crop, (320, 180), interpolation=cv2.INTER_NEAREST)
        
        # Add labels
        cv2.putText(prev_scaled, "PREVIOUS (cut upper lip)", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        cv2.putText(fine_tuned_scaled, "FINE-TUNED (complete lip)", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Create side-by-side comparison
        crop_comparison = np.hstack([prev_scaled, fine_tuned_scaled])
        cv2.imwrite("crop_comparison.jpg", crop_comparison)
        print(f"💾 Saved crop_comparison.jpg")
    
    return x1, y1, x2, y2

def test_processing_pipeline(video_path):
    """Test the complete processing pipeline with fine-tuned algorithm"""
    print(f"\n🔧 TESTING COMPLETE PROCESSING PIPELINE")
    
    # Remove existing processed video
    processed_path = Path("data/where_am_i/where_am_i__useruser01__18to39__male__not_specified__20250809T053449_processed.mp4")
    if processed_path.exists():
        processed_path.unlink()
        print(f"🗑️ Removed existing processed video")
    
    # Process the video
    preprocessor = EnhancedVideoPreprocessor(".", phrase_set="26")
    
    # Find phrase index for "Where am I?"
    phrase_idx = 0  # First phrase
    
    try:
        result = preprocessor.process_single_video(Path(video_path), phrase_idx)
        
        if result and processed_path.exists():
            print(f"✅ Processing successful!")
            
            # Analyze processed video
            cap = cv2.VideoCapture(str(processed_path))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            print(f"📹 Processed video: {width}x{height}, {fps} FPS, {frames} frames")
            
            # Extract sample frames to check complete lip visibility
            sample_frames = []
            for frame_idx in [0, 15, 30, 45, 60, 74]:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if ret:
                    sample_frames.append((frame_idx, frame))
            
            cap.release()
            
            # Create validation grid
            if len(sample_frames) >= 6:
                scaled_frames = []
                for frame_idx, frame in sample_frames:
                    # Scale up for visibility (140x46 -> 560x184)
                    scaled = cv2.resize(frame, (560, 184), interpolation=cv2.INTER_NEAREST)
                    cv2.putText(scaled, f"Frame {frame_idx}", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
                    cv2.putText(scaled, "FINE-TUNED: Complete Lip Capture", (10, 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    scaled_frames.append(scaled)
                
                # Create 2x3 grid
                top_row = np.hstack([scaled_frames[0], scaled_frames[1], scaled_frames[2]])
                bottom_row = np.hstack([scaled_frames[3], scaled_frames[4], scaled_frames[5]])
                grid = np.vstack([top_row, bottom_row])
                
                # Add title
                title_height = 50
                title_img = np.zeros((title_height, grid.shape[1], 3), dtype=np.uint8)
                cv2.putText(title_img, "FINE-TUNED ALGORITHM - COMPLETE LIP CAPTURE VALIDATION", 
                           (20, 35), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
                
                final_grid = np.vstack([title_img, grid])
                cv2.imwrite("fine_tuned_validation_grid.jpg", final_grid)
                print(f"💾 Saved fine_tuned_validation_grid.jpg")
            
            return True
        else:
            print(f"❌ Processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Processing error: {e}")
        return False

def main():
    video_path = "/Users/<USER>/Desktop/icu-videos-today/where_am_i__useruser01__18to39__male__not_specified__20250809T053449.webm"
    
    if not Path(video_path).exists():
        print(f"❌ Video file not found: {video_path}")
        return
    
    print("🔧 FINE-TUNED MOUTH DETECTION ALGORITHM TEST")
    print("=" * 60)
    
    # Test fine-tuned coordinates
    coords = test_fine_tuned_coordinates(video_path)
    
    # Test complete processing pipeline
    success = test_processing_pipeline(video_path)
    
    print(f"\n📋 FINE-TUNING TEST RESULTS:")
    print(f"Coordinates test: {'✅ PASSED' if coords else '❌ FAILED'}")
    print(f"Processing test: {'✅ PASSED' if success else '❌ FAILED'}")
    
    if coords and success:
        print(f"\n🎉 FINE-TUNING SUCCESSFUL!")
        print(f"✅ Algorithm now captures complete upper lip")
        print(f"✅ Ready for full dataset reprocessing")
        print(f"\n📁 Generated files:")
        print(f"- fine_tuned_comparison.jpg: Shows coordinate adjustment")
        print(f"- crop_comparison.jpg: Shows before/after lip capture")
        print(f"- fine_tuned_validation_grid.jpg: Shows processed output")
    else:
        print(f"\n❌ FINE-TUNING NEEDS ADJUSTMENT")
        print(f"❌ Further coordinate refinement required")

if __name__ == "__main__":
    main()
