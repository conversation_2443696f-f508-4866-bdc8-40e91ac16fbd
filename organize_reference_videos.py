#!/usr/bin/env python3
"""
Organize reference videos and verify phrase coverage
"""

import shutil
from pathlib import Path
import pandas as pd
from collections import defaultdict

def organize_reference_videos():
    """Move and organize reference videos"""
    
    print("📁 Organizing Reference Videos")
    print("=" * 40)
    
    # Define directories
    source_dir = Path("mouth_cropped_videos")
    target_dir = Path("/Users/<USER>/Desktop/reference videos for training")
    
    # Create target directory
    target_dir.mkdir(exist_ok=True)
    
    if not source_dir.exists():
        print(f"❌ Source directory not found: {source_dir}")
        return False
    
    # Get all cropped videos
    cropped_videos = list(source_dir.glob("*_mouth_cropped.webm"))
    
    if not cropped_videos:
        print(f"❌ No cropped videos found in {source_dir}")
        return False
    
    print(f"📊 Found {len(cropped_videos)} mouth-cropped reference videos")
    
    # Define the 26 ICU phrases
    icu_phrases = [
        "where am i",
        "who is with me today",
        "what happened to me", 
        "am i getting better",
        "please explain again",
        "where is my wife",
        "where is my husband",
        "i want to phone my family",
        "i want to see my wife",
        "i want to see my husband",
        "what time is my wife coming",
        "what time is my husband coming",
        "i feel anxious",
        "stay with me please",
        "my chest hurts",
        "my back hurts",
        "i m confused",
        "i m in pain",
        "i have a headache",
        "i m uncomfortable",
        "i need a medication",
        "i need to lie down",
        "i need to use the toilet",
        "i need to sit up",
        "i need help",
        "i need to move"
    ]
    
    # Track phrase coverage
    phrase_counts = defaultdict(int)
    moved_videos = []
    
    # Move videos and track phrases
    for video_path in cropped_videos:
        # Extract phrase from filename
        filename = video_path.stem.replace("_mouth_cropped", "")
        phrase_part = filename.split("__")[0]
        phrase = phrase_part.replace("_", " ")
        
        # Check if it's a valid ICU phrase
        if phrase in icu_phrases:
            phrase_counts[phrase] += 1
            
            # Move video to target directory
            target_path = target_dir / video_path.name
            
            try:
                shutil.copy2(video_path, target_path)
                moved_videos.append({
                    'original_path': str(video_path),
                    'new_path': str(target_path),
                    'phrase': phrase,
                    'filename': video_path.name
                })
                print(f"✅ Moved: {phrase.title()} → {video_path.name}")
            except Exception as e:
                print(f"❌ Failed to move {video_path.name}: {e}")
        else:
            print(f"⚠️  Unknown phrase: {phrase} in {video_path.name}")
    
    print(f"\n📊 Reference Video Organization Summary:")
    print(f"   Total videos moved: {len(moved_videos)}")
    print(f"   Target directory: {target_dir}")
    
    # Verify phrase coverage
    print(f"\n📋 Phrase Coverage Analysis:")
    print("-" * 30)
    
    covered_phrases = set(phrase_counts.keys())
    missing_phrases = set(icu_phrases) - covered_phrases
    
    print(f"Covered phrases: {len(covered_phrases)}/26")
    print(f"Missing phrases: {len(missing_phrases)}")
    
    if covered_phrases:
        print(f"\n✅ Phrases with reference videos:")
        for phrase in sorted(covered_phrases):
            count = phrase_counts[phrase]
            print(f"   {phrase.title()}: {count} video{'s' if count != 1 else ''}")
    
    if missing_phrases:
        print(f"\n❌ Missing phrases (no reference videos):")
        for phrase in sorted(missing_phrases):
            print(f"   {phrase.title()}")
    
    # Create reference manifest
    create_reference_manifest(moved_videos, target_dir)
    
    return len(moved_videos) > 0

def create_reference_manifest(moved_videos, target_dir):
    """Create manifest for reference videos"""
    
    print(f"\n📝 Creating Reference Manifest")
    print("-" * 30)
    
    manifest_data = []
    
    for video_info in moved_videos:
        video_path = Path(video_info['new_path'])
        
        # Extract metadata from filename
        filename = video_path.stem.replace("_mouth_cropped", "")
        parts = filename.split("__")
        
        if len(parts) >= 5:
            phrase_part = parts[0]
            speaker_id = parts[1]
            age_group = parts[2]
            gender = parts[3]
            ethnicity = parts[4]
            timestamp = parts[5] if len(parts) > 5 else "unknown"
        else:
            # Fallback parsing
            phrase_part = parts[0] if len(parts) > 0 else "unknown"
            speaker_id = parts[1] if len(parts) > 1 else "unknown"
            age_group = "unknown"
            gender = "unknown"
            ethnicity = "not_specified"
            timestamp = "unknown"
        
        phrase = phrase_part.replace("_", " ")
        
        # Get file info
        file_size = video_path.stat().st_size
        
        manifest_data.append({
            'video_path': str(video_path.relative_to(target_dir.parent)),
            'phrase': phrase,
            'speaker_id': speaker_id,
            'age_group': age_group,
            'gender': gender,
            'ethnicity': ethnicity,
            'timestamp': timestamp,
            'file_size_kb': file_size / 1024,
            'video_type': 'reference',
            'processing_status': 'mouth_cropped'
        })
    
    # Save manifest
    manifest_df = pd.DataFrame(manifest_data)
    manifest_path = target_dir / "reference_videos_manifest.csv"
    manifest_df.to_csv(manifest_path, index=False)
    
    print(f"💾 Reference manifest saved: {manifest_path}")
    print(f"📊 Manifest contains {len(manifest_data)} reference videos")
    
    # Show summary statistics
    print(f"\n📈 Reference Video Statistics:")
    print(f"   Unique phrases: {manifest_df['phrase'].nunique()}")
    print(f"   Unique speakers: {manifest_df['speaker_id'].nunique()}")
    print(f"   Total file size: {manifest_df['file_size_kb'].sum():.1f} KB")
    print(f"   Average file size: {manifest_df['file_size_kb'].mean():.1f} KB")

def verify_organization():
    """Verify the organization was successful"""
    
    print(f"\n🔍 Verifying Organization")
    print("=" * 25)
    
    target_dir = Path("/Users/<USER>/Desktop/reference videos for training")
    
    if not target_dir.exists():
        print(f"❌ Target directory not found: {target_dir}")
        return False
    
    # Count videos in target directory
    reference_videos = list(target_dir.glob("*_mouth_cropped.webm"))
    
    print(f"📊 Verification Results:")
    print(f"   Target directory: {target_dir}")
    print(f"   Reference videos found: {len(reference_videos)}")
    
    if len(reference_videos) > 0:
        print(f"   ✅ Organization successful!")
        
        # Check manifest
        manifest_path = target_dir / "reference_videos_manifest.csv"
        if manifest_path.exists():
            print(f"   ✅ Reference manifest created: {manifest_path}")
        else:
            print(f"   ⚠️  Reference manifest not found")
        
        return True
    else:
        print(f"   ❌ No reference videos found in target directory")
        return False

def main():
    """Main organization function"""
    
    print("📁 Reference Video Organization System")
    print("=" * 50)
    
    # Organize reference videos
    success = organize_reference_videos()
    
    if success:
        # Verify organization
        verify_organization()
        
        print(f"\n🎯 Reference Video Organization Complete!")
        print(f"✅ Videos moved to organized directory structure")
        print(f"✅ Phrase coverage verified")
        print(f"✅ Reference manifest created")
        print(f"✅ Ready for training pipeline integration")
    else:
        print(f"\n❌ Reference video organization failed")

if __name__ == '__main__':
    main()
