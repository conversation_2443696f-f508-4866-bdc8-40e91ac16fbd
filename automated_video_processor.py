#!/usr/bin/env python3
"""
Automated Video Filtering and Organization System
Processes additional ICU lipreading training videos with mouth-focused cropping
"""

import os
import re
import cv2
import subprocess
import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from collections import defaultdict
import shutil
from tqdm import tqdm
import sys

# Add current directory to path
sys.path.append('.')

from mouth_cropping_system import MouthCroppingSystem

class AutomatedVideoProcessor:
    """Automated system for processing ICU lipreading training videos"""
    
    def __init__(self, source_dir: str, target_dir: str):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        
        # Create target directory
        self.target_dir.mkdir(exist_ok=True)
        
        # Initialize mouth cropping system with same parameters as reference videos
        self.cropper = MouthCroppingSystem(crop_region=(133, 0, 133, 100))
        
        # Define the 26 ICU phrases
        self.icu_phrases = [
            "where am i",
            "who is with me today",
            "what happened to me", 
            "am i getting better",
            "please explain again",
            "where is my wife",
            "where is my husband",
            "i want to phone my family",
            "i want to see my wife",
            "i want to see my husband",
            "what time is my wife coming",
            "what time is my husband coming",
            "i feel anxious",
            "stay with me please",
            "my chest hurts",
            "my back hurts",
            "i m confused",
            "i m in pain",
            "i have a headache",
            "i m uncomfortable",
            "i need a medication",
            "i need to lie down",
            "i need to use the toilet",
            "i need to sit up",
            "i need help",
            "i need to move"
        ]
        
        # Supported video formats
        self.video_extensions = {'.webm', '.mp4', '.avi', '.mov', '.mkv'}
        
        # Processing statistics
        self.stats = {
            'discovered': 0,
            'parsed': 0,
            'filtered': 0,
            'processed': 0,
            'failed': 0,
            'phrase_distribution': defaultdict(int),
            'speaker_distribution': defaultdict(int),
            'errors': []
        }
    
    def discover_videos(self) -> List[Path]:
        """Recursively discover all video files in source directory"""
        
        print(f"🔍 Discovering Videos in Source Directory")
        print("=" * 45)
        print(f"📁 Source: {self.source_dir}")
        
        if not self.source_dir.exists():
            print(f"❌ Source directory not found: {self.source_dir}")
            return []
        
        video_files = []
        
        # Recursively find all video files
        for ext in self.video_extensions:
            pattern = f"**/*{ext}"
            found_files = list(self.source_dir.glob(pattern))
            video_files.extend(found_files)
        
        self.stats['discovered'] = len(video_files)
        
        print(f"📊 Discovery Results:")
        print(f"   Total video files found: {len(video_files)}")
        
        # Show breakdown by extension
        ext_counts = defaultdict(int)
        for video_file in video_files:
            ext_counts[video_file.suffix.lower()] += 1
        
        for ext, count in sorted(ext_counts.items()):
            print(f"   {ext}: {count} files")
        
        return video_files
    
    def parse_video_metadata(self, video_path: Path) -> Optional[Dict]:
        """Extract metadata from video filename"""
        
        filename = video_path.stem
        
        # Try different parsing patterns
        patterns = [
            # Pattern 1: phrase__speaker__age__gender__ethnicity__timestamp
            r'^(.+?)__(.+?)__(.+?)__(.+?)__(.+?)__(.+?)$',
            # Pattern 2: phrase__speaker__age__gender__ethnicity
            r'^(.+?)__(.+?)__(.+?)__(.+?)__(.+?)$',
            # Pattern 3: phrase__speaker__metadata
            r'^(.+?)__(.+?)__(.+?)$',
            # Pattern 4: phrase__speaker
            r'^(.+?)__(.+?)$',
            # Pattern 5: just phrase
            r'^(.+?)$'
        ]
        
        for pattern in patterns:
            match = re.match(pattern, filename)
            if match:
                groups = match.groups()
                
                # Extract phrase and normalize
                phrase_part = groups[0] if len(groups) > 0 else "unknown"
                phrase = phrase_part.replace("_", " ").lower().strip()
                
                # Extract other metadata with defaults
                speaker_id = groups[1] if len(groups) > 1 else "unknown"
                age_group = groups[2] if len(groups) > 2 else "unknown"
                gender = groups[3] if len(groups) > 3 else "unknown"
                ethnicity = groups[4] if len(groups) > 4 else "not_specified"
                timestamp = groups[5] if len(groups) > 5 else "unknown"
                
                return {
                    'phrase': phrase,
                    'speaker_id': speaker_id,
                    'age_group': age_group,
                    'gender': gender,
                    'ethnicity': ethnicity,
                    'timestamp': timestamp,
                    'original_filename': filename,
                    'file_path': str(video_path)
                }
        
        # If no pattern matches, return basic info
        return {
            'phrase': filename.replace("_", " ").lower().strip(),
            'speaker_id': "unknown",
            'age_group': "unknown", 
            'gender': "unknown",
            'ethnicity': "not_specified",
            'timestamp': "unknown",
            'original_filename': filename,
            'file_path': str(video_path)
        }
    
    def filter_icu_phrases(self, video_metadata: List[Dict]) -> List[Dict]:
        """Filter videos to only include ICU phrases"""
        
        print(f"\n🔍 Filtering for ICU Phrases")
        print("=" * 30)
        
        filtered_videos = []
        phrase_matches = defaultdict(int)
        unmatched_phrases = set()
        
        for metadata in video_metadata:
            phrase = metadata['phrase']
            
            # Check if phrase matches any ICU phrase (case-insensitive)
            matched = False
            for icu_phrase in self.icu_phrases:
                if phrase == icu_phrase:
                    filtered_videos.append(metadata)
                    phrase_matches[icu_phrase] += 1
                    matched = True
                    break
            
            if not matched:
                unmatched_phrases.add(phrase)
        
        self.stats['filtered'] = len(filtered_videos)
        
        print(f"📊 Filtering Results:")
        print(f"   Videos matching ICU phrases: {len(filtered_videos)}")
        print(f"   Videos with unmatched phrases: {len(video_metadata) - len(filtered_videos)}")
        
        # Show phrase distribution
        if phrase_matches:
            print(f"\n📋 ICU Phrase Distribution:")
            for phrase in sorted(self.icu_phrases):
                count = phrase_matches[phrase]
                if count > 0:
                    print(f"   {phrase.title()}: {count} videos")
        
        # Show some unmatched phrases (for debugging)
        if unmatched_phrases:
            print(f"\n⚠️  Sample Unmatched Phrases:")
            for phrase in sorted(list(unmatched_phrases)[:10]):
                print(f"   '{phrase}'")
            if len(unmatched_phrases) > 10:
                print(f"   ... and {len(unmatched_phrases) - 10} more")
        
        return filtered_videos
    
    def get_video_properties(self, video_path: Path) -> Optional[Dict]:
        """Get technical properties of video file"""
        
        try:
            cap = cv2.VideoCapture(str(video_path))
            
            if not cap.isOpened():
                return None
            
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            cap.release()
            
            file_size = video_path.stat().st_size
            duration = frame_count / fps if fps > 0 else 0
            
            return {
                'width': width,
                'height': height,
                'fps': fps,
                'frame_count': frame_count,
                'duration': duration,
                'file_size': file_size
            }
            
        except Exception as e:
            return None
    
    def process_videos(self, filtered_videos: List[Dict]) -> List[Dict]:
        """Process videos with mouth-focused cropping"""
        
        print(f"\n🎬 Processing Videos with Mouth-Focused Cropping")
        print("=" * 50)
        print(f"📊 Videos to process: {len(filtered_videos)}")
        print(f"📁 Target directory: {self.target_dir}")
        
        processed_videos = []
        
        # Process videos with progress bar
        for video_metadata in tqdm(filtered_videos, desc="Processing videos"):
            try:
                video_path = Path(video_metadata['file_path'])
                
                # Get original video properties
                original_props = self.get_video_properties(video_path)
                if not original_props:
                    self.stats['failed'] += 1
                    self.stats['errors'].append(f"Could not read properties: {video_path.name}")
                    continue
                
                # Generate output filename
                phrase_part = video_metadata['phrase'].replace(" ", "_")
                speaker_id = video_metadata['speaker_id']
                age_group = video_metadata['age_group']
                gender = video_metadata['gender']
                ethnicity = video_metadata['ethnicity']
                timestamp = video_metadata['timestamp']
                
                output_name = f"{phrase_part}__{speaker_id}__{age_group}__{gender}__{ethnicity}__{timestamp}_mouth_cropped.webm"
                output_path = self.target_dir / output_name
                
                # Apply mouth cropping
                crop_result = self.cropper.crop_single_video(
                    str(video_path), 
                    str(output_path),
                    preserve_quality=True
                )
                
                if crop_result['success']:
                    # Get processed video properties
                    processed_props = self.get_video_properties(output_path)
                    
                    # Create complete metadata record
                    processed_metadata = {
                        **video_metadata,
                        'processed_path': str(output_path),
                        'original_width': original_props['width'],
                        'original_height': original_props['height'],
                        'original_file_size': original_props['file_size'],
                        'processed_width': processed_props['width'] if processed_props else 0,
                        'processed_height': processed_props['height'] if processed_props else 0,
                        'processed_file_size': processed_props['file_size'] if processed_props else 0,
                        'size_reduction': original_props['file_size'] / processed_props['file_size'] if processed_props and processed_props['file_size'] > 0 else 0,
                        'processing_status': 'success',
                        'quality_metrics': crop_result.get('quality_metrics', {})
                    }
                    
                    processed_videos.append(processed_metadata)
                    self.stats['processed'] += 1
                    self.stats['phrase_distribution'][video_metadata['phrase']] += 1
                    self.stats['speaker_distribution'][video_metadata['speaker_id']] += 1
                    
                else:
                    self.stats['failed'] += 1
                    self.stats['errors'].append(f"Cropping failed: {video_path.name} - {crop_result.get('error', 'Unknown error')}")
                
            except Exception as e:
                self.stats['failed'] += 1
                self.stats['errors'].append(f"Processing exception: {video_path.name} - {str(e)}")
        
        print(f"\n📊 Processing Results:")
        print(f"   Successfully processed: {self.stats['processed']}")
        print(f"   Failed: {self.stats['failed']}")
        print(f"   Success rate: {(self.stats['processed'] / len(filtered_videos) * 100):.1f}%")
        
        return processed_videos

    def generate_training_manifest(self, processed_videos: List[Dict]) -> str:
        """Generate comprehensive training manifest CSV"""

        print(f"\n📝 Generating Training Manifest")
        print("=" * 30)

        manifest_data = []

        for video_metadata in processed_videos:
            # Get additional video properties
            processed_path = Path(video_metadata['processed_path'])

            if processed_path.exists():
                # Calculate relative path from target directory parent
                relative_path = processed_path.relative_to(self.target_dir.parent)

                manifest_data.append({
                    'video_path': str(relative_path),
                    'phrase': video_metadata['phrase'],
                    'speaker_id': video_metadata['speaker_id'],
                    'age_group': video_metadata['age_group'],
                    'gender': video_metadata['gender'],
                    'ethnicity': video_metadata['ethnicity'],
                    'file_size_kb': video_metadata.get('processed_file_size', 0) / 1024,
                    'duration_seconds': video_metadata.get('quality_metrics', {}).get('duration', 0),
                    'frame_count': video_metadata.get('quality_metrics', {}).get('frame_count', 0),
                    'processing_timestamp': pd.Timestamp.now().isoformat(),
                    'original_file_size_kb': video_metadata.get('original_file_size', 0) / 1024,
                    'size_reduction_factor': video_metadata.get('size_reduction', 0),
                    'motion_score': video_metadata.get('quality_metrics', {}).get('avg_motion_score', 0),
                    'video_type': 'training',
                    'processing_status': 'mouth_cropped'
                })

        # Create DataFrame and save
        manifest_df = pd.DataFrame(manifest_data)
        manifest_path = self.target_dir / "processed_training_manifest.csv"
        manifest_df.to_csv(manifest_path, index=False)

        print(f"💾 Training manifest saved: {manifest_path}")
        print(f"📊 Manifest contains {len(manifest_data)} processed videos")

        return str(manifest_path)

    def generate_processing_report(self, processed_videos: List[Dict], manifest_path: str) -> str:
        """Generate comprehensive processing report"""

        print(f"\n📊 Generating Processing Report")
        print("=" * 30)

        # Calculate statistics
        total_original_size = sum(v.get('original_file_size', 0) for v in processed_videos)
        total_processed_size = sum(v.get('processed_file_size', 0) for v in processed_videos)
        avg_size_reduction = sum(v.get('size_reduction', 0) for v in processed_videos) / len(processed_videos) if processed_videos else 0

        # Create report
        report = {
            'processing_summary': {
                'total_videos_discovered': self.stats['discovered'],
                'total_videos_parsed': self.stats['parsed'],
                'total_videos_filtered': self.stats['filtered'],
                'total_videos_processed': self.stats['processed'],
                'total_videos_failed': self.stats['failed'],
                'success_rate': (self.stats['processed'] / self.stats['filtered'] * 100) if self.stats['filtered'] > 0 else 0
            },
            'phrase_distribution': dict(self.stats['phrase_distribution']),
            'speaker_distribution': dict(self.stats['speaker_distribution']),
            'file_size_metrics': {
                'total_original_size_mb': total_original_size / (1024 * 1024),
                'total_processed_size_mb': total_processed_size / (1024 * 1024),
                'average_size_reduction_factor': avg_size_reduction,
                'total_space_saved_mb': (total_original_size - total_processed_size) / (1024 * 1024)
            },
            'quality_metrics': {
                'videos_with_motion_detected': sum(1 for v in processed_videos if v.get('quality_metrics', {}).get('motion_detected', False)),
                'average_motion_score': sum(v.get('quality_metrics', {}).get('avg_motion_score', 0) for v in processed_videos) / len(processed_videos) if processed_videos else 0
            },
            'processing_errors': self.stats['errors'][:20],  # First 20 errors
            'manifest_path': manifest_path,
            'target_directory': str(self.target_dir),
            'processing_timestamp': pd.Timestamp.now().isoformat()
        }

        # Save report
        report_path = self.target_dir / "processing_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        print(f"💾 Processing report saved: {report_path}")

        # Print summary
        print(f"\n📋 Processing Summary:")
        print(f"   Videos discovered: {report['processing_summary']['total_videos_discovered']}")
        print(f"   Videos processed: {report['processing_summary']['total_videos_processed']}")
        print(f"   Success rate: {report['processing_summary']['success_rate']:.1f}%")
        print(f"   Unique phrases: {len(report['phrase_distribution'])}")
        print(f"   Unique speakers: {len(report['speaker_distribution'])}")
        print(f"   Average size reduction: {report['file_size_metrics']['average_size_reduction_factor']:.1f}x")
        print(f"   Total space saved: {report['file_size_metrics']['total_space_saved_mb']:.1f} MB")

        return str(report_path)

def main():
    """Main processing function"""
    
    print("🎬 Automated Video Filtering and Organization System")
    print("=" * 60)
    
    # Define directories
    source_dir = "/Users/<USER>/Desktop/icu-videos-for-training 14.8.25"
    target_dir = "/Users/<USER>/Desktop/processed videos for training"
    
    # Initialize processor
    processor = AutomatedVideoProcessor(source_dir, target_dir)
    
    # Step 1: Discover videos
    video_files = processor.discover_videos()
    
    if not video_files:
        print("❌ No video files found. Exiting.")
        return
    
    # Step 2: Parse metadata
    print(f"\n📝 Parsing Video Metadata")
    print("=" * 25)
    
    video_metadata = []
    for video_file in tqdm(video_files, desc="Parsing metadata"):
        metadata = processor.parse_video_metadata(video_file)
        if metadata:
            video_metadata.append(metadata)
    
    processor.stats['parsed'] = len(video_metadata)
    print(f"✅ Parsed metadata for {len(video_metadata)} videos")
    
    # Step 3: Filter for ICU phrases
    filtered_videos = processor.filter_icu_phrases(video_metadata)
    
    if not filtered_videos:
        print("❌ No videos match ICU phrases. Exiting.")
        return
    
    # Step 4: Process videos
    processed_videos = processor.process_videos(filtered_videos)
    
    # Step 5: Generate manifest
    if processed_videos:
        manifest_path = processor.generate_training_manifest(processed_videos)

        # Step 6: Generate report
        report_path = processor.generate_processing_report(processed_videos, manifest_path)

        print(f"\n🎯 Processing Complete!")
        print(f"✅ {len(processed_videos)} videos successfully processed")
        print(f"📁 Output directory: {target_dir}")
        print(f"📝 Manifest: {manifest_path}")
        print(f"📊 Report: {report_path}")
    else:
        print(f"\n❌ No videos were successfully processed")

if __name__ == '__main__':
    main()
