#!/usr/bin/env python3
"""
Preprocessing Verification Tool for Perfect 10 ICU Lipreading
Visualizes the preprocessing pipeline step-by-step to verify correct operation
"""

import cv2
import numpy as np
import torch
from pathlib import Path
import sys
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import os

# Add current directory to path
sys.path.append('.')

from backend.lightweight_vsr.utils_video import VideoProcessor

class PreprocessingVerifier:
    """Tool to verify and visualize the preprocessing pipeline"""
    
    def __init__(self, videos_folder: str):
        """Initialize the verifier"""
        
        self.videos_folder = Path(videos_folder)
        self.video_processor = VideoProcessor(
            target_frames=32,
            target_size=(96, 96),
            grayscale=True
        )
        
        print(f"🔍 Preprocessing Verification Tool")
        print(f"   Videos folder: {self.videos_folder}")
        print(f"   Target: 32 frames, 96×96, grayscale")
    
    def get_video_info(self, video_path: str) -> dict:
        """Get basic information about a video file"""
        
        cap = cv2.VideoCapture(video_path)
        
        info = {
            'path': video_path,
            'name': Path(video_path).name,
            'exists': Path(video_path).exists(),
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'duration': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0
        }
        
        cap.release()
        return info
    
    def extract_sample_frames(self, video_path: str, num_frames: int = 5) -> list:
        """Extract sample frames from original video"""
        
        cap = cv2.VideoCapture(video_path)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Get evenly spaced frame indices
        frame_indices = np.linspace(0, total_frames-1, num_frames, dtype=int)
        
        frames = []
        for idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
            ret, frame = cap.read()
            if ret:
                # Convert BGR to RGB for matplotlib
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frames.append(frame_rgb)
        
        cap.release()
        return frames
    
    def show_preprocessing_steps(self, video_path: str):
        """Show step-by-step preprocessing visualization"""
        
        print(f"\n🎬 Preprocessing Verification: {Path(video_path).name}")
        print("=" * 50)
        
        # Get video info
        info = self.get_video_info(video_path)
        print(f"📊 Original Video Info:")
        print(f"   Resolution: {info['width']}×{info['height']}")
        print(f"   Frame count: {info['frame_count']}")
        print(f"   FPS: {info['fps']:.1f}")
        print(f"   Duration: {info['duration']:.1f}s")
        
        # Extract sample frames from original
        print(f"\n🔄 Extracting sample frames...")
        original_frames = self.extract_sample_frames(video_path, 5)
        
        # Process through pipeline
        print(f"🔄 Processing through Perfect 10 pipeline...")
        try:
            processed_tensor = self.video_processor.process_video(video_path)
            print(f"✅ Processing successful")
            print(f"   Output shape: {processed_tensor.shape}")
            print(f"   Value range: [{processed_tensor.min():.3f}, {processed_tensor.max():.3f}]")
            
            # Convert tensor back to numpy for visualization
            processed_frames = processed_tensor.squeeze(0).numpy()  # Remove channel dim
            
            # Create visualization
            self.create_preprocessing_visualization(
                original_frames, processed_frames, info, video_path
            )
            
            return True
            
        except Exception as e:
            print(f"❌ Processing failed: {e}")
            return False
    
    def create_preprocessing_visualization(self, original_frames, processed_frames, info, video_path):
        """Create a comprehensive visualization of preprocessing steps"""
        
        fig = plt.figure(figsize=(20, 12))
        gs = GridSpec(3, 6, figure=fig, hspace=0.3, wspace=0.3)
        
        video_name = Path(video_path).name
        fig.suptitle(f'Preprocessing Verification: {video_name}', fontsize=16, fontweight='bold')
        
        # Original frames row
        for i in range(5):
            ax = fig.add_subplot(gs[0, i])
            if i < len(original_frames):
                ax.imshow(original_frames[i])
                ax.set_title(f'Original Frame {i+1}\n{info["width"]}×{info["height"]}', fontsize=10)
            ax.axis('off')
        
        # Info panel
        ax_info = fig.add_subplot(gs[0, 5])
        ax_info.text(0.1, 0.9, 'Original Video Info:', fontweight='bold', fontsize=12)
        ax_info.text(0.1, 0.8, f'Resolution: {info["width"]}×{info["height"]}', fontsize=10)
        ax_info.text(0.1, 0.7, f'Frames: {info["frame_count"]}', fontsize=10)
        ax_info.text(0.1, 0.6, f'FPS: {info["fps"]:.1f}', fontsize=10)
        ax_info.text(0.1, 0.5, f'Duration: {info["duration"]:.1f}s', fontsize=10)
        ax_info.text(0.1, 0.3, 'Processing Steps:', fontweight='bold', fontsize=12)
        ax_info.text(0.1, 0.2, '1. Mouth cropping', fontsize=10)
        ax_info.text(0.1, 0.1, '2. Resize to 96×96', fontsize=10)
        ax_info.text(0.1, 0.0, '3. Grayscale conversion', fontsize=10)
        ax_info.set_xlim(0, 1)
        ax_info.set_ylim(0, 1)
        ax_info.axis('off')
        
        # Processed frames row (sample from 32 frames)
        frame_indices = np.linspace(0, processed_frames.shape[0]-1, 5, dtype=int)
        for i, frame_idx in enumerate(frame_indices):
            ax = fig.add_subplot(gs[1, i])
            frame = processed_frames[frame_idx]
            ax.imshow(frame, cmap='gray', vmin=0, vmax=1)
            ax.set_title(f'Processed Frame {frame_idx+1}\n96×96 Grayscale', fontsize=10)
            ax.axis('off')
        
        # Processing info panel
        ax_proc = fig.add_subplot(gs[1, 5])
        ax_proc.text(0.1, 0.9, 'Processed Video Info:', fontweight='bold', fontsize=12)
        ax_proc.text(0.1, 0.8, f'Shape: {processed_frames.shape}', fontsize=10)
        ax_proc.text(0.1, 0.7, f'Frames: {processed_frames.shape[0]}', fontsize=10)
        ax_proc.text(0.1, 0.6, f'Resolution: 96×96', fontsize=10)
        ax_proc.text(0.1, 0.5, f'Channels: Grayscale', fontsize=10)
        ax_proc.text(0.1, 0.4, f'Value range: [{processed_frames.min():.3f}, {processed_frames.max():.3f}]', fontsize=10)
        ax_proc.text(0.1, 0.2, 'Pipeline Status:', fontweight='bold', fontsize=12)
        ax_proc.text(0.1, 0.1, '✅ Mouth cropping applied', fontsize=10, color='green')
        ax_proc.text(0.1, 0.0, '✅ Resize to 96×96 applied', fontsize=10, color='green')
        ax_proc.set_xlim(0, 1)
        ax_proc.set_ylim(0, 1)
        ax_proc.axis('off')
        
        # Comparison visualization
        ax_comp = fig.add_subplot(gs[2, :3])
        
        # Show original vs processed side by side
        if len(original_frames) > 0:
            # Resize original frame for comparison
            orig_resized = cv2.resize(original_frames[2], (96, 96))
            orig_gray = cv2.cvtColor(orig_resized, cv2.COLOR_RGB2GRAY)
            
            comparison = np.hstack([orig_gray, processed_frames[16]])  # Middle frames
            ax_comp.imshow(comparison, cmap='gray')
            ax_comp.set_title('Comparison: Original (resized) vs Processed', fontsize=12)
            ax_comp.axvline(x=96, color='red', linestyle='--', linewidth=2)
            ax_comp.text(48, 10, 'Original\n(resized)', ha='center', color='white', fontweight='bold')
            ax_comp.text(144, 10, 'Processed\n(mouth-cropped)', ha='center', color='white', fontweight='bold')
        ax_comp.axis('off')
        
        # Value distribution
        ax_hist = fig.add_subplot(gs[2, 3:])
        ax_hist.hist(processed_frames.flatten(), bins=50, alpha=0.7, color='blue', edgecolor='black')
        ax_hist.set_title('Processed Frame Value Distribution', fontsize=12)
        ax_hist.set_xlabel('Pixel Values')
        ax_hist.set_ylabel('Frequency')
        ax_hist.grid(True, alpha=0.3)
        
        # Save visualization
        output_path = f"preprocessing_verification_{Path(video_path).stem}.png"
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"💾 Visualization saved: {output_path}")
        
        # Show the plot
        plt.show()
    
    def verify_all_videos(self):
        """Verify preprocessing for all numbered videos"""
        
        print(f"\n🔍 Verifying All New Test Videos")
        print("=" * 40)
        
        video_paths = []
        for i in range(1, 6):
            video_path = self.videos_folder / f"{i}.webm"
            if video_path.exists():
                video_paths.append(str(video_path))
                print(f"   ✅ Found: {i}.webm")
            else:
                print(f"   ❌ Missing: {i}.webm")
        
        if not video_paths:
            print(f"❌ No videos found")
            return
        
        # Show summary info for all videos
        print(f"\n📊 Video Summary:")
        for video_path in video_paths:
            info = self.get_video_info(video_path)
            print(f"   {info['name']}: {info['width']}×{info['height']}, {info['frame_count']} frames, {info['duration']:.1f}s")
        
        # Process each video
        for video_path in video_paths:
            success = self.show_preprocessing_steps(video_path)
            if not success:
                print(f"⚠️  Skipping visualization for {Path(video_path).name}")
    
    def open_original_videos(self):
        """Open original videos for manual inspection"""
        
        print(f"\n📹 Opening Original Videos for Inspection")
        print("=" * 45)
        
        video_paths = []
        for i in range(1, 6):
            video_path = self.videos_folder / f"{i}.webm"
            if video_path.exists():
                video_paths.append(str(video_path))
        
        if not video_paths:
            print(f"❌ No videos found")
            return
        
        print(f"🎬 Video file paths:")
        for video_path in video_paths:
            print(f"   📄 {video_path}")
        
        # Try to open first video as example
        if video_paths:
            first_video = video_paths[0]
            print(f"\n🎯 Opening first video for inspection: {Path(first_video).name}")
            try:
                os.system(f'open "{first_video}"')
                print(f"✅ Video opened in default player")
            except Exception as e:
                print(f"⚠️  Could not auto-open video: {e}")
                print(f"📁 Please manually open: {first_video}")

def main():
    """Main verification function"""
    
    print("🔍 Perfect 10 Preprocessing Verification Tool")
    print("=" * 50)
    
    # Video folder path
    videos_folder = "/Users/<USER>/Desktop/new videos 14.8.25 to test the model on"
    
    # Initialize verifier
    verifier = PreprocessingVerifier(videos_folder)
    
    # Show original video paths
    verifier.open_original_videos()
    
    # Verify preprocessing pipeline
    verifier.verify_all_videos()
    
    print(f"\n🎉 Preprocessing Verification Complete!")
    print("=" * 40)
    print("✅ Original videos located and displayed")
    print("✅ Preprocessing pipeline verified")
    print("✅ Visualizations saved as PNG files")

if __name__ == '__main__':
    main()
